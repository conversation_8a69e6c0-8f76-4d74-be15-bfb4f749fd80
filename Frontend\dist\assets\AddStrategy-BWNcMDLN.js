import{ed as De,j as e,aB as Ce,bc as we,r as l,ee as Pe,ef as Q,eg as Te,eh as Ee,ei as Ie,ej as Fe,b as ke,c as Ue,d as ee,F as te,e as ae,f as se,s as C,ek as Be,k as $,l as ie}from"./index-ctFdmWBt.js";/* empty css                       *//* empty css                   */import{S as ne}from"./SellerLayout-EbrVdrvL.js";import{S as R}from"./SummernoteEditor-CakKAUcC.js";import{i as re,g as qe,T as le,a as ce,U as Me,v as oe,F as de}from"./TimezoneErrorBoundary-BPlQ-kmB.js";import{t as E}from"./timezoneUtils-BuH33ask.js";import{g as Oe}from"./settingsService-Enovp4Qd.js";var $e=De();const Re=({isOpen:u,onClose:h,contentTitle:g})=>{if(!u)return null;const I=S=>{S.target===S.currentTarget&&h()};return e.jsx("div",{className:"content-submission-modal-overlay",onClick:I,children:e.jsxs("div",{className:"content-submission-modal",children:[e.jsx("div",{className:"content-submission-modal__header",children:e.jsx("button",{className:"content-submission-modal__close",onClick:h,"aria-label":"Close modal",children:e.jsx(Ce,{})})}),e.jsxs("div",{className:"content-submission-modal__content",children:[e.jsx("div",{className:"content-submission-modal__icon",children:e.jsx(we,{})}),e.jsx("h2",{className:"content-submission-modal__title",children:"Content Submitted Successfully!"}),e.jsxs("div",{className:"content-submission-modal__message",children:[e.jsxs("p",{children:["Your content ",e.jsxs("strong",{children:['"',g,'"']})," has been submitted and will be listed after admin approval."]}),e.jsx("p",{children:"You will receive a notification once your content has been reviewed and approved by our team."})]}),e.jsx("div",{className:"content-submission-modal__actions",children:e.jsx("button",{className:"btn-primary",onClick:h,children:"Got it!"})})]})]})})},ze=({contentId:u,onStatusChange:h})=>{const[g,I]=l.useState("pending"),[S,f]=l.useState(null),[t,m]=l.useState(!1),[j,v]=l.useState(!1);l.useEffect(()=>{if(!u)return;const N=async()=>{try{const _=await(await fetch(`${Pe}/content/${u}/preview-status`)).json();_.success&&(I(_.data.previewStatus),f(_.data.previewError),m(_.data.hasPreview),h&&h(_.data),["completed","failed","not_supported"].includes(_.data.previewStatus)&&v(!1))}catch(P){console.error("Error checking preview status:",P),v(!1)}};if(["pending","processing"].includes(g)){v(!0);const P=setInterval(N,3e3);return N(),()=>clearInterval(P)}},[u,g,h]);const y=()=>{switch(g){case"pending":return e.jsx(Q,{className:"preview-status__icon preview-status__icon--pending"});case"processing":return e.jsx(Fe,{className:"preview-status__icon preview-status__icon--processing"});case"completed":return e.jsx(Ie,{className:"preview-status__icon preview-status__icon--completed"});case"failed":return e.jsx(Ee,{className:"preview-status__icon preview-status__icon--failed"});case"not_supported":return e.jsx(Te,{className:"preview-status__icon preview-status__icon--not-supported"});default:return e.jsx(Q,{className:"preview-status__icon preview-status__icon--pending"})}},w=()=>{switch(g){case"pending":return"Preview generation queued...";case"processing":return"Generating preview...";case"completed":return t?"Preview generated successfully!":"Content created (no preview available)";case"failed":return`Preview generation failed${S?`: ${S}`:""}`;case"not_supported":return"Preview not supported for this content type";default:return"Checking preview status..."}},k=()=>`preview-status preview-status--${g}`;return u?e.jsxs("div",{className:k(),children:[e.jsxs("div",{className:"preview-status__content",children:[y(),e.jsx("span",{className:"preview-status__message",children:w()}),j&&e.jsx("div",{className:"preview-status__spinner",children:e.jsx("div",{className:"spinner"})})]}),g==="failed"&&S&&e.jsx("div",{className:"preview-status__error-details",children:e.jsxs("small",{children:["Error: ",S]})}),g==="completed"&&t&&e.jsx("div",{className:"preview-status__success-note",children:e.jsx("small",{children:"✨ Your content now has a preview that buyers can see before purchasing!"})})]}):null},b={TITLE:{maxChars:100},COACH_NAME:{maxChars:100}},ue=u=>u>=1024*1024*1024?`${(u/(1024*1024*1024)).toFixed(1)}GB`:u>=1024*1024?`${(u/(1024*1024)).toFixed(0)}MB`:u>=1024?`${(u/1024).toFixed(0)}KB`:`${u}B`,Xe=()=>{var X;const u=ke(),h=Ue(),{isLoading:g,isError:I,error:S}=ee(i=>i.content),{user:f}=ee(i=>i.auth),[t,m]=l.useState({title:"",category:"",coachName:"",description:"",fileUrl:"",aboutCoach:"",strategicContent:"",sport:"Basketball",contentType:"",thumbnailUrl:"",tags:[],difficulty:"",saleType:"",price:"",allowCustomRequests:!1,auctionDetails:{basePrice:"",auctionStartDate:"",auctionEndDate:"",minimumBidIncrement:"",allowOfferBeforeAuctionStart:!1},previewUrl:"",duration:"",videoLength:"",fileSize:"",prerequisites:[],learningObjectives:[],equipment:[],customRequestPrice:"",status:"Draft",visibility:"Public"}),[j,v]=l.useState(""),[y,w]=l.useState(null),[k,N]=l.useState(!1),[P,_]=l.useState(""),[me,F]=l.useState(""),[z,L]=l.useState(null),[pe,V]=l.useState(!1),[he,T]=l.useState(0),[G,U]=l.useState(""),[ge,B]=l.useState(!0),[r,x]=l.useState({}),[ye,H]=l.useState(!1),[p,Y]=l.useState(!1),[fe,_e]=l.useState(0),[Se,q]=l.useState(!1),[xe,J]=l.useState("");if(l.useEffect(()=>{(async()=>{try{const a=await Oe();a.data.success&&_e(a.data.data.platformCommission)}catch(a){console.error("Error fetching initial data:",a),C("Failed to load required data")}})()},[]),l.useEffect(()=>{f&&(()=>{var s;if(!((s=f==null?void 0:f.paymentInfo)==null?void 0:s.stripeConnectId)){C("Complete your payout setup to create content.",{autoClose:!1,closeOnClick:!1,closeButton:!0});const c=window.confirm(`⚠️ Payment Setup Required

You need to complete your payment setup before creating content. This ensures you can receive payments from your sales.

Would you like to go to Payment Settings now?`);return h(c?"/seller/payment-settings":"/seller/my-sports-strategies"),!1}return!0})()},[f,h]),f&&!((X=f==null?void 0:f.paymentInfo)!=null&&X.stripeConnectId))return e.jsx(ne,{children:e.jsx("div",{className:"payment-setup-required",children:e.jsxs("div",{className:"payment-setup-card",children:[e.jsx("div",{className:"payment-setup-icon",children:"🏦"}),e.jsx("h2",{children:"Payment Setup Required"}),e.jsx("p",{children:"You need to complete your payment setup before creating content. This ensures you can receive payments from your content sales."}),e.jsxs("div",{className:"payment-setup-benefits",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("span",{className:"benefit-icon",children:"✅"}),e.jsx("span",{children:"Secure payments powered by Stripe"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("span",{className:"benefit-icon",children:"✅"}),e.jsx("span",{children:"Automatic payouts to your bank account"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("span",{className:"benefit-icon",children:"✅"}),e.jsxs("span",{children:["You keep ",100-fe,"% of each sale"]})]})]}),e.jsxs("div",{className:"payment-setup-actions",children:[e.jsx("button",{className:"btn btn-primary",onClick:()=>h("/seller/payment-settings"),children:"Complete Payment Setup"}),e.jsx("button",{className:"btn btn-outline",onClick:()=>h("/seller/my-sports-strategies"),children:"Go Back"})]})]})})});const A=i=>{const{name:a,value:s,type:c,checked:d}=i.target,n=c==="checkbox"?d:s;if(a==="auctionDetails.auctionStartDate"||a==="auctionDetails.auctionEndDate")m(o=>({...o,auctionDetails:{...o.auctionDetails,[a.split(".")[1]]:s?E(new Date(s)).toISOString().slice(0,16):""}}));else if(m(o=>({...o,[a]:n})),a==="contentType"&&y&&!oe(y,n).isValid){w(null),m(Ae=>({...Ae,fileUrl:"",fileSize:""})),C(`Current file is not valid for ${n} content type. Please upload a new file.`);const Z=document.querySelector('input[type="file"]');Z&&(Z.value="")}c!=="checkbox"&&O(a,n)},D=i=>{const{name:a,value:s}=i.target;O(a,s)},M=(i,a)=>{m(s=>({...s,[i]:a})),O(i,a)},O=(i,a)=>{const s={...r};switch(i){case"title":{a.trim()?a.length>b.TITLE.maxChars?s.title=`Title cannot exceed ${b.TITLE.maxChars} characters`:delete s.title:s.title="Strategy title is required";break}case"category":{a?delete s.category:s.category="Please select a category";break}case"coachName":{a.trim()?a.length>b.COACH_NAME.maxChars?s.coachName=`Coach name cannot exceed ${b.COACH_NAME.maxChars} characters`:delete s.coachName:s.coachName="Coach/Seller/Academy name is required";break}case"description":{a.replace(/<[^>]*>/g,"").trim()?delete s.description:s.description="Strategy description is required";break}case"aboutCoach":{a.replace(/<[^>]*>/g,"").trim()?delete s.aboutCoach:s.aboutCoach="About the coach information is required";break}case"strategicContent":{a.replace(/<[^>]*>/g,"").trim()?delete s.strategicContent:s.strategicContent="Strategic content description is required";break}case"contentType":{a?delete s.contentType:s.contentType="Please select a content type";break}case"difficulty":{a?delete s.difficulty:s.difficulty="Please select a difficulty level";break}case"saleType":{a?delete s.saleType:s.saleType="Please select a sale type";break}case"price":{!a||a<=0?s.price="Please enter a valid price greater than $0":delete s.price;break}}x(s)},be=async i=>{const a=i.target.files[0];if(a){if(!t.contentType){C("Please select a content type before uploading a file"),i.target.value="";return}const s=oe(a,t.contentType);if(!s.isValid){C(s.message),i.target.value="";return}w(a),N(!0),_("content file"),F(a.name),T(0);const c=new FormData;c.append("file",a),c.append("type","content");try{const d=await u(ie({formData:c,onProgress:o=>T(o)})).unwrap();m(o=>({...o,fileUrl:d.data.fileUrl,fileSize:d.data.fileSize||a.size}));const n={...r};delete n.fileUpload,x(n),$("Content file uploaded successfully!")}catch(d){console.error("File upload failed:",d),w(null),C(d.message||"Failed to upload content file. Please try again.")}finally{N(!1),_(""),F(""),T(0)}}},je=async i=>{i.preventDefault(),Y(!0);const a={};if(t.title.trim()?t.title.length>b.TITLE.maxChars&&(a.title=`Title cannot exceed ${b.TITLE.maxChars} characters`):a.title="Strategy title is required",t.category||(a.category="Please select a category"),t.coachName.trim()?t.coachName.length>b.COACH_NAME.maxChars&&(a.coachName=`Coach name cannot exceed ${b.COACH_NAME.maxChars} characters`):a.coachName="Coach/Seller/Academy name is required",t.description.replace(/<[^>]*>/g,"").trim()||(a.description="Strategy description is required"),!t.fileUrl&&!y&&(a.fileUpload="Please upload a video or document file"),t.aboutCoach.replace(/<[^>]*>/g,"").trim()||(a.aboutCoach="About the coach information is required"),t.strategicContent.replace(/<[^>]*>/g,"").trim()||(a.strategicContent="Strategic content description is required"),t.contentType||(a.contentType="Please select a content type"),t.thumbnailUrl||(a.thumbnailUpload="Please upload a thumbnail image"),t.difficulty||(a.difficulty="Please select a difficulty level"),t.saleType||(a.saleType="Please select a sale type"),t.saleType==="Fixed")(!t.price||t.price<=0)&&(a.price="Please enter a valid price greater than $0");else if(t.saleType==="Auction"&&((!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0)&&(a.auctionBasePrice="Please enter a valid starting bid price greater than $0"),(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0)&&(a.auctionMinIncrement="Please enter a valid minimum bid increment greater than $0"),t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate)<=new Date&&(a.auctionStartDate="Auction start date must be in the future"):a.auctionStartDate="Please select an auction start date",t.auctionDetails.auctionEndDate||(a.auctionEndDate="Please select an auction end date"),t.auctionDetails.auctionStartDate&&t.auctionDetails.auctionEndDate)){const n=new Date(t.auctionDetails.auctionStartDate);new Date(t.auctionDetails.auctionEndDate)<=n&&(a.auctionDateRange="Auction end date must be after start date")}if(Object.keys(a).length>0){$e.flushSync(()=>{x(a),H(!0)}),setTimeout(()=>{const n=document.querySelector(".AddStrategy__validation-error");n&&n.scrollIntoView({behavior:"smooth",block:"center"})},100);return}try{const n={...t,sport:t.category||"Other",coachName:t.coachName||"Coach"};t.saleType==="Fixed"?n.price=parseFloat(t.price):t.saleType==="Auction"&&(n.price=parseFloat(t.auctionDetails.basePrice),n.auctionDetails.auctionStartDate&&(n.auctionDetails.auctionStartDate=E(new Date(n.auctionDetails.auctionStartDate))),n.auctionDetails.auctionEndDate&&(n.auctionDetails.auctionEndDate=E(new Date(n.auctionDetails.auctionEndDate))));const o=await u(Be(n)).unwrap();o.data&&o.data._id&&(L(o.data._id),V(!0)),J(t.title),q(!0),$("🎉 Strategy created successfully! Preview will be generated in the background.")}catch(n){console.error("Content creation failed:",n);let o="Failed to create strategy. Please try again.";n.message?o=n.message:n.errors&&n.errors.length>0?o=n.errors[0].msg||o:typeof n=="string"&&(o=n),C(`❌ ${o}`)}},W=()=>{m({title:"",category:"",coachName:"",description:"",fileUrl:"",aboutCoach:"",strategicContent:"",sport:"Other",contentType:"",previewUrl:"",thumbnailUrl:"",duration:"",videoLength:"",fileSize:"",tags:[],difficulty:"",language:"English",prerequisites:[],learningObjectives:[],equipment:[],saleType:"",price:"",allowCustomRequests:!1,customRequestPrice:"",status:"Draft",visibility:"Public",auctionDetails:{basePrice:"",auctionStartDate:"",auctionEndDate:"",minimumBidIncrement:"",allowOfferBeforeAuctionStart:!1}}),w(null),L(null),V(!1),x({}),H(!1),Y(!1),U(""),B(!1),q(!1),J("")},ve=()=>{q(!1),W(),h("/seller/my-sports-strategies")},K={maxSize:5*1024*1024,allowedTypes:["image/jpeg","image/jpg","image/png","image/gif"],allowedExtensions:[".jpg",".jpeg",".png",".gif"]},Ne=async i=>{const a=i.target.files[0];if(a){U(""),B(!1);try{if(!K.allowedTypes.includes(a.type))throw new Error("Only JPG, JPEG, PNG, and GIF formats are supported for thumbnails");if(a.size>K.maxSize)throw new Error("Thumbnail file size must be less than 5MB");N(!0),_("thumbnail"),F(a.name),T(0);const s=new FormData;s.append("file",a),s.append("type","thumbnail");const c=await u(ie({formData:s,onProgress:n=>T(n)})).unwrap();if(!c.data||!c.data.fileUrl)throw new Error("Invalid response from server");const d=c.data.fileUrl;m(n=>({...n,thumbnailUrl:d})),B(!0),$("Thumbnail uploaded successfully!")}catch(s){console.error("Thumbnail upload failed:",s),U(s.message||"Failed to upload thumbnail. Please try again."),m(c=>({...c,thumbnailUrl:""}))}finally{N(!1),_(""),F(""),T(0)}}};return e.jsx(ne,{children:e.jsxs("div",{className:"AddStrategy",children:[e.jsxs("form",{className:"AddStrategy__form",onSubmit:je,children:[e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Strategy Title"}),e.jsx("input",{type:"text",name:"title",className:"AddStrategy__input",placeholder:"Add title for New Strategy",value:t.title,onChange:A,onBlur:D}),(r.title||p&&!t.title.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.title||"Strategy title is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Select Category"}),e.jsxs("select",{name:"category",className:"AddStrategy__select",value:t.category,onChange:A,onBlur:D,children:[e.jsx("option",{value:"",children:"Select Category"}),e.jsx("option",{value:"Basketball",children:"Basketball"}),e.jsx("option",{value:"Football",children:"Football"}),e.jsx("option",{value:"Soccer",children:"Soccer"}),e.jsx("option",{value:"Baseball",children:"Baseball"})]}),(r.category||p&&!t.category)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.category||"Please select a category"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Coach/Seller/Academy Name"}),e.jsx("input",{type:"text",name:"coachName",className:"AddStrategy__input",placeholder:"Enter coach, seller, or academy name",value:t.coachName,onChange:A,onBlur:D}),(r.coachName||ye&&!t.coachName.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.coachName||"Coach/Seller/Academy name is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Description for Strategy"}),e.jsx(R,{value:t.description,onChange:i=>M("description",i),placeholder:"Enter a detailed description of your strategy...",height:200,className:"AddStrategy__summernote"}),(r.description||p&&!t.description.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.description||"Strategy description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Content Type"}),e.jsxs("select",{name:"contentType",className:"AddStrategy__select",value:t.contentType,onChange:A,onBlur:D,children:[e.jsx("option",{value:"",children:"Select Content Type"}),e.jsx("option",{value:"Video",children:"Video"}),e.jsx("option",{value:"Document",children:"Document"})]}),(r.contentType||p&&!t.contentType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.contentType||"Please select a content type"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsxs("label",{className:"AddStrategy__label",children:["Upload ",t.contentType||"Video/Document"]}),t.contentType&&e.jsx("p",{className:"AddStrategy__format-note",children:t.contentType==="Video"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:ue(de.Video)})," • Supported formats: ",e.jsx("span",{children:"MP4, MOV, AVI, WEBM"})]}):t.contentType==="Document"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:ue(de.Document)})," • Supported formats: ",e.jsx("span",{children:"PDF"})]}):"Please select a content type to see upload requirements"}),e.jsxs("label",{htmlFor:"file-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"file-upload",className:"AddStrategy__file-input",onChange:be,accept:qe(t.contentType),disabled:re(t.contentType),style:{display:"none"}}),e.jsxs("div",{className:`AddStrategy__upload-content ${re(t.contentType)?"AddStrategy__upload-content--disabled":""}`,children:[e.jsx(te,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:y?y.name:t.fileUrl?"Current file uploaded":"Click to upload file"})]}),(y||t.fileUrl)&&e.jsxs("div",{className:"AddStrategy__file-info",children:[e.jsx("p",{className:"AddStrategy__file-name",children:y?y.name:"Current file uploaded"}),y&&e.jsxs("p",{className:"AddStrategy__file-size",children:[(y.size/1024/1024).toFixed(2)," MB"]})]})]}),(r.fileUpload||p&&!t.fileUrl&&!y)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.fileUpload||"Please upload a video or document file"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"About The Coach"}),e.jsx(R,{value:t.aboutCoach,onChange:i=>M("aboutCoach",i),placeholder:"Share your background, experience, and expertise...",height:200,className:"AddStrategy__summernote"}),(r.aboutCoach||p&&!t.aboutCoach.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.aboutCoach||"About the coach information is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Includes Strategic Content"}),e.jsx(R,{value:t.strategicContent,onChange:i=>M("strategicContent",i),placeholder:"Describe what strategic content is included...",height:200,className:"AddStrategy__summernote"}),(r.strategicContent||p&&!t.strategicContent.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.strategicContent||"Strategic content description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Thumbnail Image"}),e.jsxs("p",{className:"AddStrategy__format-note",children:["Maximum size: ",e.jsx("span",{children:"5MB"})," • Supported formats: ",e.jsx("span",{children:"JPG, JPEG, PNG, GIF"})]}),e.jsxs("label",{htmlFor:"thumbnail-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"thumbnail-upload",className:"AddStrategy__file-input",accept:"image/jpeg,image/jpg,image/png,image/gif",onChange:Ne,style:{display:"none"}}),e.jsxs("div",{className:"AddStrategy__upload-content",children:[e.jsx(te,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:t.thumbnailUrl?"Thumbnail uploaded":"Click to upload thumbnail"})]}),G&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:G})}),(r.thumbnailUpload||p&&!t.thumbnailUrl)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.thumbnailUpload||"Please upload a thumbnail image"})}),t.thumbnailUrl&&ge&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:ae(t.thumbnailUrl),alt:"Thumbnail preview",onError:i=>{console.error("Thumbnail preview failed:",i),console.error("Failed URL:",ae(t.thumbnailUrl)),console.error("Original thumbnailUrl:",t.thumbnailUrl),i.target.src=se(200,120,"Image not found")},style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)"}})}),!t.thumbnailUrl&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:se(200,120,"No thumbnail"),alt:"No thumbnail",style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)",opacity:.7}})})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Tags"}),e.jsxs("div",{className:"AddStrategy__array-field",children:[e.jsxs("div",{className:"AddStrategy__array-input",children:[e.jsx("input",{type:"text",className:"AddStrategy__input",placeholder:"Add a tag (e.g., basketball, technique, training)...",value:j,onChange:i=>v(i.target.value),onKeyPress:i=>{i.key==="Enter"&&(i.preventDefault(),j.trim()&&(m(a=>({...a,tags:[...a.tags,j.trim()]})),v("")))}}),e.jsx("button",{type:"button",className:"AddStrategy__add-btn btn-primary",onClick:()=>{j.trim()&&(m(i=>({...i,tags:[...i.tags,j.trim()]})),v(""))},children:"Add"})]}),e.jsx("div",{className:"AddStrategy__array-items",children:t.tags.map((i,a)=>e.jsxs("div",{className:"AddStrategy__array-item",children:[e.jsx("span",{children:i}),e.jsx("button",{type:"button",className:"AddStrategy__remove-btn",onClick:()=>{m(s=>({...s,tags:s.tags.filter((c,d)=>d!==a)}))},children:"X"})]},a))})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Difficulty Level"}),e.jsxs("select",{name:"difficulty",className:"AddStrategy__select",value:t.difficulty,onChange:A,onBlur:D,children:[e.jsx("option",{value:"",children:"Select Difficulty"}),e.jsx("option",{value:"Beginner",children:"Beginner"}),e.jsx("option",{value:"Intermediate",children:"Intermediate"}),e.jsx("option",{value:"Advanced",children:"Advanced"}),e.jsx("option",{value:"Professional",children:"Professional"})]}),(r.difficulty||p&&!t.difficulty)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.difficulty||"Please select a difficulty level"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Sale Type"}),e.jsxs("select",{name:"saleType",className:"AddStrategy__select",value:t.saleType,onChange:A,onBlur:D,children:[e.jsx("option",{value:"",children:"Select Sale Type"}),e.jsx("option",{value:"Fixed",children:"Fixed Price"}),e.jsx("option",{value:"Auction",children:"Auction"})]}),(r.saleType||p&&!t.saleType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.saleType||"Please select a sale type"})})]}),t.saleType==="Fixed"&&e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Price ($)"}),e.jsx("input",{type:"number",name:"price",className:"AddStrategy__input",placeholder:"Enter price",value:t.price,onChange:A,onBlur:D,min:"0",step:"0.01"}),(r.price||p&&(!t.price||t.price<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.price||"Please enter a valid price greater than $0"})})]}),t.saleType==="Auction"&&e.jsxs("div",{className:"AddStrategy__auction-section",children:[e.jsx("h3",{className:"AddStrategy__section-title",children:"Auction Settings"}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Starting Bid Price ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.basePrice",className:"AddStrategy__input",placeholder:"Enter starting bid price",value:t.auctionDetails.basePrice,onChange:i=>m(a=>({...a,auctionDetails:{...a.auctionDetails,basePrice:i.target.value}})),min:"0",step:"0.01"}),(r.auctionBasePrice||p&&(!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionBasePrice||"Please enter a valid starting bid price greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Minimum Bid Increment ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.minimumBidIncrement",className:"AddStrategy__input",placeholder:"Enter minimum bid increment",value:t.auctionDetails.minimumBidIncrement,onChange:i=>m(a=>({...a,auctionDetails:{...a.auctionDetails,minimumBidIncrement:i.target.value}})),min:"0.01",step:"0.01"}),(r.auctionMinIncrement||p&&(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionMinIncrement||"Please enter a valid minimum bid increment greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction Start Date"}),e.jsxs(le,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionStartDate",className:"AddStrategy__input",value:t.auctionDetails.auctionStartDate,min:new Date().toISOString().slice(0,16),onChange:i=>{const a=i.target.value;m(s=>({...s,auctionDetails:{...s.auctionDetails,auctionStartDate:a}})),a&&(E(new Date(a))<=new Date?x(d=>({...d,auctionStartDate:"Auction start date must be in the future"})):x(d=>{const n={...d};return delete n.auctionStartDate,n}))}}),e.jsx(ce,{})]}),(r.auctionStartDate||p&&!t.auctionDetails.auctionStartDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionStartDate||"Please select an auction start date"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction End Date"}),e.jsxs(le,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionEndDate",className:"AddStrategy__input",value:t.auctionDetails.auctionEndDate,min:t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate).toISOString().slice(0,16):new Date().toISOString().slice(0,16),onChange:i=>{const a=i.target.value;if(m(s=>({...s,auctionDetails:{...s.auctionDetails,auctionEndDate:a}})),a&&t.auctionDetails.auctionStartDate){const s=E(new Date(t.auctionDetails.auctionStartDate));E(new Date(a))<=s?x(d=>({...d,auctionDateRange:"Auction end date must be after start date"})):x(d=>{const n={...d};return delete n.auctionDateRange,n})}}}),e.jsx(ce,{})]}),(r.auctionEndDate||r.auctionDateRange||p&&!t.auctionDetails.auctionEndDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionEndDate||r.auctionDateRange||"Please select an auction end date"})})]}),e.jsx("div",{className:"AddStrategy__field",children:e.jsxs("label",{className:"AddStrategy__checkbox-label",children:[e.jsx("input",{type:"checkbox",name:"auctionDetails.allowOfferBeforeAuctionStart",checked:t.auctionDetails.allowOfferBeforeAuctionStart,onChange:i=>m(a=>({...a,auctionDetails:{...a.auctionDetails,allowOfferBeforeAuctionStart:i.target.checked}})),className:"AddStrategy__checkbox"}),"Allow offers before auction starts"]})}),e.jsx("div",{className:"AddStrategy__field",children:e.jsx("div",{className:"AddStrategy__auction-note",children:e.jsxs("p",{children:[e.jsx("strong",{children:"Note:"})," Once the auction starts, the strategy content cannot be edited."]})})})]}),I&&S&&e.jsx("div",{className:"AddStrategy__error",children:e.jsxs("p",{children:["Error: ",S.message||"Something went wrong"]})}),e.jsxs("div",{className:"AddStrategy__actions",children:[e.jsx("button",{type:"submit",className:"btn-primary",disabled:g,children:g?"Creating...":"Add New Strategy"}),e.jsx("button",{type:"button",className:"btn-outline",onClick:W,disabled:g,children:"Reset Form"})]})]}),pe&&z&&e.jsx(ze,{contentId:z,onStatusChange:i=>{console.log("Preview status updated:",i)}}),e.jsx(Me,{isVisible:k,progress:he,fileName:me,uploadType:P}),e.jsx(Re,{isOpen:Se,onClose:ve,contentTitle:xe})]})})};export{Xe as default};
