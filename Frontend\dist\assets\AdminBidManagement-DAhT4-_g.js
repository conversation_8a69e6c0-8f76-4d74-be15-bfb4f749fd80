import{b as C,d as u,dg as O,bn as _,j as e,aB as I,dh as U,di as E,dj as R,b4 as P,bD as W,dk as G,dl as V,r as v,dm as A,dn as D,bt as q,cI as z,a6 as k,dp as H,bi as p,dq as S,dr as J,ds as K,b5 as Q,dt as X,du as Y,dv as Z,dw as ee}from"./index-ctFdmWBt.js";import{A as se}from"./AdminLayout-D3bHW2Uz.js";import{T as te}from"./Table-j5pMA9qi.js";import{A as ae}from"./AdminTableActions-BuK3aIxJ.js";const ie=()=>{var j,o,f,m,g;const t=C(),i=u(O),d=u(_);if(!i||!d.showBidDetailModal)return null;const B=b=>new Date(b).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),r=b=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(b),l=()=>{t(U())};return e.jsx("div",{className:"modal-overlay",children:e.jsxs("div",{className:"bid-detail-modal",children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h2",{children:"Bid Details"}),e.jsx("button",{className:"close-button",onClick:l,children:e.jsx(I,{})})]}),e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"bid-info-section",children:[e.jsx("h3",{children:"Bid Information"}),e.jsxs("div",{className:"info-grid",children:[e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Bid ID:"}),e.jsx("span",{children:i.bidId||`#${(j=i._id)==null?void 0:j.slice(-6)}`})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Status:"}),e.jsx("span",{className:`status-badge ${i.status.toLowerCase()}`,children:i.status})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Bid Amount:"}),e.jsx("span",{children:r(i.amount)})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Bid Date:"}),e.jsx("span",{children:B(i.createdAt)})]}),i.maxAutoBidAmount&&e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Max Auto-Bid Amount:"}),e.jsx("span",{children:r(i.maxAutoBidAmount)})]})]})]}),e.jsxs("div",{className:"content-info-section",children:[e.jsx("h3",{children:"Content Information"}),e.jsxs("div",{className:"info-grid",children:[e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Title:"}),e.jsx("span",{children:(o=i.content)==null?void 0:o.title})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Type:"}),e.jsx("span",{children:(f=i.content)==null?void 0:f.contentType})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Base Price:"}),e.jsx("span",{children:r(((m=i.content)==null?void 0:m.price)||0)})]})]})]}),e.jsxs("div",{className:"bidder-info-section",children:[e.jsx("h3",{children:"Bidder Information"}),e.jsxs("div",{className:"info-grid",children:[e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Name:"}),e.jsx("span",{children:i.bidder?`${i.bidder.firstName} ${i.bidder.lastName}`:i.bidderName})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("label",{children:"Email:"}),e.jsx("span",{children:((g=i.bidder)==null?void 0:g.email)||"N/A"})]})]})]})]})]})})},ce=()=>{const t=C(),i=u(E),d=u(R);u(_);const B=u(P);u(W);const r=u(G),l=u(V),j=i.data.length>0?i.data:[],[o,f]=v.useState(r.search||""),[m,g]=v.useState(r.status||"all");v.useEffect(()=>{t(A({page:1,limit:10,search:"",status:"",sortBy:"createdAt",sortOrder:"desc"}))},[t]),v.useEffect(()=>{const s=setTimeout(()=>{if(o!==r.search||m!==(r.status||"all")){const a={search:o,status:m==="all"?"":m};t(D(a)),t(A({page:1,limit:l.limit,search:a.search,status:a.status,sortBy:r.sortBy,sortOrder:r.sortOrder}))}},10);return()=>clearTimeout(s)},[o,m,t,r.search,r.status,r.sortBy,r.sortOrder,l.limit]),v.useEffect(()=>{l.current>1&&t(A({page:l.current,limit:l.limit,search:r.search,status:r.status,sortBy:r.sortBy,sortOrder:r.sortOrder}))},[l.current]);const b=j.filter(s=>{var c,h;const a=!o||s.bidId&&s.bidId.toLowerCase().includes(o.toLowerCase())||((c=s.content)==null?void 0:c.title)&&s.content.title.toLowerCase().includes(o.toLowerCase())||((h=s.bidder)==null?void 0:h.firstName)&&`${s.bidder.firstName} ${s.bidder.lastName}`.toLowerCase().includes(o.toLowerCase())||s.bidderName&&s.bidderName.toLowerCase().includes(o.toLowerCase()),n=m==="all"||s.status===m;return a&&n}),L=async(s,a)=>{var x;const n=s._id||s.id,c=s.bidId||`#${n==null?void 0:n.slice(-6)}`,h=((x=s.content)==null?void 0:x.title)||s.contentTitle;switch(a){case"view":case"edit":t(ee(s));break;case"approve":if(window.confirm(`Approve bid "${c}"?`))try{await t(Z({id:n,approvalNotes:""})).unwrap(),t(p({id:Date.now(),type:"bid_approval",description:`Bid approved: ${c} for ${h}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`Bid "${c}" has been approved!`)}catch(N){alert(`Failed to approve bid: ${N}`)}break;case"reject":const y=prompt(`Reason for rejecting bid "${c}":`);if(y)try{await t(Y({id:n,reason:y,rejectionNotes:""})).unwrap(),t(p({id:Date.now(),type:"bid_rejection",description:`Bid rejected: ${c} - Reason: ${y}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`Bid "${c}" has been rejected.`)}catch(N){alert(`Failed to reject bid: ${N}`)}break;case"delete":if(window.confirm(`Delete bid "${c}"? This action cannot be undone.`))try{await t(X(n)).unwrap(),t(p({id:Date.now(),type:"bid_deletion",description:`Bid deleted: ${c}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`Bid "${c}" has been deleted!`)}catch(N){alert(`Failed to delete bid: ${N}`)}break}},w=async s=>{if(d.length===0){alert("Please select bids first");return}switch(s){case"approve":if(window.confirm(`Approve ${d.length} selected bids?`))try{await t(K(d)).unwrap(),t(p({id:Date.now(),type:"bulk_bid_approval",description:`Bulk approved ${d.length} bids`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`${d.length} bids approved`),t(S([]))}catch(n){alert(`Failed to approve bids: ${n}`)}break;case"reject":const a=prompt(`Reason for rejecting ${d.length} bids:`);if(a)try{await t(J({bidIds:d,reason:a})).unwrap(),t(p({id:Date.now(),type:"bulk_bid_rejection",description:`Bulk rejected ${d.length} bids - Reason: ${a}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`${d.length} bids rejected`),t(S([]))}catch(n){alert(`Failed to reject bids: ${n}`)}break;case"delete":if(window.confirm(`Delete ${d.length} selected bids? This action cannot be undone.`))try{await t(H(d)).unwrap(),t(p({id:Date.now(),type:"bulk_bid_deletion",description:`Bulk deleted ${d.length} bids`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`${d.length} bids deleted`),t(S([]))}catch(n){alert(`Failed to delete bids: ${n}`)}break}},$=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s),F=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),T=s=>{switch(s){case"Active":return"status-badge active";case"Won":return"status-badge won";case"Lost":return"status-badge lost";case"Outbid":return"status-badge outbid";case"Cancelled":return"status-badge cancelled";default:return"status-badge"}},M=[{key:"bidId",label:"Bid ID",render:s=>{var a;return`#${((a=s._id)==null?void 0:a.slice(-6))||"N/A"}`}},{key:"content",label:"Content",render:s=>{var a,n,c,h,x;return e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-thumbnail",children:(a=s.content)!=null&&a.thumbnailUrl?e.jsx("img",{src:`${Q}${s.content.thumbnailUrl}`,alt:((n=s.content)==null?void 0:n.title)||"Content"}):e.jsx(k,{})}),e.jsxs("div",{className:"content-details",children:[e.jsx("span",{className:"content-title",children:((c=s.content)==null?void 0:c.title)||"Unknown Content"}),e.jsx("span",{className:"content-seller",children:((x=(h=s.content)==null?void 0:h.seller)==null?void 0:x.fullName)||"Unknown Seller"})]})]})}},{key:"bidder",label:"Bidder",render:s=>{var a,n;return e.jsxs("div",{className:"bidder-info",children:[e.jsx("span",{className:"bidder-name",children:((a=s.bidder)==null?void 0:a.fullName)||"Unknown Bidder"}),e.jsx("span",{className:"bidder-email",children:((n=s.bidder)==null?void 0:n.email)||"No email"})]})}},{key:"amount",label:"Bid Amount",render:s=>e.jsxs("div",{className:"bid-amount",children:[e.jsx("span",{className:"current-bid",children:$(s.bidAmount||0)}),s.isAutoBid&&e.jsxs("span",{className:"auto-bid-info",children:["Max: ",$(s.maxAutoBidAmount)]})]})},{key:"status",label:"Status",render:s=>e.jsx("span",{className:T(s.status),children:s.status})},{key:"bidDate",label:"Bid Date",render:s=>{const a=s.bidDate||s.createdAt||new Date;return F(a)}},{key:"actions",label:"Actions",render:s=>e.jsx(ae,{item:s,onView:()=>L(s,"view"),permissions:{view:!0,edit:!1,delete:!1},tooltips:{view:"View Bid Details"}}),className:"actions-column"}];return e.jsxs(se,{children:[e.jsxs("div",{className:"AdminBidManagement",children:[e.jsx("div",{className:"AdminBidManagement__header",children:e.jsxs("div",{className:"AdminUserManagement__main",children:[e.jsxs("div",{className:"search-container",children:[e.jsx(q,{className:"search-icon"}),e.jsx("input",{type:"text",placeholder:"Search bids by ID, content, or bidder...",value:o,onChange:s=>f(s.target.value),className:"search-input"})]}),e.jsxs("div",{className:"AdminBidManagement__filters",children:[e.jsx("div",{className:"filter-group",children:e.jsxs("select",{value:m,onChange:s=>g(s.target.value),className:"filter-select",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"Active",children:"Active"}),e.jsx("option",{value:"Won",children:"Won"}),e.jsx("option",{value:"Lost",children:"Lost"}),e.jsx("option",{value:"Outbid",children:"Outbid"}),e.jsx("option",{value:"Cancelled",children:"Cancelled"})]})}),d.length>0&&e.jsxs("div",{className:"bulk-actions",children:[e.jsxs("span",{className:"selected-count",children:[d.length," selected"]}),e.jsxs("button",{className:"btn btn-success",onClick:()=>w("approve"),children:[e.jsx(z,{}),"Approve"]}),e.jsxs("button",{className:"btn btn-warning",onClick:()=>w("reject"),children:[e.jsx(I,{}),"Reject"]}),e.jsx("button",{className:"btn btn-danger",onClick:()=>w("delete"),children:"Delete"})]})]})]})}),e.jsx("div",{className:"AdminBidManagement__table",children:e.jsx(te,{columns:M,data:b,isAdmin:!0,loading:{isLoading:B.bids,message:"Loading bids..."},emptyMessage:e.jsxs("div",{className:"no-results",children:[e.jsx(k,{className:"no-results-icon"}),e.jsx("h3",{children:"No bids found"}),e.jsx("p",{children:"Try adjusting your search or filter criteria"})]}),className:"bids-table"})}),e.jsxs("div",{className:"AdminBidManagement__pagination",children:[e.jsxs("div",{className:"pagination-info",children:["Showing ",b.length," of"," ",l.total||j.length," bids"]}),e.jsxs("div",{className:"pagination-controls",children:[e.jsx("button",{className:"btn btn-outline",disabled:l.current<=1,onClick:()=>t(D({page:l.current-1})),children:"Previous"}),e.jsx("span",{className:"page-number active",children:l.current}),e.jsx("button",{className:"btn btn-outline",disabled:l.current>=l.pages,onClick:()=>t(D({page:l.current+1})),children:"Next"})]})]})]}),e.jsx(ie,{})]})};export{ce as default};
