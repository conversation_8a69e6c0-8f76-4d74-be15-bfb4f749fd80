import{b as I,d as v,dL as O,b4 as L,r as p,j as e,dM as F,by as U,aB as B,dN as q,dO as R,aV as V,b8 as z,dP as Y,dQ as A,dR as H,dS as K,bi as S,dT as Q,dU as W,dV as X,dW as Z,bn as G,bD as J,dX as ee,a8 as E,br as se,bs as te,dY as k,bt as ae,dZ as ie,d_ as ne,d$ as M,e0 as le,e1 as $,e2 as re}from"./index-ctFdmWBt.js";import{A as ce}from"./AdminLayout-D3bHW2Uz.js";import{S as de}from"./SummernoteEditor-CakKAUcC.js";import{T as oe}from"./Table-j5pMA9qi.js";import{A as ue}from"./AdminTableActions-BuK3aIxJ.js";const me=()=>{const i=I(),n=v(O),l=v(L),[a,x]=p.useState({title:"",slug:"",content:"",metaDescription:"",status:"draft",featuredImage:""}),[N,C]=p.useState(!1),[m,b]=p.useState({}),[D,h]=p.useState(!1),[y,_]=p.useState({}),[o,j]=p.useState(!1),g=n&&(n._id||n.id);p.useEffect(()=>{n?(x({title:n.title||"",slug:n.slug||"",content:n.content||"",metaDescription:n.metaDescription||"",status:n.status||"draft",featuredImage:n.featuredImage||""}),j(!0)):(x({title:"",slug:"",content:"",metaDescription:"",status:"draft",featuredImage:""}),j(!1)),b({}),_({}),h(!1)},[n,g]);const w=()=>{i(Y()),C(!1),b({})},P=t=>t.toLowerCase().trim().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+|-+$/g,""),f=(t,r)=>{if(x(u=>({...u,[t]:r})),t==="title"&&!o){const u=P(r);x(T=>({...T,slug:u}))}t==="slug"&&j(!0),m[t]&&b(u=>({...u,[t]:""})),y[t]&&_(u=>({...u,[t]:""}))},s=(t,r)=>{f(t,r)},c=()=>{const t={},r={};return a.title.trim()||(t.title="Title is required",r.title="Title is required"),a.slug.trim()?/^[a-z0-9-]+$/.test(a.slug)||(t.slug="Slug can only contain lowercase letters, numbers, and hyphens",r.slug="Slug can only contain lowercase letters, numbers, and hyphens"):(t.slug="Slug is required",r.slug="Slug is required"),(!a.content.trim()||!a.content.replace(/<[^>]*>/g,"").trim())&&(t.content="Content is required",r.content="Content is required"),b(t),_(r),h(!0),Object.keys(t).length===0},d=async()=>{if(c()){i(A(!0));try{const t={title:a.title,slug:a.slug,content:a.content,metaDescription:a.metaDescription||"",status:a.status,featuredImage:a.featuredImage||""};let r;if(g){const u=n._id||n.id;r=await H(u,t),i(K({...r.data,_id:u,id:u,lastModified:new Date().toISOString(),author:"Admin"})),i(S({id:Date.now(),type:"cms_update",description:`CMS page updated: ${a.title}`,timestamp:new Date().toISOString(),user:"Admin"}))}else r=await Q(t),i(W({...r.data,id:r.data._id||r.data.id||Date.now(),lastModified:new Date().toISOString(),author:"Admin"})),i(S({id:Date.now(),type:"cms_create",description:`CMS page created: ${a.title}`,timestamp:new Date().toISOString(),user:"Admin"}));i(A(!1)),w(),alert(`Page "${a.title}" has been ${g?"updated":"created"} successfully!`)}catch(t){console.error("Error saving CMS page:",t),i(A(!1)),alert(`Error ${g?"updating":"creating"} page: ${t.message||"Unknown error"}`)}}};return e.jsxs("div",{className:"CMSEditorModal",children:[e.jsx("div",{className:"CMSEditorModal__overlay",onClick:w}),e.jsxs("div",{className:"CMSEditorModal__container",children:[e.jsxs("div",{className:"CMSEditorModal__header",children:[e.jsxs("div",{className:"header-content",children:[e.jsxs("h2",{children:[e.jsx(F,{}),g?"Edit Page":"Create New Page"]}),e.jsx("div",{className:"header-actions",children:e.jsxs("button",{className:`btn ${N?"btn-primary":"btn-outline"}`,onClick:()=>C(!N),children:[e.jsx(U,{}),N?"Edit":"Preview"]})})]}),e.jsx("button",{className:"close-btn",onClick:w,children:e.jsx(B,{})})]}),e.jsx("div",{className:"CMSEditorModal__content",children:N?e.jsxs("div",{className:"preview-content",children:[e.jsxs("div",{className:"preview-header",children:[e.jsx("h1",{children:a.title||"Untitled Page"}),a.featuredImage&&e.jsx("img",{src:a.featuredImage,alt:a.title,className:"featured-image"}),a.metaDescription&&e.jsx("p",{className:"meta-description",children:a.metaDescription})]}),e.jsx("div",{className:"preview-body",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:a.content||"<p>No content yet...</p>"}})})]}):e.jsxs("div",{className:"editor-form",children:[e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Basic Information"}),e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Page Title *"}),e.jsx("input",{type:"text",value:a.title,onChange:t=>f("title",t.target.value),className:`form-input ${m.title?"error":""}`,placeholder:"Enter page title"}),m.title&&e.jsx("span",{className:"error-message",children:m.title})]}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{children:["URL Slug *",e.jsxs("button",{type:"button",className:"slug-toggle-btn",onClick:()=>{const t=!o;if(j(t),!t&&a.title){const r=P(a.title);x(u=>({...u,slug:r}))}},title:o?"Auto-generate from title":"Edit manually",children:[o?e.jsx(q,{}):e.jsx(R,{}),o?" Manual":" Auto"]})]}),e.jsxs("div",{className:"input-with-button",children:[e.jsx("input",{type:"text",value:a.slug,onChange:t=>f("slug",t.target.value),className:`form-input ${m.slug?"error":""} ${o?"":"auto-generated"}`,placeholder:"page-url-slug",readOnly:!o}),o&&e.jsx("button",{type:"button",className:"reset-slug-btn",onClick:()=>{if(a.title){const t=P(a.title);x(r=>({...r,slug:t}))}},title:"Reset to auto-generated slug",children:e.jsx(V,{})})]}),!o&&e.jsx("small",{className:"help-text",children:"Slug is automatically generated from the page title. Click the lock icon to edit manually."}),o&&e.jsx("small",{className:"help-text",children:"You are manually editing the slug. Use hyphens instead of spaces and avoid special characters."}),m.slug&&e.jsx("span",{className:"error-message",children:m.slug})]})]}),e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Status"}),e.jsxs("select",{value:a.status,onChange:t=>f("status",t.target.value),className:"form-select",children:[e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"published",children:"Published"}),e.jsx("option",{value:"archived",children:"Archived"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Featured Image URL (Optional)"}),e.jsx("input",{type:"url",value:a.featuredImage,onChange:t=>f("featuredImage",t.target.value),className:"form-input",placeholder:"https://example.com/image.jpg"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Meta Description (Optional)"}),e.jsx("textarea",{value:a.metaDescription,onChange:t=>f("metaDescription",t.target.value),className:"form-textarea",rows:"2",placeholder:"Brief description for search engines (160 characters max)",maxLength:"160"}),e.jsxs("small",{className:"char-count",children:[a.metaDescription.length,"/160"]})]})]}),e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Page Content"}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Content *"}),e.jsx(de,{value:a.content,onChange:t=>s("content",t),placeholder:"Enter your page content here...",height:300,className:"cms-summernote",contentKey:`cms-content-${g?n==null?void 0:n.id:"new"}`}),(y.content||D&&!a.content.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"validation-error",children:e.jsx("p",{className:"error-message",children:y.content||"Content is required"})}),m.content&&e.jsx("span",{className:"error-message",children:m.content})]})]})]})}),e.jsxs("div",{className:"CMSEditorModal__footer",children:[e.jsx("div",{className:"footer-info",children:g&&e.jsxs("span",{className:"last-modified",children:["Last modified: ",new Date(n.lastModified).toLocaleString()]})}),e.jsxs("div",{className:"footer-actions",children:[e.jsx("button",{className:"btn btn-outline",onClick:w,disabled:l.cms,children:"Cancel"}),e.jsxs("button",{className:"btn btn-primary",onClick:d,disabled:l.cms,children:[e.jsx(z,{}),l.cms?"Saving...":g?"Update Page":"Create Page"]})]})]})]})]})},je=()=>{const i=I(),n=v(X),l=v(Z),a=v(G),x=v(L),N=v(J),[C,m]=p.useState(""),[b,D]=p.useState("all");p.useEffect(()=>{i(ee())},[i]);const h=Array.isArray(n)?n.filter(s=>{const c=s.title.toLowerCase().includes(C.toLowerCase())||s.slug.toLowerCase().includes(C.toLowerCase()),d=b==="all"||s.status===b;return c&&d}):[],y=s=>{s.target.checked?i(M(h.map(c=>c._id||c.id))):i(M([]))},_=s=>{const c=l.includes(s)?l.filter(d=>d!==s):[...l,s];i(M(c))},o=(s,c)=>{switch(c){case"view":{s.status==="published"?window.open(`/cms/${s.slug}`,"_blank"):alert("This page is not published yet. Only published pages can be viewed on the website.");break}case"edit":{i($(s));break}case"delete":{window.confirm(`Delete page "${s.title}"? This action cannot be undone.`)&&(i(le(s._id||s.id)),i(S({id:Date.now(),type:"cms_deletion",description:`CMS page deleted: ${s.title}`,timestamp:new Date().toISOString(),user:"Admin"})));break}case"toggle":{const d=s.status==="published"?"draft":"published";i(k({id:s._id||s.id,status:d})),i(S({id:Date.now(),type:"cms_status_change",description:`CMS page ${d}: ${s.title}`,timestamp:new Date().toISOString(),user:"Admin"}));break}}},j=s=>{if(l.length===0){alert("Please select pages first");return}switch(s){case"publish":{window.confirm(`Publish ${l.length} selected pages?`)&&(l.forEach(c=>{const d=h.find(t=>(t._id||t.id)===c);d&&i(k({id:d._id||d.id,status:"published"}))}),i(S({id:Date.now(),type:"bulk_cms_publish",description:`Bulk published ${l.length} CMS pages`,timestamp:new Date().toISOString(),user:"Admin"})),i(M([])));break}case"unpublish":{window.confirm(`Unpublish ${l.length} selected pages?`)&&(l.forEach(c=>{const d=h.find(t=>(t._id||t.id)===c);d&&i(k({id:d._id||d.id,status:"draft"}))}),i(S({id:Date.now(),type:"bulk_cms_unpublish",description:`Bulk unpublished ${l.length} CMS pages`,timestamp:new Date().toISOString(),user:"Admin"})),i(M([])));break}case"delete":{window.confirm(`Delete ${l.length} selected pages? This action cannot be undone.`)&&(i(re(l)),i(S({id:Date.now(),type:"bulk_cms_deletion",description:`Bulk deleted ${l.length} CMS pages`,timestamp:new Date().toISOString(),user:"Admin"})),i(M([])));break}}},g=()=>{i($(null))},w=s=>s?new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"N/A",P=s=>{switch(s){case"published":return"status-badge published";case"draft":return"status-badge draft";default:return"status-badge"}},f=[{key:"select",label:e.jsx("input",{type:"checkbox",onChange:y,checked:l.length===h.length&&h.length>0}),render:s=>e.jsx("input",{type:"checkbox",checked:l.includes(s._id||s.id),onChange:()=>_(s._id||s.id)}),className:"select-column"},{key:"page",label:"Page Title",render:s=>e.jsxs("div",{className:"page-info",children:[e.jsx("div",{className:"page-icon",children:e.jsx(E,{})}),e.jsx("div",{className:"page-details",children:e.jsx("span",{className:"page-title",children:s.title})})]})},{key:"slug",label:"URL Slug",render:s=>e.jsxs("code",{className:"url-slug",children:["/",s.slug]})},{key:"status",label:"Status",render:s=>e.jsxs("div",{className:"status-controls",children:[e.jsx("span",{className:P(s.status),children:s.status}),e.jsx("button",{className:"toggle-btn",onClick:()=>{const c=s.status==="published"?"draft":"published";i(k({id:s._id||s.id,status:c})),i(S({id:Date.now(),type:"cms_status_change",description:`CMS page ${c}: ${s.title}`,timestamp:new Date().toISOString(),user:"Admin"}))},title:`${s.status==="published"?"Unpublish":"Publish"} page`,children:s.status==="published"?e.jsx(se,{}):e.jsx(te,{})})]})},{key:"lastModified",label:"Last Modified",render:s=>w(s.updatedAt||s.lastModified||s.createdAt)},{key:"actions",label:"Actions",render:s=>e.jsx(ue,{item:s,onView:()=>o(s,"view"),onEdit:()=>o(s,"edit"),onDelete:()=>o(s,"delete"),tooltips:{view:"View Page",edit:"Edit Page",delete:"Delete Page"}}),className:"actions-column"}];return e.jsx(ce,{children:e.jsxs("div",{className:"AdminCMSPages",children:[e.jsxs("div",{className:"flex space-between gap-10",children:[e.jsxs("div",{className:"AdminUserManagement__main",children:[e.jsx("div",{className:"AdminCMSPages__header",children:e.jsx("div",{className:"header-left",children:e.jsxs("div",{className:"search-container",children:[e.jsx(ae,{className:"search-icon"}),e.jsx("input",{type:"text",placeholder:"Search pages by title or slug...",value:C,onChange:s=>m(s.target.value),className:"search-input"})]})})}),e.jsxs("div",{className:"AdminCMSPages__filters",children:[e.jsx("div",{className:"filter-group",children:e.jsxs("select",{value:b,onChange:s=>D(s.target.value),className:"filter-select",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"published",children:"Published"}),e.jsx("option",{value:"draft",children:"Draft"})]})}),l.length>0&&e.jsxs("div",{className:"bulk-actions",children:[e.jsxs("span",{className:"selected-count",children:[l.length," selected"]}),e.jsxs("button",{className:"btn btn-success",onClick:()=>j("publish"),children:[e.jsx(ie,{}),"Publish"]}),e.jsx("button",{className:"btn btn-outline",onClick:()=>j("unpublish"),children:"Unpublish"}),e.jsx("button",{className:"btn btn-danger",onClick:()=>j("delete"),children:"Delete"})]})]})]}),e.jsx("div",{className:"header-right",children:e.jsxs("button",{className:"btn btn-primary",onClick:g,children:[e.jsx(ne,{}),"Create New Page"]})})]}),e.jsx("div",{className:"AdminCMSPages__table",children:e.jsx(oe,{columns:f,data:h,isAdmin:!0,loading:{isLoading:x.cmsPages,message:"Loading pages..."},emptyMessage:e.jsxs("div",{className:"no-results",children:[e.jsx(E,{className:"no-results-icon"}),e.jsx("h3",{children:"No pages found"}),e.jsx("p",{children:N.cmsPages?`Error: ${N.cmsPages}`:"Try adjusting your search or filter criteria"})]}),className:"pages-table"})}),e.jsxs("div",{className:"AdminCMSPages__pagination",children:[e.jsxs("div",{className:"pagination-info",children:["Showing ",h.length," of ",Array.isArray(n)?n.length:0," pages"]}),e.jsxs("div",{className:"pagination-controls",children:[e.jsx("button",{className:"btn btn-outline",disabled:!0,children:"Previous"}),e.jsx("span",{className:"page-number active",children:"1"}),e.jsx("button",{className:"btn btn-outline",disabled:!0,children:"Next"})]})]}),a.showCMSEditorModal&&e.jsx(me,{})]})})};export{je as default};
