import{b as ds,d as S,d1 as fs,b4 as ms,bn as hs,r as m,j as s,b5 as X,cO as J,aB as us,d2 as xs,d3 as ls,cP as Ns,d4 as ns,d5 as ps,a8 as T,d6 as L,d7 as G,aW as Z,aZ as R,a6 as K,b9 as y,ba as vs,aK as gs,d8 as bs,d9 as ws,da as ys,db as Cs,cX as Ds,dc as Ss,bt as As,cI as _s,dd as C,de as ts,bi as D,cT as cs,cS as os,df as rs}from"./index-ctFdmWBt.js";import{A as ks}from"./AdminLayout-D3bHW2Uz.js";import{D as Ps}from"./DocumentViewer-CrWHT6xN.js";import{T as Fs}from"./Table-j5pMA9qi.js";import{A as Us}from"./AdminTableActions-BuK3aIxJ.js";import"./SimplePDFViewer-RiyMKPYj.js";const Bs=()=>{var v,q,H,z,V,W,F,U,e,t,l,r,N,g,b,w,d,h,u,B,$;const i=ds(),a=S(fs);S(ms);const n=S(hs),[Q,A]=m.useState(!1),[_,k]=m.useState(!1),[I,P]=m.useState({}),[ss,E]=m.useState(!1);if(!a||!n.showContentDetailModal)return null;a.viewOnly;const x=()=>{var f;if(!a.fileUrl)return!1;const c=["mp4","avi","mov","wmv","flv","webm","mkv"],j=(f=a.fileUrl.split(".").pop())==null?void 0:f.toLowerCase();return c.includes(j)},o=c=>new Date(c).toLocaleString(),O=()=>{i(ws()),A(!1),P({})},p=c=>{const j={Published:{class:"status-approved",label:"Published"},Draft:{class:"status-draft",label:"Draft"},pending:{class:"status-pending",label:"Pending Review"},rejected:{class:"status-rejected",label:"Rejected"}},f=j[c]||j.Draft;return s.jsx("span",{className:`status-badge ${f.class}`,children:f.label})};return s.jsxs("div",{className:"ContentDetailModal",children:[s.jsx("div",{className:"ContentDetailModal__overlay",onClick:O}),s.jsxs("div",{className:"ContentDetailModal__container",children:[s.jsxs("div",{className:"ContentDetailModal__header",children:[s.jsxs("div",{className:"header-content",children:[s.jsx("div",{className:"content-thumbnail",children:a!=null&&a.thumbnailUrl?s.jsx("img",{src:X+a.thumbnailUrl,alt:(a==null?void 0:a.title)||"Content"}):s.jsx(J,{})}),s.jsxs("div",{className:"content-basic-info",children:[s.jsx("h2",{children:(a==null?void 0:a.title)||"Untitled Content"}),s.jsxs("div",{className:"content-badges",children:[p(a==null?void 0:a.status),s.jsx("span",{className:"category-badge",children:(a==null?void 0:a.category)||"Uncategorized"}),s.jsx("span",{className:"type-badge",children:(a==null?void 0:a.contentType)||"Unknown Type"})]})]})]}),s.jsx("button",{className:"close-btn",onClick:O,children:s.jsx(us,{})})]}),s.jsx("div",{className:"ContentDetailModal__preview-section",children:a.fileUrl&&s.jsx(s.Fragment,{children:x()?s.jsx("div",{className:"ContentDetailModal__video-container",children:s.jsxs("video",{className:"ContentDetailModal__video",controls:!0,autoPlay:!1,controlsList:"nodownload noremoteplayback",disablePictureInPicture:!0,onPlay:()=>E(!0),onPause:()=>E(!1),style:{width:"100%",maxHeight:"500px"},children:[s.jsx("source",{src:X+a.fileUrl,type:"video/mp4"}),"Your browser does not support the video tag."]})}):s.jsx("div",{className:"ContentDetailModal__document-container",children:s.jsx(Ps,{fileUrl:X+a.fileUrl,fileName:a.fileUrl.split("/").pop(),title:a.title,height:"500px",showDownload:!1})})})}),s.jsxs("div",{className:"ContentDetailModal__content",children:[s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Basic Information"}),s.jsxs("div",{className:"info-grid",children:[s.jsxs("div",{className:"info-item",children:[s.jsx(xs,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Description"}),s.jsx("span",{className:"info-value",dangerouslySetInnerHTML:{__html:a.description}})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(ls,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Coach Name"}),s.jsx("span",{className:"info-value",children:a.coachName})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(Ns,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Sport"}),s.jsx("span",{className:"info-value",children:a.sport})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(ns,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Difficulty"}),s.jsx("span",{className:"info-value",children:a.difficulty})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(ps,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Language"}),s.jsx("span",{className:"info-value",children:a.language})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(T,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Content Type"}),s.jsx("span",{className:"info-value",children:a.contentType})]})]})]})]}),(a.aboutCoach||a.strategicContent)&&s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Coach Information"}),s.jsxs("div",{className:"info-grid",children:[a.aboutCoach&&s.jsxs("div",{className:"info-item full-width",children:[s.jsx(ls,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"About Coach"}),s.jsx("span",{className:"info-value",dangerouslySetInnerHTML:{__html:a.aboutCoach}})]})]}),a.strategicContent&&s.jsxs("div",{className:"info-item full-width",children:[s.jsx(T,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Strategic Content"}),s.jsx("span",{className:"info-value",dangerouslySetInnerHTML:{__html:a.strategicContent}})]})]})]})]}),s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"File Information"}),s.jsxs("div",{className:"info-grid",children:[s.jsxs("div",{className:"info-item",children:[s.jsx(T,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"File Size"}),s.jsx("span",{className:"info-value",children:a.fileSize?`${(a.fileSize/1024/1024).toFixed(2)} MB`:"N/A"})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(L,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Preview Status"}),s.jsx("span",{className:"info-value",children:a.previewStatus||"N/A"})]})]}),a.duration&&s.jsxs("div",{className:"info-item",children:[s.jsx(G,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Duration"}),s.jsx("span",{className:"info-value",children:a.duration})]})]}),a.videoLength&&s.jsxs("div",{className:"info-item",children:[s.jsx(G,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Video Length"}),s.jsx("span",{className:"info-value",children:a.videoLength})]})]})]})]}),s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Seller Information"}),s.jsxs("div",{className:"info-grid",children:[s.jsxs("div",{className:"info-item",children:[s.jsx(Z,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Seller Name"}),s.jsx("span",{className:"info-value",children:((v=a.seller)==null?void 0:v.fullName)||`${(q=a.seller)==null?void 0:q.firstName} ${(H=a.seller)==null?void 0:H.lastName}`.trim()||"Unknown"})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(Z,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Seller Email"}),s.jsx("span",{className:"info-value",children:((z=a.seller)==null?void 0:z.email)||"N/A"})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(Z,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Seller ID"}),s.jsx("span",{className:"info-value",children:((V=a.seller)==null?void 0:V.id)||((W=a.seller)==null?void 0:W._id)||"N/A"})]})]})]})]}),s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Pricing & Sale Information"}),s.jsxs("div",{className:"info-grid",children:[s.jsxs("div",{className:"info-item",children:[s.jsx(R,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Price"}),s.jsxs("span",{className:"info-value",children:["$",a.price||0]})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(K,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Sale Type"}),s.jsx("span",{className:"info-value",children:a.saleType})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(L,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Sold Status"}),s.jsx("span",{className:"info-value",children:a.isSold?"Yes":"No"})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(L,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Custom Requests"}),s.jsx("span",{className:"info-value",children:a.allowCustomRequests?"Allowed":"Not Allowed"})]})]}),a.customRequestPrice&&s.jsxs("div",{className:"info-item",children:[s.jsx(R,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Custom Request Price"}),s.jsxs("span",{className:"info-value",children:["$",a.customRequestPrice]})]})]})]})]}),(((F=a.tags)==null?void 0:F.length)>0||((U=a.prerequisites)==null?void 0:U.length)>0||((e=a.learningObjectives)==null?void 0:e.length)>0||((t=a.equipment)==null?void 0:t.length)>0)&&s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Metadata"}),s.jsxs("div",{className:"info-grid",children:[((l=a.tags)==null?void 0:l.length)>0&&s.jsxs("div",{className:"info-item full-width",children:[s.jsx(L,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Tags"}),s.jsx("span",{className:"info-value",children:a.tags.join(", ")})]})]}),((r=a.prerequisites)==null?void 0:r.length)>0&&s.jsxs("div",{className:"info-item full-width",children:[s.jsx(T,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Prerequisites"}),s.jsx("span",{className:"info-value",children:a.prerequisites.join(", ")})]})]}),((N=a.learningObjectives)==null?void 0:N.length)>0&&s.jsxs("div",{className:"info-item full-width",children:[s.jsx(T,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Learning Objectives"}),s.jsx("span",{className:"info-value",children:a.learningObjectives.join(", ")})]})]}),((g=a.equipment)==null?void 0:g.length)>0&&s.jsxs("div",{className:"info-item full-width",children:[s.jsx(ns,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Equipment"}),s.jsx("span",{className:"info-value",children:a.equipment.join(", ")})]})]})]})]}),s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Important Dates"}),s.jsxs("div",{className:"info-grid",children:[s.jsxs("div",{className:"info-item",children:[s.jsx(y,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Created Date"}),s.jsx("span",{className:"info-value",children:o(a.createdAt)})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(y,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Upload Date"}),s.jsx("span",{className:"info-value",children:o(a.uploadDate)})]})]}),a.publishedDate&&s.jsxs("div",{className:"info-item",children:[s.jsx(y,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Published Date"}),s.jsx("span",{className:"info-value",children:o(a.publishedDate)})]})]}),a.lastUpdated&&s.jsxs("div",{className:"info-item",children:[s.jsx(y,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Last Updated"}),s.jsx("span",{className:"info-value",children:o(a.lastUpdated)})]})]}),a.soldAt&&s.jsxs("div",{className:"info-item",children:[s.jsx(y,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Sold Date"}),s.jsx("span",{className:"info-value",children:o(a.soldAt)})]})]}),a.auctionEndedAt&&s.jsxs("div",{className:"info-item",children:[s.jsx(y,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Auction Ended"}),s.jsx("span",{className:"info-value",children:o(a.auctionEndedAt)})]})]})]})]}),(a.winningBidId||a.winningOfferId)&&s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Auction Results"}),s.jsxs("div",{className:"info-grid",children:[a.winningBidId&&s.jsxs("div",{className:"info-item",children:[s.jsx(K,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Winning Bid ID"}),s.jsx("span",{className:"info-value",children:a.winningBidId})]})]}),a.winningOfferId&&s.jsxs("div",{className:"info-item",children:[s.jsx(K,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Winning Offer ID"}),s.jsx("span",{className:"info-value",children:a.winningOfferId})]})]})]})]}),a.saleType==="Auction"&&s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Auction Details"}),s.jsxs("div",{className:"info-grid",children:[s.jsxs("div",{className:"info-item",children:[s.jsx(K,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Auction Status"}),s.jsx("span",{className:`info-value status-${(b=a.auctionStatus)==null?void 0:b.toLowerCase()}`,children:a.auctionStatus})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(R,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Base Price"}),s.jsxs("span",{className:"info-value",children:["$",((w=a.auctionDetails)==null?void 0:w.basePrice)||0]})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(R,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Minimum Bid Increment"}),s.jsxs("span",{className:"info-value",children:["$",((d=a.auctionDetails)==null?void 0:d.minimumBidIncrement)||0]})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(G,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Start Date"}),s.jsx("span",{className:"info-value",children:o((h=a.auctionDetails)==null?void 0:h.auctionStartDate)})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(G,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"End Date"}),s.jsx("span",{className:"info-value",children:o((u=a.auctionDetails)==null?void 0:u.auctionEndDate)})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(L,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Allow Pre-auction Offers"}),s.jsx("span",{className:"info-value",children:(B=a.auctionDetails)!=null&&B.allowOfferBeforeAuctionStart?"Yes":"No"})]})]})]})]}),s.jsxs("div",{className:"info-section",children:[s.jsx("h3",{children:"Sales Information"}),s.jsxs("div",{className:"info-grid",children:[s.jsxs("div",{className:"info-item",children:[s.jsx(vs,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Sales Count"}),s.jsx("span",{className:"info-value",children:a.salesCount||0})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(R,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Total Revenue"}),s.jsxs("span",{className:"info-value",children:["$",a.totalRevenue||0]})]})]}),s.jsxs("div",{className:"info-item",children:[s.jsx(gs,{className:"info-icon"}),s.jsxs("div",{children:[s.jsx("span",{className:"info-label",children:"Average Rating"}),s.jsxs("span",{className:"info-value",children:[a.averageRating||0," (",a.reviewCount," reviews)"]})]})]})]})]}),s.jsxs("div",{className:"info-section",children:[s.jsxs("h3",{children:[s.jsx(bs,{className:"section-icon"}),"Status History",s.jsx("button",{className:"toggle-history-btn",onClick:()=>k(!_),children:_?"Hide History":"Show History"})]}),_&&s.jsx("div",{className:"status-history-list",children:($=a.statusHistory)==null?void 0:$.map((c,j)=>s.jsxs("div",{className:"status-history-item",children:[s.jsx("div",{className:"status-badge",children:p(c.status)}),s.jsxs("div",{className:"status-details",children:[s.jsx("div",{className:"status-timestamp",children:o(c.changedAt)}),s.jsx("div",{className:"status-reason",children:c.reason})]})]},c.id))})]})]})]})]})},Os=()=>{var F,U;const i=ds(),a=S(ys),n=S(Cs),Q=S(hs),[A,_]=m.useState(""),[k,I]=m.useState("all"),[P,ss]=m.useState("all");m.useEffect(()=>{i(Ds())},[i]);const E=Ss.useMemo(()=>{const e=new Set((a.data||[]).map(t=>(t==null?void 0:t.category)||"Uncategorized"));return Array.from(e).filter(Boolean)},[a.data]),x=(a.data||[]).filter(e=>{var d,h,u;const t=(e==null?void 0:e.title)||"",l=((d=e==null?void 0:e.seller)==null?void 0:d.fullName)||((h=e==null?void 0:e.seller)==null?void 0:h.firstName)+" "+((u=e==null?void 0:e.seller)==null?void 0:u.lastName)||"Unknown",r=(e==null?void 0:e.category)||"",N=(e==null?void 0:e.status)||"";return(t.toLowerCase().includes(A.toLowerCase())||l.toLowerCase().includes(A.toLowerCase()))&&(k==="all"||r===k)&&(P==="all"||N===P)}),o=e=>{e.target.checked?i(C(x.map(t=>t.id))):i(C([]))},O=e=>{const t=n.includes(e)?n.filter(l=>l!==e):[...n,e];i(C(t))},p=async(e,t)=>{var l,r,N,g,b,w,d,h,u,B,$,c,j,f,es,as,is;switch(t){case"view":const js={id:e.id||e._id,_id:e._id,title:e.title||"Untitled",description:e.description||"",sport:e.sport||"",contentType:e.contentType||"",category:e.category||"Uncategorized",difficulty:e.difficulty||"",coachName:e.coachName||"",aboutCoach:e.aboutCoach||"",strategicContent:e.strategicContent||"",price:e.price||0,status:e.status||"draft",saleType:e.saleType||"Fixed",fileUrl:e.fileUrl||"",previewUrl:e.previewUrl||"",previewStatus:e.previewStatus||"",thumbnailUrl:e.thumbnailUrl||"",duration:e.duration,fileSize:e.fileSize||0,videoLength:e.videoLength,tags:e.tags||[],language:e.language||"English",prerequisites:e.prerequisites||[],learningObjectives:e.learningObjectives||[],equipment:e.equipment||[],seller:{_id:((l=e.seller)==null?void 0:l._id)||"",fullName:((r=e.seller)==null?void 0:r.fullName)||"",firstName:((N=e.seller)==null?void 0:N.firstName)||"",lastName:((g=e.seller)==null?void 0:g.lastName)||"",email:((b=e.seller)==null?void 0:b.email)||"",id:((w=e.seller)==null?void 0:w.id)||((d=e.seller)==null?void 0:d._id)||""},visibility:e.visibility||"Public",isActive:e.isActive,allowCustomRequests:e.allowCustomRequests||!1,customRequestPrice:e.customRequestPrice,isCustomContent:e.isCustomContent||!1,isSold:e.isSold||!1,auctionStatus:e.auctionStatus||"",salesCount:e.salesCount||0,totalRevenue:e.totalRevenue||0,averageRating:e.averageRating||0,reviewCount:e.reviewCount||0,auctionDetails:{basePrice:((h=e.auctionDetails)==null?void 0:h.basePrice)||null,minimumBidIncrement:((u=e.auctionDetails)==null?void 0:u.minimumBidIncrement)||null,auctionStartDate:((B=e.auctionDetails)==null?void 0:B.auctionStartDate)||null,auctionEndDate:(($=e.auctionDetails)==null?void 0:$.auctionEndDate)||null,allowOfferBeforeAuctionStart:((c=e.auctionDetails)==null?void 0:c.allowOfferBeforeAuctionStart)||!1,endTime:((j=e.auctionDetails)==null?void 0:j.endTime)||null},winningBidId:e.winningBidId||null,winningOfferId:e.winningOfferId||null,auctionEndedAt:e.auctionEndedAt||null,soldAt:e.soldAt||null,uploadDate:e.uploadDate||e.createdAt||new Date,createdAt:e.createdAt||new Date,publishedDate:e.publishedDate||null,lastUpdated:e.lastUpdated||null,statusHistory:e.statusHistory||[],__v:e.__v||0,thumbnail:e.thumbnailUrl||e.thumbnail||null,views:e.views||0,isAuction:e.saleType==="Auction",auctionStartDate:((f=e.auctionDetails)==null?void 0:f.auctionStartDate)||null,auctionEndDate:((es=e.auctionDetails)==null?void 0:es.auctionEndDate)||null,startingBid:((as=e.auctionDetails)==null?void 0:as.basePrice)||0,currentBid:e.currentBid||0,minBidIncrement:((is=e.auctionDetails)==null?void 0:is.minimumBidIncrement)||0,highestBidder:e.highestBidder?{fullName:e.highestBidder.fullName||"",firstName:e.highestBidder.firstName||"",lastName:e.highestBidder.lastName||""}:null};i(rs({...js,viewOnly:!0})),console.log("Modal should be visible now");break;case"edit":i(rs(e));break;case"published":if(window.confirm(`Publish "${e.title}"?`))try{await i(os({id:e.id,approvalNotes:"Individual approval"})).unwrap(),i(D({id:Date.now(),type:"content_approval",description:`Content published: ${e.title}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`Content "${e.title}" has been published!`)}catch(M){console.error("Failed to publish content:",M),alert("Failed to publish content. Please try again.")}break;case"draft":const Y=prompt(`Reason for setting "${e.title}" to draft:`);if(Y)try{await i(cs({id:e.id,reason:Y,rejectionNotes:Y})).unwrap(),i(D({id:Date.now(),type:"content_rejection",description:`Content set to draft: ${e.title} - Reason: ${Y}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`Content "${e.title}" has been set to draft.`)}catch(M){console.error("Failed to set content to draft:",M),alert("Failed to set content to draft. Please try again.")}break;case"delete":if(window.confirm(`Delete "${e.title}"? This action cannot be undone.`))try{await i(ts(e.id)).unwrap(),i(D({id:Date.now(),type:"content_deletion",description:`Content deleted: ${e.title}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`Content "${e.title}" has been deleted!`)}catch(M){console.error("Failed to delete content:",M),alert("Failed to delete content. Please try again.")}break}},v=async e=>{if(n.length===0){alert("Please select content first");return}switch(e){case"Published":if(window.confirm(`Publish ${n.length} selected items?`))try{await Promise.all(n.map(l=>i(os({id:l,approvalNotes:"Bulk approval"})).unwrap())),i(D({id:Date.now(),type:"bulk_content_approval",description:`Bulk published ${n.length} content items`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`${n.length} items published`),i(C([]))}catch(l){console.error("Failed to publish content:",l),alert("Failed to publish some content items. Please try again.")}break;case"draft":const t=prompt(`Reason for setting ${n.length} items to draft:`);if(t)try{await Promise.all(n.map(l=>i(cs({id:l,reason:t,rejectionNotes:t})).unwrap())),i(D({id:Date.now(),type:"bulk_content_rejection",description:`Bulk set to draft ${n.length} content items - Reason: ${t}`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`${n.length} items set to draft`),i(C([]))}catch(l){console.error("Failed to set content to draft:",l),alert("Failed to set some content items to draft. Please try again.")}break;case"delete":if(window.confirm(`Delete ${n.length} selected items? This action cannot be undone.`))try{await Promise.all(n.map(l=>i(ts(l)).unwrap())),i(D({id:Date.now(),type:"bulk_content_deletion",description:`Bulk deleted ${n.length} content items`,timestamp:new Date().toISOString(),user:"Admin"})),alert(`${n.length} items deleted`),i(C([]))}catch(l){console.error("Failed to delete content:",l),alert("Failed to delete some content items. Please try again.")}break}},q=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),H=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),z=e=>s.jsx("div",{className:"status-controls",children:s.jsx("span",{className:`status-badge status-${e.status.toLowerCase()}`,children:e.status})}),V=e=>{switch(e){case"Training Videos":return"category-badge training";case"Courses":return"category-badge courses";case"E-books":return"category-badge ebooks";case"Live Sessions":return"category-badge live";default:return"category-badge"}},W=[{key:"select",label:s.jsx("input",{type:"checkbox",onChange:o,checked:n.length===x.length&&x.length>0}),render:e=>s.jsx("input",{type:"checkbox",checked:n.includes(e.id),onChange:()=>O(e.id)}),className:"select-column"},{key:"content",label:"Content",render:e=>s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:e!=null&&e.thumbnailUrl?s.jsx("img",{src:X+e.thumbnailUrl,alt:(e==null?void 0:e.title)||"Content"}):s.jsx(J,{})}),s.jsx("div",{className:"content-details",children:s.jsx("span",{className:"content-title",children:(e==null?void 0:e.title)||"Untitled"})})]})},{key:"seller",label:"Seller",render:e=>{var t,l,r;return((t=e==null?void 0:e.seller)==null?void 0:t.fullName)||`${((l=e==null?void 0:e.seller)==null?void 0:l.firstName)||""} ${((r=e==null?void 0:e.seller)==null?void 0:r.lastName)||""}`.trim()||"Unknown"}},{key:"category",label:"Category",render:e=>s.jsx("span",{className:V(e==null?void 0:e.category),children:(e==null?void 0:e.category)||"Uncategorized"})},{key:"price",label:"Price",render:e=>q((e==null?void 0:e.price)||0)},{key:"status",label:"Status",render:e=>z(e)},{key:"uploadDate",label:"Upload Date",render:e=>H((e==null?void 0:e.uploadDate)||new Date)},{key:"actions",label:"Actions",render:e=>s.jsx(Us,{item:e,onView:()=>p(e,"view"),onEdit:()=>p(e,"edit"),onDelete:()=>p(e,"delete"),permissions:{view:!0,edit:!0,delete:!0},tooltips:{view:"View Content",edit:"Edit Content",delete:"Delete Content"}}),className:"actions-column"}];return s.jsxs(ks,{children:[s.jsxs("div",{className:"AdminContentManagement",children:[s.jsxs("div",{className:"AdminUserManagement__main",children:[s.jsx("div",{className:"AdminContentManagement__header",children:s.jsx("div",{className:"header-left",children:s.jsxs("div",{className:"search-container",children:[s.jsx(As,{className:"search-icon"}),s.jsx("input",{type:"text",placeholder:"Search content by title or seller...",value:A,onChange:e=>_(e.target.value),className:"search-input"})]})})}),s.jsxs("div",{className:"AdminContentManagement__filters",children:[s.jsx("div",{className:"filter-group",children:s.jsxs("select",{value:k,onChange:e=>I(e.target.value),className:"filter-select",children:[s.jsx("option",{value:"all",children:"All Categories"}),E.map(e=>s.jsx("option",{value:e,children:e},e))]})}),s.jsx("div",{className:"filter-group",children:s.jsxs("select",{value:P,onChange:e=>ss(e.target.value),className:"filter-select",children:[s.jsx("option",{value:"all",children:"All Status"}),s.jsx("option",{value:"Published",children:"Published"}),s.jsx("option",{value:"Draft",children:"Draft"})]})}),n.length>0&&s.jsxs("div",{className:"bulk-actions",children:[s.jsxs("span",{className:"selected-count",children:[n.length," selected"]}),s.jsxs("button",{className:"btn btn-success",onClick:()=>v("Published"),children:[s.jsx(_s,{}),"Approve"]}),s.jsxs("button",{className:"btn btn-warning",onClick:()=>v("draft"),children:[s.jsx(us,{}),"Reject"]}),s.jsx("button",{className:"btn btn-danger",onClick:()=>v("delete"),children:"Delete"})]})]})]}),s.jsx("div",{className:"AdminContentManagement__table",children:s.jsx(Fs,{columns:W,data:x,isAdmin:!0,loading:{isLoading:(F=Q.loading)==null?void 0:F.content,message:"Loading content..."},emptyMessage:s.jsxs("div",{className:"no-results",children:[s.jsx(J,{className:"no-results-icon"}),s.jsx("h3",{children:"No content found"}),s.jsx("p",{children:"Try adjusting your search or filter criteria"})]}),className:"content-table"})}),s.jsxs("div",{className:"AdminContentManagement__pagination",children:[s.jsxs("div",{className:"pagination-info",children:["Showing ",x.length," of ",((U=a.data)==null?void 0:U.length)||0," ","content items"]}),s.jsxs("div",{className:"pagination-controls",children:[s.jsx("button",{className:"btn btn-outline",disabled:!0,children:"Previous"}),s.jsx("span",{className:"page-number active",children:"1"}),s.jsx("button",{className:"btn btn-outline",disabled:!0,children:"Next"})]})]})]}),s.jsx(Bs,{})]})};export{Os as default};
