import{b as L,d as c,cN as B,b4 as F,r as C,j as e,aB as A,b5 as k,cO as E,aW as S,cP as V,b9 as $,aZ as I,cI as P,cQ as J,cR as g,cS as y,bi as D,cT as q,c as O,bL as W,cU as Z,cV as z,bl as G,bn as Q,bD as X,cW as Y,bo as H,cX as K,cY as R,cZ as ee,bq as U,bp as _,c_ as se,by as w,aV as T,c$ as b,d0 as ae,bv as M}from"./index-ctFdmWBt.js";import{A as te}from"./AdminLayout-D3bHW2Uz.js";import{U as ne}from"./UserDetailModal-30vfmYkc.js";const ie=()=>{const a=L(),t=c(B),n=c(F),[l,x]=C.useState(""),[r,o]=C.useState(""),[m,d]=C.useState(null);if(!t)return null;const h=()=>{a(J()),x(""),o(""),d(null)},f=async()=>{if(!l.trim()){alert("Please provide an approval reason");return}try{a(g(!0)),await a(y({id:t.id,approvalNotes:l})).unwrap(),a(D({id:Date.now(),type:"content_approval",description:`Content approved: ${t.contentTitle}`,timestamp:new Date().toISOString(),user:"Admin"})),h(),alert(`Content "${t.contentTitle}" has been approved successfully!`)}catch(i){console.error("Failed to approve content:",i),alert("Failed to approve content. Please try again.")}finally{a(g(!1))}},v=async()=>{if(!r.trim()){alert("Please provide a rejection reason");return}try{a(g(!0)),await a(q({id:t.id,reason:r,rejectionNotes:r})).unwrap(),a(D({id:Date.now(),type:"content_rejection",description:`Content rejected: ${t.contentTitle}`,timestamp:new Date().toISOString(),user:"Admin"})),h(),alert(`Content "${t.contentTitle}" has been rejected.`)}catch(i){console.error("Failed to reject content:",i),alert("Failed to reject content. Please try again.")}finally{a(g(!1))}},p=i=>new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return e.jsxs("div",{className:"ApprovalModal",children:[e.jsx("div",{className:"ApprovalModal__overlay",onClick:h}),e.jsxs("div",{className:"ApprovalModal__container",children:[e.jsxs("div",{className:"ApprovalModal__header",children:[e.jsx("h2",{children:"Content Approval Review"}),e.jsx("button",{className:"close-btn",onClick:h,children:e.jsx(A,{})})]}),e.jsxs("div",{className:"ApprovalModal__content",children:[e.jsxs("div",{className:"content-preview",children:[e.jsx("div",{className:"content-thumbnail",children:t.thumbnailUrl?e.jsx("img",{src:k+t.thumbnailUrl,alt:t.contentTitle}):e.jsx(E,{})}),e.jsxs("div",{className:"content-details",children:[e.jsx("h3",{children:t.contentTitle}),t.contentTitle,e.jsxs("div",{className:"content-meta",children:[e.jsxs("div",{className:"meta-item",children:[e.jsx(S,{className:"meta-icon"}),e.jsxs("span",{children:["Seller: ",t.seller]})]}),e.jsxs("div",{className:"meta-item",children:[e.jsx(V,{className:"meta-icon"}),e.jsxs("span",{children:["Category: ",t.category]})]}),e.jsxs("div",{className:"meta-item",children:[e.jsx($,{className:"meta-icon"}),e.jsxs("span",{children:["Submitted: ",p(t.submissionDate)]})]}),e.jsxs("div",{className:"meta-item",children:[e.jsx(I,{className:"meta-icon"}),e.jsx("span",{children:"Price: $29.99"})]})]})]})]}),e.jsxs("div",{className:"content-description",children:[e.jsx("h4",{children:"Content Description"}),e.jsxs("p",{children:["This comprehensive training content covers advanced techniques and strategies for ",t.category.toLowerCase(),". The content includes detailed explanations, practical examples, and step-by-step guidance to help users improve their skills."]})]}),e.jsxs("div",{className:"seller-info",children:[e.jsx("h4",{children:"Seller Information"}),e.jsxs("div",{className:"seller-details",children:[e.jsx("div",{className:"seller-avatar",children:e.jsx(S,{})}),e.jsxs("div",{className:"seller-data",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Name:"})," ",t.seller]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Rating:"})," 4.8/5 (124 reviews)"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Total Content:"})," 23 items"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Member Since:"})," January 2023"]})]})]})]}),e.jsxs("div",{className:"approval-actions",children:[!m&&e.jsxs("div",{className:"action-buttons",children:[e.jsxs("button",{className:"btn btn-approve",onClick:()=>d("approve"),disabled:n.approval,children:[e.jsx(P,{}),"Approve Content"]}),e.jsxs("button",{className:"btn btn-reject",onClick:()=>d("reject"),disabled:n.approval,children:[e.jsx(A,{}),"Reject Content"]})]}),m==="approve"&&e.jsxs("div",{className:"approval-form",children:[e.jsx("h4",{children:"Approve Content"}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Approval Reason/Notes"}),e.jsx("textarea",{value:l,onChange:i=>x(i.target.value),placeholder:"Provide reason for approval and any notes for the seller...",rows:"4",className:"form-textarea"})]}),e.jsxs("div",{className:"form-actions",children:[e.jsx("button",{className:"btn btn-approve",onClick:f,disabled:n.approval||!l.trim(),children:n.approval?"Approving...":"Confirm Approval"}),e.jsx("button",{className:"btn btn-outline",onClick:()=>d(null),disabled:n.approval,children:"Cancel"})]})]}),m==="reject"&&e.jsxs("div",{className:"rejection-form",children:[e.jsx("h4",{children:"Reject Content"}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{children:["Rejection Reason ",e.jsx("span",{className:"required",children:"*"})]}),e.jsx("textarea",{value:r,onChange:i=>o(i.target.value),placeholder:"Provide detailed reason for rejection. This will be sent to the seller...",rows:"4",className:"form-textarea"})]}),e.jsxs("div",{className:"form-actions",children:[e.jsx("button",{className:"btn btn-reject",onClick:v,disabled:n.approval||!r.trim(),children:n.approval?"Rejecting...":"Confirm Rejection"}),e.jsx("button",{className:"btn btn-outline",onClick:()=>d(null),disabled:n.approval,children:"Cancel"})]})]})]})]})]})]})},oe=()=>{const a=L(),t=O(),n=c(W),l=c(Z);c(z);const x=c(G),r=c(Q),o=c(F),m=c(X);C.useEffect(()=>{a(Y()),a(H()),a(K()),a(R()),a(ee())},[a]);const d=(x.data||[]).filter(s=>s.activeRole==="buyer").slice(0,5),h=(x.data||[]).filter(s=>s.activeRole==="seller").slice(0,5),f=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s),v=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),p=s=>{switch(s){case"active":return"status-badge active";case"inactive":return"status-badge inactive";case"pending":return"status-badge pending";default:return"status-badge"}},i=s=>{switch(s){case"buyers":case"sellers":a(b("users")),t("/admin/users");break;case"content":a(b("content")),t("/admin/content");break;case"revenue":a(b("reports"));break}},N=(s,j)=>{j==="approve"||j==="reject"?a(ae(s)):j==="view"&&(a(b("content")),t("/admin/content"))},u=(s,j)=>{j==="view"?a(M(s)):j==="edit"&&a(M(s))};return e.jsx(te,{children:e.jsxs("div",{className:"AdminDashboard",children:[e.jsxs("div",{className:"AdminDashboard__metrics",children:[e.jsxs("div",{className:"metric-card buyers clickable",onClick:()=>i("buyers"),title:"Click to view user management",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(U,{})}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:o.stats?"Loading...":((n==null?void 0:n.totalBuyers)||0).toLocaleString()}),e.jsx("div",{className:"metric-label",children:"Total Buyers"})]})]}),e.jsxs("div",{className:"metric-card sellers clickable",onClick:()=>i("sellers"),title:"Click to view user management",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(_,{})}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:o.stats?"Loading...":((n==null?void 0:n.totalSellers)||0).toLocaleString()}),e.jsx("div",{className:"metric-label",children:"Total Sellers"})]})]}),e.jsxs("div",{className:"metric-card content clickable",onClick:()=>i("content"),title:"Click to view content management",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(E,{})}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:o.stats?"Loading...":((n==null?void 0:n.totalContent)||0).toLocaleString()}),e.jsx("div",{className:"metric-label",children:"Total Content"})]})]}),e.jsxs("div",{className:"metric-card revenue clickable",onClick:()=>i("revenue"),title:"Click to view reports & analytics",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(I,{})}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:o.stats?"Loading...":f((n==null?void 0:n.totalRevenue)||0)}),e.jsx("div",{className:"metric-label",children:"Total Revenue"})]})]})]}),e.jsxs("div",{className:"AdminDashboard__section",children:[e.jsx("div",{className:"section-header",children:e.jsxs("h2",{children:[e.jsx(se,{className:"section-icon"}),"Pending Approvals",e.jsx("span",{className:"approval-count",children:(l||[]).length})]})}),e.jsx("div",{className:"approvals-list",children:o.approval?e.jsx("div",{className:"loading-state",children:e.jsx("p",{children:"Loading pending approvals..."})}):m.approval?e.jsxs("div",{className:"error-state",children:[e.jsxs("p",{children:["Error loading approvals: ",m.approval]}),e.jsx("button",{onClick:()=>a(R()),children:"Retry"})]}):(l||[]).length===0?e.jsx("div",{className:"empty-state",children:e.jsx("p",{children:"No pending approvals at the moment."})}):(l||[]).slice(0,6).map(s=>e.jsxs("div",{className:"approval-item",children:[e.jsxs("div",{className:"approval-content clickable",onClick:()=>N(s,"view"),title:"Click to view content details",children:[e.jsx("h4",{children:(s==null?void 0:s.contentTitle)||"Untitled Content"}),e.jsxs("p",{children:["by ",(s==null?void 0:s.seller)||"Unknown Seller"]}),e.jsx("span",{className:"approval-category",children:(s==null?void 0:s.category)||"Uncategorized"}),e.jsxs("span",{className:"approval-date",children:["Submitted: ",v((s==null?void 0:s.submissionDate)||new Date)]})]}),e.jsxs("div",{className:"approval-actions",children:[e.jsxs("button",{className:"btn-approve",onClick:()=>N(s,"approve"),title:"Approve this content",children:[e.jsx(P,{}),"Approve"]}),e.jsxs("button",{className:"btn-reject",onClick:()=>N(s,"reject"),title:"Reject this content",children:[e.jsx(A,{}),"Reject"]}),e.jsxs("button",{className:"btn-view",onClick:()=>N(s,"view"),title:"View content details",children:[e.jsx(w,{}),"View"]})]})]},(s==null?void 0:s.id)||Math.random()))})]}),e.jsxs("div",{className:"AdminDashboard__tables",children:[e.jsxs("div",{className:"AdminDashboard__table-section",children:[e.jsx("div",{className:"table-header",children:e.jsx("h3",{children:"Latest Buyers"})}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{className:"admin-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Name"}),e.jsx("th",{children:"Email"}),e.jsx("th",{children:"Join Date"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:d.map(s=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"user-info",children:[e.jsx("div",{className:"user-avatar",children:s!=null&&s.profileImage?e.jsx("img",{src:k+s.profileImage,alt:(s==null?void 0:s.firstName)||"User"}):e.jsx(U,{})}),e.jsxs("span",{children:[(s==null?void 0:s.firstName)||"Unknown"," ",(s==null?void 0:s.lastName)||""]})]})}),e.jsx("td",{children:(s==null?void 0:s.email)||"No email"}),e.jsx("td",{children:v((s==null?void 0:s.dateJoined)||new Date)}),e.jsx("td",{children:e.jsx("span",{className:p(s==null?void 0:s.status),children:(s==null?void 0:s.status)||"unknown"})}),e.jsx("td",{children:e.jsxs("div",{className:"table-actions",children:[e.jsx("button",{className:"btn-action view",onClick:()=>u(s,"view"),title:"View user details",children:e.jsx(w,{})}),e.jsx("button",{className:"btn-action edit",onClick:()=>u(s,"edit"),title:"Edit user",children:e.jsx(T,{})})]})})]},(s==null?void 0:s.id)||Math.random()))})]})})]}),e.jsxs("div",{className:"AdminDashboard__table-section",children:[e.jsx("div",{className:"table-header",children:e.jsx("h3",{children:"Latest Sellers"})}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{className:"admin-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Name"}),e.jsx("th",{children:"Email"}),e.jsx("th",{children:"Join Date"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:h.map(s=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"user-info",children:[e.jsx("div",{className:"user-avatar",children:s!=null&&s.profileImage?e.jsx("img",{src:k+s.profileImage,alt:(s==null?void 0:s.firstName)||"User"}):e.jsx(_,{})}),e.jsxs("span",{children:[(s==null?void 0:s.firstName)||"Unknown"," ",(s==null?void 0:s.lastName)||""]})]})}),e.jsx("td",{children:(s==null?void 0:s.email)||"No email"}),e.jsx("td",{children:v((s==null?void 0:s.dateJoined)||new Date)}),e.jsx("td",{children:e.jsx("span",{className:p(s==null?void 0:s.status),children:(s==null?void 0:s.status)||"unknown"})}),e.jsx("td",{children:e.jsxs("div",{className:"table-actions",children:[e.jsx("button",{className:"btn-action view",onClick:()=>u(s,"view"),title:"View user details",children:e.jsx(w,{})}),e.jsx("button",{className:"btn-action edit",onClick:()=>u(s,"edit"),title:"Edit user",children:e.jsx(T,{})})]})})]},(s==null?void 0:s.id)||Math.random()))})]})})]})]}),r.showApprovalModal&&e.jsx(ie,{}),r.showUserDetailModal&&e.jsx(ne,{})]})})};export{oe as default};
