import{b as N,c as g,d as h,el as f,j as e,ae as A,em as S,a4 as w,a6 as M,a7 as C,ba as L,en as k,a8 as y,eo as D,ep as O,e8 as p,cd as $,c$ as R,ce as q,bQ as T,r as l,eq as F,ec as B,b5 as v,aW as j,A as E,er as I,es as _}from"./index-ctFdmWBt.js";const U=()=>{const o=N(),a=g(),n=h(f),s=["settings","requests","reports"],i=c=>{if(!s.includes(c))switch(o(R(c)),c){case"dashboard":a("/admin/dashboard");break;case"users":a("/admin/users");break;case"content":a("/admin/content");break;case"bids":a("/admin/bids");break;case"offers":a("/admin/offers");break;case"orders":a("/admin/orders");break;case"reviews":a("/admin/reviews");break;case"cms":a("/admin/cms");break;default:a("/admin/dashboard")}},t=()=>{o(q()),a("/")},d=()=>e.jsx("span",{className:"coming-soon-badge",children:"Coming Soon"});return e.jsx("div",{className:"AdminSidebar",children:e.jsxs("div",{className:"AdminSidebar__container",children:[e.jsxs("div",{className:"AdminSidebar__logo",children:[e.jsx("h3",{children:"XOSportsHub"}),e.jsx("span",{children:"Admin Panel"})]}),e.jsxs("ul",{className:"AdminSidebar__menu",children:[e.jsx("li",{className:`AdminSidebar__item ${n==="dashboard"?"active":""}`,onClick:()=>i("dashboard"),"data-tooltip":"Dashboard Overview",children:e.jsxs("div",{className:"item-content",children:[e.jsx(A,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Dashboard Overview"})]})}),e.jsx("li",{className:`AdminSidebar__item ${n==="users"?"active":""}`,onClick:()=>i("users"),"data-tooltip":"User Management",children:e.jsxs("div",{className:"item-content",children:[e.jsx(S,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"User Management"})]})}),e.jsx("li",{className:`AdminSidebar__item ${n==="content"?"active":""}`,onClick:()=>i("content"),"data-tooltip":"Content Management",children:e.jsxs("div",{className:"item-content",children:[e.jsx(w,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Content Management"})]})}),e.jsx("li",{className:`AdminSidebar__item ${n==="bids"?"active":""}`,onClick:()=>i("bids"),"data-tooltip":"Bid Management",children:e.jsxs("div",{className:"item-content",children:[e.jsx(M,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Bid Management"})]})}),e.jsx("li",{className:`AdminSidebar__item ${n==="offers"?"active":""}`,onClick:()=>i("offers"),"data-tooltip":"Offer Management",children:e.jsxs("div",{className:"item-content",children:[e.jsx(C,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Offer Management"})]})}),e.jsx("li",{className:`AdminSidebar__item ${n==="orders"?"active":""}`,onClick:()=>i("orders"),"data-tooltip":"Order Management",children:e.jsxs("div",{className:"item-content",children:[e.jsx(L,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Order Management"})]})}),e.jsx("li",{className:`AdminSidebar__item ${n==="reviews"?"active":""}`,onClick:()=>i("reviews"),"data-tooltip":"Review Management",children:e.jsxs("div",{className:"item-content",children:[e.jsx(k,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Review Management"})]})}),e.jsx("li",{className:`AdminSidebar__item ${n==="cms"?"active":""}`,onClick:()=>i("cms"),"data-tooltip":"CMS Pages",children:e.jsxs("div",{className:"item-content",children:[e.jsx(y,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"CMS Pages"})]})}),e.jsxs("li",{className:`AdminSidebar__item ${n==="requests"?"active":""} ${s.includes("requests")?"disabled":""}`,onClick:()=>i("requests"),"data-tooltip":"Request Management",children:[e.jsxs("div",{className:"item-content",children:[e.jsx(D,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Request Management"})]}),s.includes("requests")&&e.jsx(d,{})]}),e.jsxs("li",{className:`AdminSidebar__item ${n==="reports"?"active":""} ${s.includes("reports")?"disabled":""}`,onClick:()=>i("reports"),"data-tooltip":"Reports & Analytics",children:[e.jsxs("div",{className:"item-content",children:[e.jsx(O,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Reports & Analytics"})]}),s.includes("reports")&&e.jsx(d,{})]}),e.jsxs("li",{className:`AdminSidebar__item ${n==="settings"?"active":""} ${s.includes("settings")?"disabled":""}`,onClick:()=>i("settings"),"data-tooltip":"Settings",children:[e.jsxs("div",{className:"item-content",children:[e.jsx(p,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Settings"})]}),s.includes("settings")&&e.jsx(d,{})]})]}),e.jsx("div",{className:"AdminSidebar__logout",children:e.jsx("div",{className:"AdminSidebar__item logout-item",onClick:t,"data-tooltip":"Logout",children:e.jsxs("div",{className:"item-content",children:[e.jsx($,{className:"AdminSidebar__icon"}),e.jsx("span",{children:"Logout"})]})})})]})})},H=({onToggleSidebar:o})=>{const a=N(),n=g(),s=h(T),[i,t]=l.useState(!1),[d,c]=l.useState(!1),m=()=>{a(q()),n("/")},b=()=>{n("/admin/profile"),t(!1)},x=()=>{n("/admin/settings"),t(!1)};return e.jsxs("div",{className:"AdminNavbar",children:[e.jsxs("div",{className:"AdminNavbar__container",children:[e.jsxs("div",{className:"AdminNavbar__left",children:[e.jsx("button",{className:"AdminNavbar__toggle",onClick:o,"aria-label":"Toggle Sidebar",children:e.jsx(F,{})}),e.jsx("div",{className:"AdminNavbar__logo",children:e.jsx("img",{src:B,alt:"XO Sports Hub Logo"})})]}),e.jsx("div",{className:"AdminNavbar__right",children:e.jsxs("div",{className:"AdminNavbar__profile",children:[e.jsxs("button",{className:"profile-btn",onClick:()=>t(!i),"aria-label":"Admin Profile",children:[e.jsx("div",{className:"profile-avatar",children:s.profileImage?e.jsx("img",{src:v+s.profileImage,alt:"Admin"}):e.jsx(j,{})}),e.jsxs("div",{className:"profile-info",children:[e.jsxs("span",{className:"profile-name",children:[s.firstName," ",s.lastName]}),e.jsx("span",{className:"profile-role",children:"Administrator"})]})]}),i&&e.jsxs("div",{className:"profile-dropdown",children:[e.jsxs("div",{className:"profile-dropdown-header",children:[e.jsx("div",{className:"profile-avatar large",children:s.profileImage?e.jsx("img",{src:v+s.profileImage,alt:"Admin"}):e.jsx(j,{})}),e.jsxs("div",{className:"profile-details",children:[e.jsxs("h4",{children:[s.firstName," ",s.lastName]}),e.jsx("p",{children:s.email})]})]}),e.jsxs("div",{className:"profile-dropdown-menu",children:[e.jsxs("button",{className:"dropdown-item",onClick:b,children:[e.jsx(j,{className:"dropdown-icon"}),"View Profile"]}),e.jsxs("button",{className:"dropdown-item",onClick:x,children:[e.jsx(p,{className:"dropdown-icon"}),"Settings"]}),e.jsx("hr",{className:"dropdown-divider"}),e.jsxs("button",{className:"dropdown-item logout",onClick:m,children:[e.jsx($,{className:"dropdown-icon"}),"Logout"]})]})]})]})})]}),(i||d)&&e.jsx("div",{className:"AdminNavbar__overlay",onClick:()=>{t(!1),c(!1)}})]})},z=({children:o})=>{const a=N(),n=E(),s=h(f),i=h(I),[t,d]=l.useState(!1),c={"/admin/dashboard":"dashboard","/admin/users":"users","/admin/content":"content","/admin/bids":"bids","/admin/offers":"offers","/admin/orders":"orders","/admin/requests":"requests","/admin/reviews":"reviews","/admin/reports":"reports","/admin/cms":"cms","/admin/settings":"settings"},m={dashboard:{title:"Dashboard Overview",icon:e.jsx(A,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard"]},users:{title:"User Management",icon:e.jsx(S,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","User Management"]},content:{title:"Content Management",icon:e.jsx(w,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Content Management"]},bids:{title:"Bid Management",icon:e.jsx(M,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Bid Management"]},offers:{title:"Offer Management",icon:e.jsx(C,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Offer Management"]},orders:{title:"Order Management",icon:e.jsx(L,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Order Management"]},requests:{title:"Request Management",icon:e.jsx(D,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Request Management"]},reviews:{title:"Review Management",icon:e.jsx(k,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Review Management"]},reports:{title:"Reports & Analytics",icon:e.jsx(O,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Reports & Analytics"]},cms:{title:"CMS Pages",icon:e.jsx(y,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","CMS Pages"]},settings:{title:"Settings",icon:e.jsx(p,{className:"AdminLayout__title-icon"}),breadcrumb:["Dashboard","Settings"]}};l.useEffect(()=>{const r=c[n.pathname];r&&r!==s&&a(R(r))},[n.pathname,s,a]);const b=m[s]||m.dashboard,x=()=>{window.innerWidth<=768?a(_(!i)):d(!t)},P=()=>{window.innerWidth<=768&&a(_(!1))};return l.useEffect(()=>{const r=()=>{window.innerWidth>768?(a(_(!1)),window.innerWidth>1024&&d(!1)):d(!1)};return window.addEventListener("resize",r),()=>window.removeEventListener("resize",r)},[a]),e.jsxs("div",{className:"AdminLayout",children:[e.jsx(H,{onToggleSidebar:x,sidebarCollapsed:t}),e.jsxs("div",{className:"AdminLayout__container",children:[e.jsx("div",{className:`AdminLayout__sidebar ${t?"collapsed":""} ${i?"mobile-open":""}`,children:e.jsx(U,{})}),i&&e.jsx("div",{className:"AdminLayout__overlay",onClick:P}),e.jsxs("div",{className:`AdminLayout__main ${t?"sidebar-collapsed":""}`,children:[e.jsx("div",{className:"AdminLayout__breadcrumb",children:e.jsx("nav",{className:"breadcrumb-nav",children:b.breadcrumb.map((r,u)=>e.jsxs("span",{className:"breadcrumb-item",children:[r,u<b.breadcrumb.length-1&&e.jsx("span",{className:"breadcrumb-separator",children:"/"})]},u))})}),e.jsx("div",{className:"AdminLayout__content",children:o})]})]})]})};export{z as A};
