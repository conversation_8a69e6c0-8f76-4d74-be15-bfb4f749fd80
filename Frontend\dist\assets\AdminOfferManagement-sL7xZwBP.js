import{j as e,b5 as _,aW as I,aX as L,aY as M,cI as P,aB as V,bd as G,b as H,d as R,dx as z,dy as J,b4 as K,dz as W,r as j,dA as u,dB as X,dC as Y,bt as q,s as d,dD as Q,bi as x,k as f,dE as T,dF as Z,dG as ee,dH as se,dI as ae,dJ as te,dK as ne}from"./index-ctFdmWBt.js";import{A as le}from"./AdminLayout-D3bHW2Uz.js";import{T as ie}from"./Table-j5pMA9qi.js";import{A as re}from"./AdminTableActions-BuK3aIxJ.js";const ce=({offer:s,onClose:n,onApprove:i,onReject:S,onDelete:O})=>{var A,r,D,C,N,o,v,b,y,U,m,h,$,w,F;const g=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),c=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a);return e.jsx("div",{className:"modal-overlay",children:e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h2",{children:"Offer Details"}),e.jsx("button",{className:"close-button",onClick:n,children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[e.jsxs("section",{className:"detail-section",children:[e.jsx("h3",{children:"Content Information"}),e.jsxs("div",{className:"detail-grid",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Title:"}),e.jsx("span",{children:((A=s.content)==null?void 0:A.title)||"Unknown"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Sport:"}),e.jsx("span",{children:((r=s.content)==null?void 0:r.sport)||"Unknown"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Type:"}),e.jsx("span",{children:((D=s.content)==null?void 0:D.contentType)||"Unknown"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Original Price:"}),e.jsx("span",{children:c(((C=s.content)==null?void 0:C.price)||0)})]})]}),((N=s.content)==null?void 0:N.description)&&e.jsxs("div",{className:"detail-item full-width",children:[e.jsx("label",{children:"Description:"}),e.jsx("div",{className:"content-description",dangerouslySetInnerHTML:{__html:s.content.description}})]})]}),e.jsxs("section",{className:"detail-section",children:[e.jsx("h3",{children:"Buyer Information"}),e.jsxs("div",{className:"user-profile",children:[e.jsx("div",{className:"user-avatar",children:e.jsx("img",{src:_+((o=s.buyer)==null?void 0:o.profileImage)||"/default-profile.jpg",alt:(v=s.buyer)==null?void 0:v.fullName,className:"profile-image"})}),e.jsxs("div",{className:"user-details",children:[e.jsxs("div",{className:"user-header",children:[e.jsx(I,{className:"icon"}),e.jsx("span",{className:"name",children:((b=s.buyer)==null?void 0:b.fullName)||"Unknown User"})]}),e.jsxs("div",{className:"user-contact",children:[e.jsxs("div",{className:"contact-item",children:[e.jsx(L,{className:"icon"}),e.jsx("span",{children:((y=s.buyer)==null?void 0:y.email)||"No email"})]}),e.jsxs("div",{className:"contact-item",children:[e.jsx(M,{className:"icon"}),e.jsx("span",{children:((U=s.buyer)==null?void 0:U.mobile)||"No phone"})]})]})]})]})]}),e.jsxs("section",{className:"detail-section",children:[e.jsx("h3",{children:"Seller Information"}),e.jsxs("div",{className:"user-profile",children:[e.jsx("div",{className:"user-avatar",children:e.jsx("img",{src:_+((m=s.seller)==null?void 0:m.profileImage)||"/default-profile.jpg",alt:(h=s.seller)==null?void 0:h.fullName,className:"profile-image"})}),e.jsxs("div",{className:"user-details",children:[e.jsxs("div",{className:"user-header",children:[e.jsx(I,{className:"icon"}),e.jsx("span",{className:"name",children:(($=s.seller)==null?void 0:$.fullName)||"Unknown User"})]}),e.jsxs("div",{className:"user-contact",children:[e.jsxs("div",{className:"contact-item",children:[e.jsx(L,{className:"icon"}),e.jsx("span",{children:((w=s.seller)==null?void 0:w.email)||"No email"})]}),e.jsxs("div",{className:"contact-item",children:[e.jsx(M,{className:"icon"}),e.jsx("span",{children:((F=s.seller)==null?void 0:F.mobile)||"No phone"})]})]})]})]})]}),e.jsxs("section",{className:"detail-section",children:[e.jsx("h3",{children:"Offer Details"}),e.jsxs("div",{className:"detail-grid",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Offer Amount:"}),e.jsx("span",{className:"highlight",children:c(s.amount||0)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Status:"}),e.jsx("span",{className:`status-badge ${(s.status||"unknown").toLowerCase()}`,children:s.status||"Unknown"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Created:"}),e.jsx("span",{children:g(s.createdAt)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("label",{children:"Expires:"}),e.jsx("span",{children:g(s.expiresAt)})]})]}),e.jsxs("div",{className:"detail-item full-width",children:[e.jsx("label",{children:"Message:"}),e.jsxs("div",{className:"message-box",children:[e.jsxs("div",{className:"message-header",children:[e.jsx(I,{className:"icon"}),e.jsx("span",{children:"Buyer's Message:"})]}),s.message||"No message provided"]}),s.status==="Rejected"&&s.sellerResponse&&e.jsxs("div",{className:"message-box seller-response",children:[e.jsxs("div",{className:"message-header",children:[e.jsx(I,{className:"icon"}),e.jsx("span",{children:"Seller's Response:"})]}),s.sellerResponse]})]})]})]}),e.jsxs("div",{className:"modal-footer",children:[s.status==="Pending"&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{className:"action-btn approve",onClick:i,title:"Approve Offer",children:[e.jsx(P,{})," Approve"]}),e.jsxs("button",{className:"action-btn reject",onClick:S,title:"Reject Offer",children:[e.jsx(V,{})," Reject"]})]}),e.jsxs("button",{className:"action-btn delete",onClick:O,title:"Delete Offer",children:[e.jsx(G,{})," Delete"]}),e.jsx("button",{className:"action-btn cancel",onClick:n,children:"Close"})]})]})})},pe=()=>{const s=H(),n=R(z),i=R(J),S=R(K),O=R(W),g=(n==null?void 0:n.data)||[];console.log("Offers from Redux:",n),console.log("Display offers:",g),console.log("Loading state:",S);const[c,A]=j.useState(O.search||""),[r,D]=j.useState(O.status||"all"),[C,N]=j.useState(!1),[o,v]=j.useState(null),b=a=>{var t;s(u({page:a,limit:((t=n==null?void 0:n.pagination)==null?void 0:t.limit)||10,search:c,status:r!=="all"?r:"",sortBy:"createdAt",sortOrder:"desc"}))};j.useEffect(()=>{s(u({page:1,limit:10,search:"",status:"",sortBy:"createdAt",sortOrder:"desc"})),s(X())},[s]),j.useEffect(()=>{const a=setTimeout(()=>{s(Y({search:c,status:r})),s(u({page:1,limit:10,search:c,status:r!=="all"?r:"",sortBy:"createdAt",sortOrder:"desc"}))},500);return()=>clearTimeout(a)},[s,c,r]);const y=async a=>{try{const t=await s(ne(a._id)).unwrap();v(t),N(!0)}catch(t){console.error("Error fetching offer details:",t),d("Failed to fetch offer details")}},U=()=>{N(!1),v(null)},m=async(a,t)=>{var B;const l=a._id,p=((B=a.content)==null?void 0:B.title)||"Unknown Content";let E;switch(t){case"view":y(a);break;case"approve":if(window.confirm("Approve this offer?"))try{await s(te({id:l,approvalNotes:""})).unwrap(),s(x({type:"offer_approval",description:`Offer approved for ${p}`,timestamp:new Date().toISOString()})),f("Offer has been approved!"),s(u())}catch(k){d(`Failed to approve offer: ${k}`)}break;case"reject":if(E=prompt("Enter rejection reason:"),E)try{await s(ae({id:l,reason:E})).unwrap(),s(x({type:"offer_rejection",description:`Offer rejected for ${p}`,timestamp:new Date().toISOString()})),f("Offer has been rejected"),s(u())}catch(k){d(`Failed to reject offer: ${k}`)}break;case"delete":if(window.confirm("Are you sure you want to delete this offer?"))try{await s(se(l)).unwrap(),s(x({type:"offer_deletion",description:`Offer deleted for ${p}`,timestamp:new Date().toISOString()})),f("Offer has been deleted"),s(u())}catch(k){d(`Failed to delete offer: ${k}`)}break}},h=async a=>{if(i.length===0){d("Please select offers first");return}let t;switch(a){case"approve":if(window.confirm(`Approve ${i.length} selected offers?`))try{await s(ee({offerIds:i,approvalNotes:""})).unwrap(),s(x({id:Date.now(),type:"bulk_offer_approval",description:`Bulk approved ${i.length} offers`,timestamp:new Date().toISOString(),user:"Admin"})),f(`${i.length} offers approved`),s(T([]))}catch(l){d(`Failed to approve offers: ${l}`)}break;case"reject":if(t=prompt(`Reason for rejecting ${i.length} offers:`),t)try{await s(Z({offerIds:i,reason:t})).unwrap(),s(x({id:Date.now(),type:"bulk_offer_rejection",description:`Bulk rejected ${i.length} offers - Reason: ${t}`,timestamp:new Date().toISOString(),user:"Admin"})),f(`${i.length} offers rejected`),s(T([]))}catch(l){d(`Failed to reject offers: ${l}`)}break;case"delete":if(window.confirm(`Delete ${i.length} selected offers? This action cannot be undone.`))try{await s(Q(i)).unwrap(),s(x({id:Date.now(),type:"bulk_offer_deletion",description:`Bulk deleted ${i.length} offers`,timestamp:new Date().toISOString(),user:"Admin"})),f(`${i.length} offers deleted`),s(T([]))}catch(l){d(`Failed to delete offers: ${l}`)}break}},$=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),w=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),F=[{key:"content",label:"Content",render:a=>{var t,l,p;return e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-thumbnail",children:((t=a.content)==null?void 0:t.thumbnailUrl)&&e.jsx("img",{src:`${_}${a.content.thumbnailUrl}`,alt:((l=a.content)==null?void 0:l.title)||"Content"})}),e.jsx("div",{className:"content-details",children:e.jsx("span",{className:"content-title",children:((p=a.content)==null?void 0:p.title)||"Unknown Content"})})]})}},{key:"buyer",label:"Buyer",render:a=>{var t,l;return e.jsxs("div",{className:"user-info",children:[e.jsx("span",{className:"name",children:((t=a.buyer)==null?void 0:t.fullName)||"Unknown User"}),e.jsxs("span",{className:"email",children:["(",((l=a.buyer)==null?void 0:l.email)||"No email",")"]})]})}},{key:"seller",label:"Seller",render:a=>{var t,l;return e.jsxs("div",{className:"user-info flex gap-10",children:[e.jsx("span",{className:"name",children:((t=a.seller)==null?void 0:t.fullName)||"Unknown User"}),e.jsxs("span",{className:"email",children:["(",((l=a.seller)==null?void 0:l.email)||"No email",")"]})]})}},{key:"amount",label:"Amount",render:a=>$(a.amount||0)},{key:"status",label:"Status",render:a=>e.jsx("span",{className:`status-badge ${(a.status||"unknown").toLowerCase()}`,children:a.status||"Unknown"})},{key:"message",label:"Message",render:a=>e.jsx("div",{className:"message-cell",title:a.message||"No message",children:(a.message||"No message").length>30?`${(a.message||"No message").substring(0,30)}...`:a.message||"No message"})},{key:"createdAt",label:"Created",render:a=>w(a.createdAt)},{key:"expiresAt",label:"Expires",render:a=>w(a.expiresAt)},{key:"actions",label:"Actions",render:a=>e.jsx(re,{item:a,onView:()=>y(a),permissions:{view:!0,edit:!1,delete:!1},tooltips:{view:"View Offer Details"}}),className:"actions-column"}];return e.jsxs(le,{children:[e.jsxs("div",{className:"admin-offer-management",children:[e.jsxs("div",{className:"controls-section",children:[e.jsxs("div",{className:"search-box",children:[e.jsx(q,{}),e.jsx("input",{type:"text",placeholder:"Search offers...",value:c,onChange:a=>A(a.target.value)})]}),e.jsx("div",{className:"filter-box",children:e.jsxs("select",{value:r,onChange:a=>D(a.target.value),children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"Pending",children:"Pending"}),e.jsx("option",{value:"Accepted",children:"Accepted"}),e.jsx("option",{value:"Rejected",children:"Rejected"}),e.jsx("option",{value:"Expired",children:"Expired"})]})})]}),i.length>0&&e.jsxs("div",{className:"bulk-actions",children:[e.jsxs("span",{children:[i.length," selected"]}),e.jsx("button",{onClick:()=>h("approve"),className:"approve",title:"Approve Selected",children:e.jsx(P,{})}),e.jsx("button",{onClick:()=>h("reject"),className:"reject",title:"Reject Selected",children:e.jsx(V,{})}),e.jsx("button",{onClick:()=>h("delete"),className:"delete",title:"Delete Selected",children:e.jsx(G,{})})]}),e.jsx(ie,{columns:F,data:g,isAdmin:!0,loading:{isLoading:S.offers,message:"Loading offers..."},emptyMessage:"No offers found",className:"offers-table"}),(n==null?void 0:n.pagination)&&n.pagination.pages>1&&e.jsxs("div",{className:"pagination",children:[e.jsx("button",{disabled:n.pagination.page===1,onClick:()=>b(n.pagination.page-1),children:"Previous"}),e.jsxs("span",{children:["Page ",n.pagination.page," of ",n.pagination.pages]}),e.jsx("button",{disabled:n.pagination.page===n.pagination.pages,onClick:()=>b(n.pagination.page+1),children:"Next"})]})]}),C&&o&&e.jsx(ce,{offer:o,onClose:U,onApprove:()=>m(o,"approve"),onReject:()=>m(o,"reject"),onDelete:()=>m(o,"delete")})]})};export{pe as default};
