import{m as c,r as y,N as S,j as e,ax as B,bM as _,bt as q,aB as V,aW as R,a8 as W,aZ as Z,ba as z,b9 as G,by as H}from"./index-ctFdmWBt.js";import{A as J}from"./AdminLayout-D3bHW2Uz.js";import{T as K}from"./Table-j5pMA9qi.js";const U={getAllOrders:async(t={})=>{const{page:r=1,limit:i=10,search:m="",status:p="",paymentStatus:u="",orderType:l="",sortBy:F="createdAt",sortOrder:h="desc",dateFrom:x="",dateTo:O="",minAmount:g="",maxAmount:o="",buyerId:C="",sellerId:w=""}=t,d=new URLSearchParams;return d.append("page",r),d.append("limit",i),m&&d.append("search",m),p&&p!=="all"&&d.append("status",p),u&&u!=="all"&&d.append("paymentStatus",u),l&&l!=="all"&&d.append("orderType",l),F&&d.append("sortBy",F),h&&d.append("sortOrder",h),x&&d.append("dateFrom",x),O&&d.append("dateTo",O),g&&d.append("minAmount",g),o&&d.append("maxAmount",o),C&&d.append("buyerId",C),w&&d.append("sellerId",w),(await c.get(`/admin/orders?${d.toString()}`)).data},getOrderById:async t=>(await c.get(`/admin/orders/${t}`)).data,updateOrderStatus:async(t,r)=>(await c.put(`/admin/orders/${t}/status`,r)).data,deleteOrder:async t=>(await c.delete(`/admin/orders/${t}`)).data,bulkUpdateOrders:async(t,r)=>(await c.post("/admin/orders/bulk-update",{orderIds:t,status:r})).data,bulkDeleteOrders:async t=>(await c.post("/admin/orders/bulk-delete",{orderIds:t})).data,getOrderStats:async()=>(await c.get("/admin/orders/stats")).data,exportOrders:async(t={})=>{const{format:r="csv",status:i="",dateFrom:m="",dateTo:p=""}=t,u=new URLSearchParams;return u.append("format",r),i&&i!=="all"&&u.append("status",i),m&&u.append("dateFrom",m),p&&u.append("dateTo",p),(await c.get(`/admin/orders/export?${u.toString()}`)).data},processRefund:async(t,r)=>(await c.post(`/admin/orders/${t}/refund`,r)).data,getOrderTimeline:async t=>(await c.get(`/admin/orders/${t}/timeline`)).data,flagOrder:async(t,r)=>(await c.put(`/admin/orders/${t}/flag`,r)).data,unflagOrder:async t=>(await c.put(`/admin/orders/${t}/unflag`)).data,getOrderAnalytics:async(t={})=>{const{period:r="30d",groupBy:i="day"}=t,m=new URLSearchParams;return m.append("period",r),m.append("groupBy",i),(await c.get(`/admin/orders/analytics?${m.toString()}`)).data}},ee=()=>{const[t,r]=y.useState([]),[i,m]=y.useState(!1),[p,u]=y.useState(""),[l,F]=y.useState({status:"all",orderType:"all",paymentStatus:"all",dateFrom:"",dateTo:""}),[h,x]=y.useState({page:1,limit:10,total:0,totalPages:0}),[O,g]=y.useState(!1),[o,C]=y.useState(null),w=s=>s.map(a=>{var n,b,f,j,k,P,E,$;return{id:a._id,orderNumber:`ORD-${a._id.slice(-8).toUpperCase()}`,buyer:{name:`${((n=a.buyer)==null?void 0:n.firstName)||""} ${((b=a.buyer)==null?void 0:b.lastName)||""}`.trim()||"Unknown",email:((f=a.buyer)==null?void 0:f.email)||"N/A"},seller:{name:`${((j=a.seller)==null?void 0:j.firstName)||""} ${((k=a.seller)==null?void 0:k.lastName)||""}`.trim()||"Unknown",email:((P=a.seller)==null?void 0:P.email)||"N/A"},content:{title:((E=a.content)==null?void 0:E.title)||"Unknown Content",type:(($=a.content)==null?void 0:$.contentType)||"Unknown"},orderType:a.orderType,amount:a.amount,platformFee:a.platformFee,sellerEarnings:a.sellerEarnings,status:a.status,paymentStatus:a.paymentStatus,createdAt:a.createdAt,updatedAt:a.updatedAt||a.createdAt,originalOrder:a}});y.useEffect(()=>{d()},[h.page,h.limit,p,l]);const d=async()=>{var s,a;m(!0);try{const n=await U.getAllOrders({page:h.page,limit:h.limit,search:p,status:l.status,paymentStatus:l.paymentStatus,orderType:l.orderType,dateFrom:l.dateFrom,dateTo:l.dateTo});if(n.success){const b=w(n.data);r(b),x(f=>({...f,total:n.pagination.total,totalPages:n.pagination.pages}))}else S.error("Failed to fetch orders")}catch(n){console.error("Error fetching orders:",n),S.error(((a=(s=n.response)==null?void 0:s.data)==null?void 0:a.message)||"Failed to fetch orders")}finally{m(!1)}},T=s=>{u(s),x(a=>({...a,page:1}))},v=(s,a)=>{F(n=>({...n,[s]:a})),x(n=>({...n,page:1}))},D=s=>{C(s),g(!0)},N=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s),L=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),I=async()=>{var s,a;try{const n=await U.exportOrders({format:"csv",status:l.status,dateFrom:l.dateFrom,dateTo:l.dateTo});if(n.success){const b=new Blob([n.data],{type:"text/csv"}),f=window.URL.createObjectURL(b),j=document.createElement("a");j.href=f,j.download=`orders-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(j),j.click(),document.body.removeChild(j),window.URL.revokeObjectURL(f),S.success("Orders exported successfully")}else S.error("Failed to export orders")}catch(n){console.error("Error exporting orders:",n),S.error(((a=(s=n.response)==null?void 0:s.data)==null?void 0:a.message)||"Failed to export orders")}},A=s=>{const a={Completed:"status-completed",Processing:"status-processing",Pending:"status-pending",Cancelled:"status-cancelled",Refunded:"status-refunded"};return e.jsx("span",{className:`status-badge ${a[s]||"status-default"}`,children:s})},M=[{key:"orderNumber",label:"Order #",sortable:!0,render:s=>e.jsxs("div",{className:"order-number",children:[e.jsx(z,{className:"order-icon"}),e.jsx("span",{children:s.orderNumber})]})},{key:"buyer",label:"Buyer",sortable:!0,render:s=>e.jsxs("div",{className:"user-info",children:[e.jsx("div",{className:"user-name",children:s.buyer.name}),e.jsx("div",{className:"user-email",children:s.buyer.email})]})},{key:"seller",label:"Seller",sortable:!0,render:s=>e.jsxs("div",{className:"user-info",children:[e.jsx("div",{className:"user-name",children:s.seller.name}),e.jsx("div",{className:"user-email",children:s.seller.email})]})},{key:"content",label:"Content",render:s=>e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:s.content.title}),e.jsx("div",{className:"content-type",children:s.content.type})]})},{key:"orderType",label:"Type",sortable:!0,render:s=>e.jsx("span",{className:`order-type order-type-${s.orderType.toLowerCase()}`,children:s.orderType})},{key:"amount",label:"Amount",sortable:!0,render:s=>e.jsxs("div",{className:"amount-info",children:[e.jsx("div",{className:"total-amount",children:N(s.amount)}),e.jsxs("div",{className:"platform-fee",children:["Fee: ",N(s.platformFee)]})]})},{key:"status",label:"Status",sortable:!0,render:s=>A(s.status)},{key:"createdAt",label:"Created",sortable:!0,render:s=>e.jsxs("div",{className:"date-info",children:[e.jsx(G,{className:"date-icon"}),e.jsx("span",{children:L(s.createdAt)})]})},{key:"actions",label:"Actions",render:s=>e.jsx("div",{className:"action-buttons",children:e.jsx("button",{className:"btn-icon btn-view",onClick:()=>D(s),title:"View Details",children:e.jsx(H,{})})})}];return e.jsx(J,{children:e.jsxs("div",{className:"AdminOrderManagement",children:[e.jsx("div",{className:"AdminOrderManagement__header",children:e.jsxs("div",{className:"header-content",children:[e.jsxs("div",{className:"header-text",children:[e.jsx("h1",{children:"Order Management"}),e.jsx("p",{children:"Manage and monitor all orders in the system"})]}),e.jsxs("div",{className:"header-actions",children:[e.jsxs("button",{className:"btn btn-secondary",onClick:d,children:[e.jsx(B,{})," Refresh"]}),e.jsxs("button",{className:"btn btn-primary",onClick:I,children:[e.jsx(_,{})," Export"]})]})]})}),e.jsxs("div",{className:"AdminOrderManagement__filters",children:[e.jsx("div",{className:"search-section",children:e.jsxs("div",{className:"search-input",children:[e.jsx(q,{className:"search-icon"}),e.jsx("input",{type:"text",placeholder:"Search orders by number, buyer, seller, or content...",value:p,onChange:s=>T(s.target.value)})]})}),e.jsxs("div",{className:"filter-section",children:[e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:"Status:"}),e.jsxs("select",{value:l.status,onChange:s=>v("status",s.target.value),children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"Pending",children:"Pending"}),e.jsx("option",{value:"Processing",children:"Processing"}),e.jsx("option",{value:"Completed",children:"Completed"}),e.jsx("option",{value:"Cancelled",children:"Cancelled"}),e.jsx("option",{value:"Refunded",children:"Refunded"})]})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:"Order Type:"}),e.jsxs("select",{value:l.orderType,onChange:s=>v("orderType",s.target.value),children:[e.jsx("option",{value:"all",children:"All Types"}),e.jsx("option",{value:"Fixed",children:"Fixed Price"}),e.jsx("option",{value:"Auction",children:"Auction"}),e.jsx("option",{value:"Custom",children:"Custom Request"})]})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:"Payment:"}),e.jsxs("select",{value:l.paymentStatus,onChange:s=>v("paymentStatus",s.target.value),children:[e.jsx("option",{value:"all",children:"All Payments"}),e.jsx("option",{value:"Completed",children:"Completed"}),e.jsx("option",{value:"Pending",children:"Pending"}),e.jsx("option",{value:"Failed",children:"Failed"}),e.jsx("option",{value:"Refunded",children:"Refunded"}),e.jsx("option",{value:"Expired",children:"Expired"})]})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:"Date From:"}),e.jsx("input",{type:"date",value:l.dateFrom,onChange:s=>v("dateFrom",s.target.value)})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:"Date To:"}),e.jsx("input",{type:"date",value:l.dateTo,onChange:s=>v("dateTo",s.target.value)})]})]})]}),e.jsx("div",{className:"AdminOrderManagement__table",children:e.jsx(K,{data:t,columns:M,loading:i,pagination:h,onPageChange:s=>x(a=>({...a,page:s})),onLimitChange:s=>x(a=>({...a,limit:s,page:1})),emptyMessage:"No orders found",selectable:!1})}),O&&o&&e.jsx("div",{className:"modal-overlay",onClick:()=>g(!1),children:e.jsxs("div",{className:"modal-content order-detail-modal",onClick:s=>s.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h3",{children:["Order Details - ",o.orderNumber]}),e.jsx("button",{className:"modal-close",onClick:()=>g(!1),children:e.jsx(V,{})})]}),e.jsx("div",{className:"modal-body",children:e.jsxs("div",{className:"order-detail-grid",children:[e.jsxs("div",{className:"detail-section",children:[e.jsxs("h4",{children:[e.jsx(R,{})," Buyer Information"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Name:"})," ",o.buyer.name]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Email:"})," ",o.buyer.email]})]}),e.jsxs("div",{className:"detail-section",children:[e.jsxs("h4",{children:[e.jsx(R,{})," Seller Information"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Name:"})," ",o.seller.name]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Email:"})," ",o.seller.email]})]}),e.jsxs("div",{className:"detail-section",children:[e.jsxs("h4",{children:[e.jsx(W,{})," Content Information"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Title:"})," ",o.content.title]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Type:"})," ",o.content.type]})]}),e.jsxs("div",{className:"detail-section",children:[e.jsxs("h4",{children:[e.jsx(Z,{})," Payment Information"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Total Amount:"})," ",N(o.amount)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Platform Fee:"})," ",N(o.platformFee)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Seller Earnings:"})," ",N(o.sellerEarnings)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Status:"})," ",A(o.status)]})]})]})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{className:"btn btn-secondary",onClick:()=>g(!1),children:"Close"}),e.jsx("button",{className:"btn btn-primary",children:"Edit Order"})]})]})})]})})};export{ee as default};
