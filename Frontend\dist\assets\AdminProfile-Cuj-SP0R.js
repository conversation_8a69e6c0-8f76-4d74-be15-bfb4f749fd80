import{b as _,d as P,bQ as I,r as d,j as a,b5 as v,aW as A,bR as y,ai as N,bS as b,b1 as S,b2 as U,N as u}from"./index-ctFdmWBt.js";import{A as w}from"./AdminLayout-D3bHW2Uz.js";const D=()=>{const t=_(),s=P(I),[r,p]=d.useState(!1),[h,g]=d.useState(null),[l,c]=d.useState({firstName:(s==null?void 0:s.firstName)||"",lastName:(s==null?void 0:s.lastName)||"",email:(s==null?void 0:s.email)||"",mobile:(s==null?void 0:s.phone)||"",profileImage:(s==null?void 0:s.profileImage)||null});d.useEffect(()=>{(async()=>{try{const e=await t(N()).unwrap();e.data&&(t(b({firstName:e.data.firstName,lastName:e.data.lastName,email:e.data.email,phone:e.data.mobile,profileImage:e.data.profileImage})),c({firstName:e.data.firstName,lastName:e.data.lastName,email:e.data.email,mobile:e.data.mobile,profileImage:e.data.profileImage}))}catch(e){console.error("Error fetching user data:",e)}})()},[t]);const o=n=>{const{name:e,value:i}=n.target;c(m=>({...m,[e]:i}))},x=n=>{const e=n.target.files[0];if(e){if(e.size>5*1024*1024){u.error("Image size should be less than 5MB");return}g(e);const i=new FileReader;i.onloadend=()=>{c(m=>({...m,profileImage:i.result}))},i.readAsDataURL(e)}},j=async n=>{n.preventDefault(),p(!0);try{let e=l.profileImage;if(h){const f=await t(S(h)).unwrap();f.success&&f.data.fileUrl&&(e=f.data.fileUrl)}const i={firstName:l.firstName,lastName:l.lastName,mobile:l.mobile,profileImage:e},m=await t(U(i)).unwrap();if(m.success)t(b({firstName:l.firstName,lastName:l.lastName,phone:l.mobile,profileImage:e})),await t(N()).unwrap(),u.success("Profile updated successfully!"),g(null);else throw new Error(m.message||"Failed to update profile")}catch(e){console.error("Profile update error:",e),u.error(e.message||"Failed to update profile")}finally{p(!1)}};return a.jsx(w,{children:a.jsxs("div",{className:"AdminProfile",children:[a.jsx("h2",{children:"My Profile"}),a.jsx("div",{className:"AdminProfile__content",children:a.jsxs("form",{onSubmit:j,className:"AdminProfile__form",children:[a.jsxs("div",{className:"AdminProfile__fields",children:[a.jsxs("div",{className:"AdminProfile__field-group",children:[a.jsx("label",{children:"First Name"}),a.jsx("input",{type:"text",name:"firstName",value:l.firstName,onChange:o,disabled:r,required:!0})]}),a.jsxs("div",{className:"AdminProfile__field-group",children:[a.jsx("label",{children:"Last Name"}),a.jsx("input",{type:"text",name:"lastName",value:l.lastName,onChange:o,disabled:r,required:!0})]}),a.jsxs("div",{className:"AdminProfile__field-group",children:[a.jsx("label",{children:"Email"}),a.jsx("input",{type:"email",name:"email",value:l.email,onChange:o,disabled:!0})]}),a.jsxs("div",{className:"AdminProfile__field-group",children:[a.jsx("label",{children:"Phone"}),a.jsx("input",{type:"tel",name:"mobile",value:l.mobile,onChange:o,disabled:r,pattern:"[0-9+\\-\\s()]+",title:"Please enter a valid phone number",placeholder:"+****************"})]}),a.jsx("button",{type:"submit",className:`AdminProfile__save-btn ${r?"loading":""}`,disabled:r,children:"Update & Save"})]}),a.jsxs("div",{className:"AdminProfile__image-section",children:[a.jsx("h3",{children:"Profile Image"}),a.jsx("div",{className:"AdminProfile__image-container",children:l.profileImage?a.jsx("img",{src:l.profileImage.startsWith("/upload")?v+l.profileImage:l.profileImage,alt:"Profile"}):a.jsx(A,{className:"AdminProfile__default-avatar"})}),a.jsxs("label",{className:"AdminProfile__upload-btn",children:[a.jsx(y,{})," Upload Photo",a.jsx("input",{type:"file",accept:"image/*",onChange:x,style:{display:"none"},disabled:r})]})]})]})})]})})};export{D as default};
