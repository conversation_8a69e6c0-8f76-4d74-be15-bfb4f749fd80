import{b as _,d as f,bK as E,bL as I,r as V,j as e,b9 as G,bM as L,aZ as A,bN as u,bO as x,bq as K,bP as q,bi as O}from"./index-ctFdmWBt.js";import{A as H}from"./AdminLayout-D3bHW2Uz.js";const Y=()=>{var p,N,y,g,C,R,w,S,$,D;const j=_(),M=f(E),t=f(I),[l,k]=V.useState("6months"),n=M||{salesChart:{labels:[],data:[]},userRegistrations:{labels:[],data:[]},categoryDistribution:{labels:[],data:[]},revenueByCategory:{labels:[],data:[]}};(p=n.salesChart)!=null&&p.labels,(N=n.salesChart)!=null&&N.data,(y=n.userRegistrations)!=null&&y.labels,(g=n.userRegistrations)!=null&&g.data;const T=()=>{j(O({id:Date.now(),type:"report_export",description:`Reports exported as PDF for period: ${l}`,timestamp:new Date().toISOString(),user:"Admin"}));const s=`
XOSportsHub Analytics Report
Period: ${l}
Generated: ${new Date().toLocaleString()}

Key Metrics:
- Total Revenue: ${c(t.totalRevenue)}
- Total Users: ${t.totalBuyers+t.totalSellers}
- Total Content: ${t.totalContent}
- Monthly Revenue: ${c(t.monthlyRevenue)}
- Monthly Orders: ${t.monthlyOrders}

Revenue Trend: ${o.isPositive?"+":""}${o.value}%
User Growth: ${d.isPositive?"+":""}${d.value}%
Content Growth: ${h.isPositive?"+":""}${h.value}%
    `,a=new Blob([s],{type:"text/plain"}),i=window.URL.createObjectURL(a),r=document.createElement("a");r.href=i,r.download=`xosportshub-report-${l}-${new Date().toISOString().split("T")[0]}.txt`,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(i),alert("PDF report has been generated and downloaded!")},F=()=>{j(O({id:Date.now(),type:"report_export",description:`Reports exported as CSV for period: ${l}`,timestamp:new Date().toISOString(),user:"Admin"}));const s=`
Date,Revenue,Users,Content,Orders
${new Date().toISOString().split("T")[0]},${t.totalRevenue},${t.totalBuyers+t.totalSellers},${t.totalContent},${t.monthlyOrders}
    `.trim(),a=new Blob([s],{type:"text/csv"}),i=window.URL.createObjectURL(a),r=document.createElement("a");r.href=i,r.download=`xosportshub-data-${l}-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(i),alert("CSV data has been generated and downloaded!")},c=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s),m=(s,a)=>{const i=(s-a)/a*100;return{value:Math.abs(i).toFixed(1),isPositive:i>0}},v={totalRevenue:10200,totalUsers:1200,totalContent:420,monthlyRevenue:2800},o=m(t.totalRevenue,v.totalRevenue),d=m(t.totalBuyers+t.totalSellers,v.totalUsers),h=m(t.totalContent,v.totalContent),b=m(t.monthlyRevenue,v.monthlyRevenue);return e.jsx(H,{children:e.jsxs("div",{className:"AdminReports",children:[e.jsxs("div",{className:"AdminReports__header",children:[e.jsx("div",{className:"header-left",children:e.jsxs("div",{className:"period-selector",children:[e.jsx(G,{className:"calendar-icon"}),e.jsxs("select",{value:l,onChange:s=>k(s.target.value),className:"period-select",children:[e.jsx("option",{value:"1month",children:"Last Month"}),e.jsx("option",{value:"3months",children:"Last 3 Months"}),e.jsx("option",{value:"6months",children:"Last 6 Months"}),e.jsx("option",{value:"1year",children:"Last Year"})]})]})}),e.jsxs("div",{className:"header-right",children:[e.jsxs("button",{className:"btn btn-outline",onClick:T,children:[e.jsx(L,{}),"Export PDF"]}),e.jsxs("button",{className:"btn btn-primary",onClick:F,children:[e.jsx(L,{}),"Export CSV"]})]})]}),e.jsxs("div",{className:"AdminReports__metrics",children:[e.jsxs("div",{className:"metric-card revenue",children:[e.jsxs("div",{className:"metric-header",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(A,{})}),e.jsxs("div",{className:`metric-trend ${o.isPositive?"positive":"negative"}`,children:[o.isPositive?e.jsx(u,{}):e.jsx(x,{}),o.value,"%"]})]}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:c(t.totalRevenue)}),e.jsx("div",{className:"metric-label",children:"Total Revenue"}),e.jsx("div",{className:"metric-sublabel",children:"vs previous period"})]})]}),e.jsxs("div",{className:"metric-card users",children:[e.jsxs("div",{className:"metric-header",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(K,{})}),e.jsxs("div",{className:`metric-trend ${d.isPositive?"positive":"negative"}`,children:[d.isPositive?e.jsx(u,{}):e.jsx(x,{}),d.value,"%"]})]}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:(t.totalBuyers+t.totalSellers).toLocaleString()}),e.jsx("div",{className:"metric-label",children:"Total Users"}),e.jsx("div",{className:"metric-sublabel",children:"vs previous period"})]})]}),e.jsxs("div",{className:"metric-card content",children:[e.jsxs("div",{className:"metric-header",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(q,{})}),e.jsxs("div",{className:`metric-trend ${h.isPositive?"positive":"negative"}`,children:[h.isPositive?e.jsx(u,{}):e.jsx(x,{}),h.value,"%"]})]}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:t.totalContent.toLocaleString()}),e.jsx("div",{className:"metric-label",children:"Total Content"}),e.jsx("div",{className:"metric-sublabel",children:"vs previous period"})]})]}),e.jsxs("div",{className:"metric-card monthly",children:[e.jsxs("div",{className:"metric-header",children:[e.jsx("div",{className:"metric-icon",children:e.jsx(A,{})}),e.jsxs("div",{className:`metric-trend ${b.isPositive?"positive":"negative"}`,children:[b.isPositive?e.jsx(u,{}):e.jsx(x,{}),b.value,"%"]})]}),e.jsxs("div",{className:"metric-content",children:[e.jsx("div",{className:"metric-number",children:c(t.monthlyRevenue)}),e.jsx("div",{className:"metric-label",children:"Monthly Revenue"}),e.jsx("div",{className:"metric-sublabel",children:"vs previous month"})]})]})]}),e.jsxs("div",{className:"AdminReports__charts",children:[e.jsxs("div",{className:"chart-container",children:[e.jsxs("div",{className:"chart-header",children:[e.jsx("h3",{children:"Sales Over Time"}),e.jsx("p",{children:"Revenue trends for the selected period"})]}),e.jsx("div",{className:"chart-placeholder",children:e.jsxs("div",{className:"chart-mock",children:[e.jsx("div",{className:"chart-bars",children:(((C=n.salesChart)==null?void 0:C.data)||[]).map((s,a)=>{var i;return e.jsx("div",{className:"chart-bar",style:{height:`${s/Math.max(...((i=n.salesChart)==null?void 0:i.data)||[1])*100}%`},children:e.jsx("span",{className:"bar-value",children:c(s)})},a)})}),e.jsx("div",{className:"chart-labels",children:(((R=n.salesChart)==null?void 0:R.labels)||[]).map((s,a)=>e.jsx("span",{className:"chart-label",children:s},a))})]})})]}),e.jsxs("div",{className:"chart-container",children:[e.jsxs("div",{className:"chart-header",children:[e.jsx("h3",{children:"User Registrations"}),e.jsx("p",{children:"Monthly user growth"})]}),e.jsx("div",{className:"chart-placeholder",children:e.jsxs("div",{className:"chart-mock",children:[e.jsx("div",{className:"chart-bars",children:(((w=n.userRegistrations)==null?void 0:w.data)||[]).map((s,a)=>{var i;return e.jsx("div",{className:"chart-bar users-bar",style:{height:`${s/Math.max(...((i=n.userRegistrations)==null?void 0:i.data)||[1])*100}%`},children:e.jsx("span",{className:"bar-value",children:s})},a)})}),e.jsx("div",{className:"chart-labels",children:(((S=n.userRegistrations)==null?void 0:S.labels)||[]).map((s,a)=>e.jsx("span",{className:"chart-label",children:s},a))})]})})]})]}),e.jsxs("div",{className:"AdminReports__distributions",children:[e.jsxs("div",{className:"distribution-container",children:[e.jsxs("div",{className:"distribution-header",children:[e.jsx("h3",{children:"Content Categories Distribution"}),e.jsx("p",{children:"Breakdown by content type"})]}),e.jsx("div",{className:"distribution-chart",children:((($=n.categoryDistribution)==null?void 0:$.labels)||[]).map((s,a)=>{var r;const i=(((r=n.categoryDistribution)==null?void 0:r.data)||[])[a]||0;return e.jsxs("div",{className:"distribution-item",children:[e.jsx("div",{className:"distribution-bar",children:e.jsx("div",{className:"distribution-fill",style:{width:`${i}%`}})}),e.jsxs("div",{className:"distribution-info",children:[e.jsx("span",{className:"distribution-label",children:s}),e.jsxs("span",{className:"distribution-value",children:[i,"%"]})]})]},a)})})]}),e.jsxs("div",{className:"distribution-container",children:[e.jsxs("div",{className:"distribution-header",children:[e.jsx("h3",{children:"Revenue by Category"}),e.jsx("p",{children:"Revenue breakdown by content type"})]}),e.jsx("div",{className:"distribution-chart",children:(((D=n.revenueByCategory)==null?void 0:D.labels)||[]).map((s,a)=>{var P,U;const i=(((P=n.revenueByCategory)==null?void 0:P.data)||[])[a]||0,r=Math.max(...((U=n.revenueByCategory)==null?void 0:U.data)||[1]),B=r>0?i/r*100:0;return e.jsxs("div",{className:"distribution-item",children:[e.jsx("div",{className:"distribution-bar",children:e.jsx("div",{className:"distribution-fill revenue-fill",style:{width:`${B}%`}})}),e.jsxs("div",{className:"distribution-info",children:[e.jsx("span",{className:"distribution-label",children:s}),e.jsx("span",{className:"distribution-value",children:c(i)})]})]},a)})})]})]})]})})};export{Y as default};
