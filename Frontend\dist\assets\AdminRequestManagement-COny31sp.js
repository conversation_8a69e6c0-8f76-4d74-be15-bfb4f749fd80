import{b as l,r as t,j as e,bt as d}from"./index-ctFdmWBt.js";import{A as c}from"./AdminLayout-D3bHW2Uz.js";const x=()=>{l();const[a,n]=t.useState(""),[r,i]=t.useState("all");return e.jsx(c,{children:e.jsxs("div",{className:"AdminRequestManagement",children:[e.jsx("div",{className:"AdminRequestManagement__header",children:e.jsx("p",{children:"Manage and monitor all custom training requests"})}),e.jsxs("div",{className:"AdminRequestManagement__controls",children:[e.jsxs("div",{className:"search-box",children:[e.jsx(d,{className:"search-icon"}),e.jsx("input",{type:"text",placeholder:"Search requests...",value:a,onChange:s=>n(s.target.value)})]}),e.jsx("div",{className:"filter-box",children:e.jsxs("select",{value:r,onChange:s=>i(s.target.value),children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"}),e.jsx("option",{value:"completed",children:"Completed"})]})})]}),e.jsx("div",{className:"AdminRequestManagement__table",children:e.jsxs("table",{children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:e.jsx("input",{type:"checkbox"})}),e.jsx("th",{children:"Request ID"}),e.jsx("th",{children:"Title"}),e.jsx("th",{children:"Buyer"}),e.jsx("th",{children:"Sport"}),e.jsx("th",{children:"Budget"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Created Date"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:e.jsx("tr",{children:e.jsx("td",{colSpan:"9",className:"no-data",children:"Loading requests..."})})})]})}),e.jsxs("div",{className:"AdminRequestManagement__pagination",children:[e.jsx("button",{disabled:!0,children:"Previous"}),e.jsx("span",{children:"Page 1 of 1"}),e.jsx("button",{disabled:!0,children:"Next"})]})]})})};export{x as default};
