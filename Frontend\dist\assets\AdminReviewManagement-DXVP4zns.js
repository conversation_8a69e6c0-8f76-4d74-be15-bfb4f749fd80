import{b as J,d as r,bz as U,bA as q,bB as I,bC as K,b4 as Q,bD as V,r as i,bE as W,bF as X,j as e,bd as y,bt as Y,N as d,bG as j,bH as o,bI as Z,bJ as ee}from"./index-ctFdmWBt.js";import{A as se}from"./AdminLayout-D3bHW2Uz.js";import{R as te}from"./RatingStars-hIwJCgKk.js";const ie=()=>{const t=J(),c=r(U),a=r(q),l=r(I),n=r(K),D=r(Q),b=r(V),[g,k]=i.useState(l.search||""),[h,A]=i.useState(l.status||"all"),[u,F]=i.useState(l.rating||"all"),[_,v]=i.useState(!1),[N,f]=i.useState(null),[B,p]=i.useState(!1);i.useEffect(()=>{const s={page:n.current,limit:n.limit,search:g,status:h==="all"?"":h,rating:u==="all"?"":u,sortBy:l.sortBy,sortOrder:l.sortOrder};t(W(s)),t(X())},[t,n.current,n.limit,g,h,u,l.sortBy,l.sortOrder]);const T=s=>{k(s),t(j({search:s}))},M=s=>{A(s),t(j({status:s}))},E=s=>{F(s),t(j({rating:s}))},P=s=>{const x=a.includes(s);t(x?o(a.filter(m=>m!==s)):o([...a,s]))},L=()=>{a.length===c.data.length?t(o([])):t(o(c.data.map(s=>s._id)))},O=s=>{f(s),v(!0)},$=async()=>{if(N)try{await t(Z(N._id)).unwrap(),d.success("Review deleted successfully"),v(!1),f(null)}catch(s){d.error(s||"Failed to delete review")}},z=()=>{if(a.length===0){d.warning("Please select reviews to delete");return}p(!0)},G=async()=>{try{await t(ee(a)).unwrap(),d.success(`${a.length} reviews deleted successfully`),t(o([])),p(!1)}catch(s){d.error(s||"Failed to delete reviews")}},H=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),w=s=>{t(j({page:s}))};return e.jsx(se,{children:e.jsxs("div",{className:"AdminReviewManagement",children:[e.jsxs("div",{className:"AdminReviewManagement__header",children:[e.jsx("p",{children:"Manage and monitor all content reviews"}),a.length>0&&e.jsxs("div",{className:"bulk-actions",children:[e.jsxs("span",{children:[a.length," reviews selected"]}),e.jsxs("button",{className:"bulk-delete-btn",onClick:z,children:[e.jsx(y,{})," Delete Selected"]})]})]}),e.jsxs("div",{className:"AdminReviewManagement__controls",children:[e.jsxs("div",{className:"search-box",children:[e.jsx(Y,{className:"search-icon"}),e.jsx("input",{type:"text",placeholder:"Search reviews...",value:g,onChange:s=>T(s.target.value)})]}),e.jsxs("div",{className:"filter-box",children:[e.jsxs("select",{value:h,onChange:s=>M(s.target.value),children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"}),e.jsx("option",{value:"flagged",children:"Flagged"})]}),e.jsxs("select",{value:u,onChange:s=>E(s.target.value),children:[e.jsx("option",{value:"all",children:"All Ratings"}),e.jsx("option",{value:"5",children:"5 Stars"}),e.jsx("option",{value:"4",children:"4 Stars"}),e.jsx("option",{value:"3",children:"3 Stars"}),e.jsx("option",{value:"2",children:"2 Stars"}),e.jsx("option",{value:"1",children:"1 Star"})]})]})]}),e.jsx("div",{className:"AdminReviewManagement__table",children:D.reviews?e.jsx("div",{className:"loading",children:"Loading reviews..."}):b.reviews?e.jsxs("div",{className:"error",children:["Error: ",b.reviews]}):e.jsxs("table",{children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:e.jsx("input",{type:"checkbox",checked:a.length===c.data.length&&c.data.length>0,onChange:L})}),e.jsx("th",{children:"Review ID"}),e.jsx("th",{children:"Content"}),e.jsx("th",{children:"Reviewer"}),e.jsx("th",{children:"Rating"}),e.jsx("th",{children:"Comment"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Created Date"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:c.data.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:"9",className:"no-data",children:"No reviews found"})}):c.data.map(s=>{var x,m,R,S,C;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("input",{type:"checkbox",checked:a.includes(s._id),onChange:()=>P(s._id)})}),e.jsx("td",{className:"review-id",children:s._id.slice(-8)}),e.jsxs("td",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:((x=s.content)==null?void 0:x.title)||"N/A"}),e.jsxs("div",{className:"content-type",children:[(m=s.content)==null?void 0:m.contentType," • ",(R=s.content)==null?void 0:R.sport]})]}),e.jsx("td",{className:"reviewer-info",children:e.jsxs("div",{className:"reviewer-name",children:[(S=s.user)==null?void 0:S.firstName," ",(C=s.user)==null?void 0:C.lastName]})}),e.jsxs("td",{className:"rating",children:[e.jsx(te,{rating:s.rating,size:20}),e.jsxs("span",{className:"rating-number",children:["(",s.rating,")"]})]}),e.jsx("td",{className:"comment",children:e.jsx("div",{className:"comment-text",children:s.text.length>100?`${s.text.substring(0,100)}...`:s.text})}),e.jsx("td",{className:"status",children:e.jsx("span",{className:`status-badge status-${s.status||"approved"}`,children:s.status||"approved"})}),e.jsx("td",{className:"created-date",children:H(s.createdAt)}),e.jsx("td",{className:"actions",children:e.jsx("button",{className:"bulk-delete-btn",onClick:()=>O(s),title:"Delete Review",children:e.jsx(y,{})})})]},s._id)})})]})}),e.jsxs("div",{className:"AdminReviewManagement__pagination",children:[e.jsx("button",{disabled:n.current<=1,onClick:()=>w(n.current-1),children:"Previous"}),e.jsxs("span",{children:["Page ",n.current," of ",n.pages]}),e.jsx("button",{disabled:n.current>=n.pages,onClick:()=>w(n.current+1),children:"Next"})]}),_&&e.jsx("div",{className:"modal-overlay",children:e.jsxs("div",{className:"modal",children:[e.jsx("h3",{children:"Confirm Delete"}),e.jsx("p",{children:"Are you sure you want to delete this review? This action cannot be undone."}),e.jsxs("div",{className:"modal-actions",children:[e.jsx("button",{className:"cancel-btn",onClick:()=>v(!1),children:"Cancel"}),e.jsx("button",{className:"delete-btn",onClick:$,children:"Delete"})]})]})}),B&&e.jsx("div",{className:"modal-overlay",children:e.jsxs("div",{className:"modal",children:[e.jsx("h3",{children:"Confirm Bulk Delete"}),e.jsxs("p",{children:["Are you sure you want to delete ",a.length," selected reviews? This action cannot be undone."]}),e.jsxs("div",{className:"modal-actions",children:[e.jsx("button",{className:"cancel-btn",onClick:()=>p(!1),children:"Cancel"}),e.jsx("button",{className:"delete-btn",onClick:G,children:"Delete All"})]})]})})]})})};export{ie as default};
