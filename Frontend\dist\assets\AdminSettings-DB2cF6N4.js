import{b as f,d as b,e3 as w,r as u,j as s,e4 as h,aZ as x,b9 as P,aM as k,b8 as y,e5 as F,bQ as A,e6 as N,e7 as E,e8 as R,e9 as M,ea as D,dO as U,bi as v}from"./index-ctFdmWBt.js";import{A as $}from"./AdminLayout-D3bHW2Uz.js";const I=()=>{const p=f(),t=b(w),[i,n]=u.useState(t),[r,l]=u.useState(!1),c=(a,e)=>{n(m=>({...m,[a]:e})),l(!0)},g=()=>{p(F(i)),l(!1),alert("Financial settings saved successfully!")},o=()=>{n(t),l(!1)};return s.jsx("div",{className:"FinancialSettings",children:s.jsxs("div",{className:"settings-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"Financial Settings"}),s.jsx("p",{children:"Configure payment processing, commissions, and payout settings"})]}),s.jsxs("div",{className:"settings-form",children:[s.jsxs("div",{className:"settings-group",children:[s.jsxs("h4",{children:[s.jsx(h,{className:"group-icon"}),"Commission Structure"]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Platform Commission (%)"}),s.jsxs("div",{className:"input-with-icon",children:[s.jsx(h,{className:"input-icon"}),s.jsx("input",{type:"number",value:i.platformCommission,onChange:a=>c("platformCommission",parseFloat(a.target.value)),className:"form-input",min:"0",max:"100",step:"0.1"})]}),s.jsx("span",{className:"form-help",children:"Percentage taken by the platform from each sale"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Seller Payout (%)"}),s.jsxs("div",{className:"input-with-icon",children:[s.jsx(h,{className:"input-icon"}),s.jsx("input",{type:"number",value:i.sellerPayout,onChange:a=>c("sellerPayout",parseFloat(a.target.value)),className:"form-input",min:"0",max:"100",step:"0.1",readOnly:!0})]}),s.jsxs("span",{className:"form-help",children:["Automatically calculated: ",100-i.platformCommission,"%"]})]})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Minimum Payout Threshold ($)"}),s.jsxs("div",{className:"input-with-icon",children:[s.jsx(x,{className:"input-icon"}),s.jsx("input",{type:"number",value:i.minimumPayout,onChange:a=>c("minimumPayout",parseFloat(a.target.value)),className:"form-input",min:"1",step:"1"})]}),s.jsx("span",{className:"form-help",children:"Minimum amount before payout is processed"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Processing Fee (%)"}),s.jsxs("div",{className:"input-with-icon",children:[s.jsx(h,{className:"input-icon"}),s.jsx("input",{type:"number",value:i.processingFee,onChange:a=>c("processingFee",parseFloat(a.target.value)),className:"form-input",min:"0",max:"10",step:"0.1"})]}),s.jsx("span",{className:"form-help",children:"Payment gateway processing fee"})]})]})]}),s.jsxs("div",{className:"settings-group",children:[s.jsxs("h4",{children:[s.jsx(P,{className:"group-icon"}),"Payout Schedule"]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Payout Frequency"}),s.jsxs("select",{value:i.payoutSchedule,onChange:a=>c("payoutSchedule",a.target.value),className:"form-select",children:[s.jsx("option",{value:"weekly",children:"Weekly (Every Friday)"}),s.jsx("option",{value:"biweekly",children:"Bi-weekly (Every 2 weeks)"}),s.jsx("option",{value:"monthly",children:"Monthly (1st of each month)"})]}),s.jsx("span",{className:"form-help",children:"How often sellers receive payouts"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Tax Rate (%)"}),s.jsxs("div",{className:"input-with-icon",children:[s.jsx(h,{className:"input-icon"}),s.jsx("input",{type:"number",value:i.taxRate,onChange:a=>c("taxRate",parseFloat(a.target.value)),className:"form-input",min:"0",max:"50",step:"0.1"})]}),s.jsx("span",{className:"form-help",children:"Default tax rate for transactions"})]})]})]}),s.jsxs("div",{className:"settings-group",children:[s.jsxs("h4",{children:[s.jsx(k,{className:"group-icon"}),"Payment Gateway Configuration"]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Stripe Publishable Key"}),s.jsx("input",{type:"text",placeholder:"pk_live_...",className:"form-input"}),s.jsx("span",{className:"form-help",children:"Your Stripe publishable key"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Stripe Secret Key"}),s.jsx("input",{type:"password",placeholder:"sk_live_...",className:"form-input"}),s.jsx("span",{className:"form-help",children:"Your Stripe secret key (encrypted)"})]})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"PayPal Client ID"}),s.jsx("input",{type:"text",placeholder:"Your PayPal Client ID",className:"form-input"}),s.jsx("span",{className:"form-help",children:"PayPal REST API Client ID"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"PayPal Client Secret"}),s.jsx("input",{type:"password",placeholder:"Your PayPal Client Secret",className:"form-input"}),s.jsx("span",{className:"form-help",children:"PayPal REST API Client Secret"})]})]})]}),s.jsxs("div",{className:"settings-group",children:[s.jsxs("h4",{children:[s.jsx(x,{className:"group-icon"}),"Content Pricing Controls"]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Minimum Content Price ($)"}),s.jsxs("div",{className:"input-with-icon",children:[s.jsx(x,{className:"input-icon"}),s.jsx("input",{type:"number",value:i.minPrice,onChange:a=>c("minPrice",parseFloat(a.target.value)),className:"form-input",min:"1",step:"1"})]}),s.jsx("span",{className:"form-help",children:"Minimum price sellers can set for content"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Maximum Content Price ($)"}),s.jsxs("div",{className:"input-with-icon",children:[s.jsx(x,{className:"input-icon"}),s.jsx("input",{type:"number",value:i.maxPrice,onChange:a=>c("maxPrice",parseFloat(a.target.value)),className:"form-input",min:"1",step:"1"})]}),s.jsx("span",{className:"form-help",children:"Maximum price sellers can set for content"})]})]})]}),s.jsxs("div",{className:"commission-preview",children:[s.jsx("h4",{children:"Commission Breakdown Preview"}),s.jsx("div",{className:"preview-example",children:s.jsxs("div",{className:"example-sale",children:[s.jsx("span",{className:"sale-label",children:"Example Sale: $100.00"}),s.jsxs("div",{className:"breakdown",children:[s.jsxs("div",{className:"breakdown-item",children:[s.jsxs("span",{children:["Platform Commission (",i.platformCommission,"%)"]}),s.jsxs("span",{className:"amount",children:["-$",(100*i.platformCommission/100).toFixed(2)]})]}),s.jsxs("div",{className:"breakdown-item",children:[s.jsxs("span",{children:["Processing Fee (",i.processingFee,"%)"]}),s.jsxs("span",{className:"amount",children:["-$",(100*i.processingFee/100).toFixed(2)]})]}),s.jsxs("div",{className:"breakdown-item total",children:[s.jsx("span",{children:"Seller Receives"}),s.jsxs("span",{className:"amount",children:["$",(100-100*i.platformCommission/100-100*i.processingFee/100).toFixed(2)]})]})]})]})})]}),s.jsxs("div",{className:"form-actions",children:[s.jsxs("button",{className:"btn btn-primary",onClick:g,disabled:!r,children:[s.jsx(y,{}),"Save Financial Settings"]}),s.jsx("button",{className:"btn btn-outline",onClick:o,disabled:!r,children:"Reset Changes"})]})]})]})})},L=()=>{const p=f();b(A);const[t,i]=u.useState("general"),[n,r]=u.useState({siteName:"XOSportsHub",siteDescription:"Premier sports content marketplace",contactEmail:"<EMAIL>",supportEmail:"<EMAIL>",logoUrl:"",emailNotifications:!0,pushNotifications:!1,marketingEmails:!0,securityAlerts:!0,twoFactorAuth:!1,sessionTimeout:30,passwordExpiry:90}),l=(e,m)=>{r(d=>({...d,[e]:m}))},c=()=>{p(v({id:Date.now(),type:"settings_update",description:`Admin settings updated for ${t} section`,timestamp:new Date().toISOString(),user:"Admin"})),console.log("Saving settings:",n),alert(`${t.charAt(0).toUpperCase()+t.slice(1)} settings have been saved successfully!`)},g=()=>{const e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=m=>{const d=m.target.files[0];if(d){const j=new FileReader;j.onload=S=>{r(C=>({...C,logoUrl:S.target.result}))},j.readAsDataURL(d),p(v({id:Date.now(),type:"logo_upload",description:`Site logo updated: ${d.name}`,timestamp:new Date().toISOString(),user:"Admin"})),alert("Logo uploaded successfully!")}},e.click()},o=e=>{alert(`Role permission editing for ${e} would open a detailed permissions modal in a real application.`)},a=[{id:"general",label:"General Settings",icon:s.jsx(E,{})},{id:"financial",label:"Financial",icon:s.jsx(R,{})},{id:"notifications",label:"Notifications",icon:s.jsx(M,{})},{id:"security",label:"Security",icon:s.jsx(D,{})},{id:"roles",label:"User Roles",icon:s.jsx(U,{})}];return s.jsx($,{children:s.jsxs("div",{className:"AdminSettings",children:[s.jsx("div",{className:"AdminSettings__nav",children:a.map(e=>s.jsxs("button",{className:`nav-tab ${t===e.id?"active":""}`,onClick:()=>i(e.id),children:[e.icon,e.label]},e.id))}),s.jsxs("div",{className:"AdminSettings__content",children:[t==="general"&&s.jsxs("div",{className:"settings-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"General Settings"}),s.jsx("p",{children:"Configure basic site settings and information"})]}),s.jsxs("div",{className:"settings-form",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Site Name"}),s.jsx("input",{type:"text",value:n.siteName,onChange:e=>l("siteName",e.target.value),className:"form-input"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Site Description"}),s.jsx("textarea",{value:n.siteDescription,onChange:e=>l("siteDescription",e.target.value),className:"form-textarea",rows:"3"})]}),s.jsxs("div",{className:"form-row",children:[s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Contact Email"}),s.jsx("input",{type:"email",value:n.contactEmail,onChange:e=>l("contactEmail",e.target.value),className:"form-input"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Support Email"}),s.jsx("input",{type:"email",value:n.supportEmail,onChange:e=>l("supportEmail",e.target.value),className:"form-input"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Site Logo"}),s.jsxs("div",{className:"logo-upload",children:[s.jsx("div",{className:"logo-preview",children:n.logoUrl?s.jsx("img",{src:n.logoUrl,alt:"Site Logo"}):s.jsxs("div",{className:"logo-placeholder",children:[s.jsx(N,{}),s.jsx("span",{children:"No logo uploaded"})]})}),s.jsxs("button",{className:"btn btn-outline",onClick:g,children:[s.jsx(N,{}),"Upload Logo"]})]})]})]})]}),t==="financial"&&s.jsx(I,{}),t==="notifications"&&s.jsxs("div",{className:"settings-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"Notification Settings"}),s.jsx("p",{children:"Configure email and push notification preferences"})]}),s.jsxs("div",{className:"settings-form",children:[s.jsxs("div",{className:"notification-group",children:[s.jsx("h4",{children:"Email Notifications"}),s.jsxs("div",{className:"toggle-setting",children:[s.jsxs("div",{className:"toggle-info",children:[s.jsx("span",{className:"toggle-label",children:"Email Notifications"}),s.jsx("span",{className:"toggle-description",children:"Receive general email notifications"})]}),s.jsxs("label",{className:"toggle-switch",children:[s.jsx("input",{type:"checkbox",checked:n.emailNotifications,onChange:e=>l("emailNotifications",e.target.checked)}),s.jsx("span",{className:"toggle-slider"})]})]}),s.jsxs("div",{className:"toggle-setting",children:[s.jsxs("div",{className:"toggle-info",children:[s.jsx("span",{className:"toggle-label",children:"Marketing Emails"}),s.jsx("span",{className:"toggle-description",children:"Receive marketing and promotional emails"})]}),s.jsxs("label",{className:"toggle-switch",children:[s.jsx("input",{type:"checkbox",checked:n.marketingEmails,onChange:e=>l("marketingEmails",e.target.checked)}),s.jsx("span",{className:"toggle-slider"})]})]}),s.jsxs("div",{className:"toggle-setting",children:[s.jsxs("div",{className:"toggle-info",children:[s.jsx("span",{className:"toggle-label",children:"Security Alerts"}),s.jsx("span",{className:"toggle-description",children:"Receive security-related notifications"})]}),s.jsxs("label",{className:"toggle-switch",children:[s.jsx("input",{type:"checkbox",checked:n.securityAlerts,onChange:e=>l("securityAlerts",e.target.checked)}),s.jsx("span",{className:"toggle-slider"})]})]})]}),s.jsxs("div",{className:"notification-group",children:[s.jsx("h4",{children:"Push Notifications"}),s.jsxs("div",{className:"toggle-setting",children:[s.jsxs("div",{className:"toggle-info",children:[s.jsx("span",{className:"toggle-label",children:"Browser Notifications"}),s.jsx("span",{className:"toggle-description",children:"Receive push notifications in your browser"})]}),s.jsxs("label",{className:"toggle-switch",children:[s.jsx("input",{type:"checkbox",checked:n.pushNotifications,onChange:e=>l("pushNotifications",e.target.checked)}),s.jsx("span",{className:"toggle-slider"})]})]})]})]})]}),t==="security"&&s.jsxs("div",{className:"settings-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"Security Settings"}),s.jsx("p",{children:"Configure security and authentication settings"})]}),s.jsx("div",{className:"settings-form",children:s.jsxs("div",{className:"security-group",children:[s.jsx("h4",{children:"Authentication"}),s.jsxs("div",{className:"toggle-setting",children:[s.jsxs("div",{className:"toggle-info",children:[s.jsx("span",{className:"toggle-label",children:"Two-Factor Authentication"}),s.jsx("span",{className:"toggle-description",children:"Require 2FA for admin accounts"})]}),s.jsxs("label",{className:"toggle-switch",children:[s.jsx("input",{type:"checkbox",checked:n.twoFactorAuth,onChange:e=>l("twoFactorAuth",e.target.checked)}),s.jsx("span",{className:"toggle-slider"})]})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Session Timeout (minutes)"}),s.jsx("input",{type:"number",value:n.sessionTimeout,onChange:e=>l("sessionTimeout",parseInt(e.target.value)),className:"form-input",min:"5",max:"480"})]}),s.jsxs("div",{className:"form-group",children:[s.jsx("label",{children:"Password Expiry (days)"}),s.jsx("input",{type:"number",value:n.passwordExpiry,onChange:e=>l("passwordExpiry",parseInt(e.target.value)),className:"form-input",min:"30",max:"365"})]})]})})]}),t==="roles"&&s.jsxs("div",{className:"settings-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"User Roles & Permissions"}),s.jsx("p",{children:"Manage user roles and their permissions"})]}),s.jsxs("div",{className:"roles-grid",children:[s.jsxs("div",{className:"role-card",children:[s.jsxs("div",{className:"role-header",children:[s.jsx("h4",{children:"Admin"}),s.jsx("span",{className:"role-count",children:"2 users"})]}),s.jsxs("div",{className:"role-permissions",children:[s.jsx("span",{className:"permission-tag",children:"Full Access"}),s.jsx("span",{className:"permission-tag",children:"User Management"}),s.jsx("span",{className:"permission-tag",children:"Content Management"}),s.jsx("span",{className:"permission-tag",children:"System Settings"})]}),s.jsx("button",{className:"btn btn-outline",onClick:()=>o("Admin"),children:"Edit Permissions"})]}),s.jsxs("div",{className:"role-card",children:[s.jsxs("div",{className:"role-header",children:[s.jsx("h4",{children:"Seller"}),s.jsx("span",{className:"role-count",children:"89 users"})]}),s.jsxs("div",{className:"role-permissions",children:[s.jsx("span",{className:"permission-tag",children:"Content Upload"}),s.jsx("span",{className:"permission-tag",children:"Sales Management"}),s.jsx("span",{className:"permission-tag",children:"Profile Management"})]}),s.jsx("button",{className:"btn btn-outline",onClick:()=>o("Seller"),children:"Edit Permissions"})]}),s.jsxs("div",{className:"role-card",children:[s.jsxs("div",{className:"role-header",children:[s.jsx("h4",{children:"Buyer"}),s.jsx("span",{className:"role-count",children:"1,247 users"})]}),s.jsxs("div",{className:"role-permissions",children:[s.jsx("span",{className:"permission-tag",children:"Content Purchase"}),s.jsx("span",{className:"permission-tag",children:"Profile Management"}),s.jsx("span",{className:"permission-tag",children:"Order History"})]}),s.jsx("button",{className:"btn btn-outline",onClick:()=>o("Buyer"),children:"Edit Permissions"})]})]})]}),s.jsx("div",{className:"settings-actions",children:s.jsxs("button",{className:"btn btn-primary",onClick:c,children:[s.jsx(y,{}),"Save Changes"]})})]})]})})};export{L as default};
