.FinancialSettings{display:flex;flex-direction:column;gap:var(--heading6)}.settings-section{background-color:var(--white);border-radius:var(--border-radius);overflow:hidden}.section-header{padding:var(--heading6);border-bottom:1px solid var(--light-gray);background-color:var(--bg-gray)}.section-header h3{margin:0 0 4px;font-size:var(--heading5);color:var(--secondary-color)}.section-header p{margin:0;font-size:var(--smallfont);color:var(--dark-gray)}.settings-form{display:flex;flex-direction:column;gap:var(--heading6)}.settings-group{padding:var(--basefont);border:1px solid var(--light-gray);border-radius:var(--border-radius);background-color:var(--bg-gray)}.settings-group h4{display:flex;align-items:center;gap:var(--smallfont);margin:0 0 var(--basefont) 0;font-size:var(--basefont);color:var(--secondary-color);padding-bottom:var(--smallfont);border-bottom:1px solid var(--light-gray)}.group-icon{color:var(--btn-color);font-size:var(--basefont)}.form-row{display:grid;grid-template-columns:1fr 1fr;gap:var(--basefont);margin-bottom:var(--basefont)}.form-row:last-child{margin-bottom:0}.form-group{display:flex;flex-direction:column;gap:var(--smallfont)}.form-group label{font-size:var(--smallfont);font-weight:600;color:var(--secondary-color)}.form-input,.form-select{padding:var(--smallfont);border:1px solid var(--light-gray);border-radius:var(--border-radius);font-size:var(--smallfont);background-color:var(--white);transition:all .3s ease}.form-input:focus,.form-select:focus{outline:none;border-color:var(--btn-color);box-shadow:0 0 0 3px #ee34251a}.form-input:read-only{background-color:var(--bg-gray);color:var(--dark-gray);cursor:not-allowed}.input-with-icon{position:relative;display:flex;align-items:center}.input-icon{position:absolute;left:var(--smallfont);color:var(--dark-gray);font-size:var(--smallfont);z-index:1}.input-with-icon .form-input{padding-left:32px}.form-help{font-size:var(--extrasmallfont);color:var(--dark-gray);font-style:italic}.commission-preview{padding:var(--basefont);border:2px solid var(--btn-color);border-radius:var(--border-radius);background-color:var(--bg-blue)}.commission-preview h4{margin:0 0 var(--basefont) 0;font-size:var(--basefont);color:var(--secondary-color)}.preview-example{background-color:var(--white);border-radius:var(--border-radius);padding:var(--basefont)}.example-sale{display:flex;flex-direction:column;gap:var(--smallfont)}.sale-label{font-size:var(--basefont);font-weight:600;color:var(--secondary-color);text-align:center;padding-bottom:var(--smallfont);border-bottom:1px solid var(--light-gray)}.breakdown{display:flex;flex-direction:column;gap:4px}.breakdown-item{display:flex;justify-content:space-between;align-items:center;font-size:var(--smallfont);color:var(--text-color)}.breakdown-item.total{font-weight:600;font-size:var(--basefont);color:var(--secondary-color);padding-top:var(--smallfont);border-top:1px solid var(--light-gray);margin-top:var(--smallfont)}.breakdown-item .amount{font-weight:600;color:var(--btn-color)}.breakdown-item.total .amount{color:#10b981;font-size:var(--basefont)}.form-actions{display:flex;gap:var(--basefont);justify-content:flex-end;padding-top:var(--basefont);border-top:1px solid var(--light-gray)}.btn{display:flex;align-items:center;gap:var(--smallfont);padding:var(--smallfont) var(--basefont);border:none;border-radius:var(--border-radius);font-size:var(--smallfont);font-weight:600;cursor:pointer;transition:all .3s ease;text-decoration:none}.btn:disabled{opacity:.6;cursor:not-allowed}.btn.btn-primary{background-color:var(--btn-color);color:var(--white)}.btn.btn-outline{background-color:transparent;color:var(--secondary-color);border:1px solid var(--light-gray)}.btn.btn-outline:hover:not(:disabled){background-color:var(--bg-gray)}@media (max-width: 768px){.form-row{grid-template-columns:1fr}.settings-form{padding:var(--basefont)}.form-actions{flex-direction:column}.form-actions .btn{justify-content:center}.breakdown-item{font-size:var(--extrasmallfont)}.sale-label{font-size:var(--smallfont)}}@media (max-width: 480px){.section-header{padding:var(--basefont)}.settings-group,.commission-preview,.preview-example{padding:var(--smallfont)}.breakdown-item{flex-direction:column;align-items:flex-start;gap:2px}.breakdown-item .amount{align-self:flex-end}}.form-input.changed,.form-select.changed{border-color:#10b981;box-shadow:0 0 0 3px #10b9811a}.form-input.error{border-color:#ef4444;box-shadow:0 0 0 3px #ef44441a}.form-input.success{border-color:#10b981;box-shadow:0 0 0 3px #10b9811a}.form-actions.loading .btn{position:relative;color:transparent}.form-actions.loading .btn:after{content:"";position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.AdminSettings{display:flex;flex-direction:column;gap:var(--heading6)}.AdminSettings .AdminSettings__nav{display:flex;gap:4px;background-color:var(--white);border-radius:var(--border-radius);box-shadow:var(--box-shadow-light);padding:4px}.AdminSettings .nav-tab{display:flex;align-items:center;gap:var(--smallfont);padding:var(--smallfont) var(--basefont);border:none;border-radius:var(--border-radius);background:none;color:var(--secondary-color);font-size:var(--smallfont);font-weight:500;cursor:pointer;transition:all .3s ease;flex:1;justify-content:center}.AdminSettings .nav-tab:hover{background-color:var(--bg-gray);color:var(--btn-color)}.AdminSettings .nav-tab.active{background-color:var(--btn-color);color:var(--white);font-weight:600}.AdminSettings .AdminSettings__content{background-color:var(--white);border-radius:var(--border-radius);box-shadow:var(--box-shadow-light);overflow:hidden}.AdminSettings .settings-section{padding:var(--heading6)}.AdminSettings .section-header{margin-bottom:var(--heading6);padding-bottom:var(--basefont);border-bottom:1px solid var(--light-gray);border-radius:var(--border-radius)}.AdminSettings .section-header h3{margin:0 0 4px;font-size:var(--heading5);color:var(--secondary-color)}.AdminSettings .section-header p{margin:0;font-size:var(--smallfont);color:var(--dark-gray)}.AdminSettings .settings-form{display:flex;flex-direction:column;gap:var(--heading6)}.AdminSettings .form-row{display:grid;grid-template-columns:1fr 1fr;gap:var(--basefont);margin-bottom:0!important}.AdminSettings .form-group{display:flex;flex-direction:column;gap:var(--smallfont);margin-bottom:0!important}.AdminSettings .form-group label{font-size:var(--smallfont);font-weight:600;color:var(--secondary-color);margin-bottom:0!important}.AdminSettings .form-input,.AdminSettings .form-textarea{padding:var(--smallfont) var(--basefont);border:1px solid var(--light-gray);border-radius:var(--border-radius);font-size:var(--smallfont);background-color:var(--white);transition:all .3s ease}.AdminSettings .form-input:focus,.AdminSettings .form-textarea:focus{outline:none;border-color:var(--btn-color);box-shadow:0 0 0 3px #ee34251a}.AdminSettings .form-textarea{resize:vertical;min-height:80px}.AdminSettings .logo-upload{display:flex;align-items:center;gap:var(--basefont)}.AdminSettings .logo-preview{width:120px;height:80px;border:2px dashed var(--light-gray);border-radius:var(--border-radius);display:flex;align-items:center;justify-content:center;overflow:hidden}.AdminSettings .logo-preview img{width:120px;height:80px;object-fit:contain;object-position:center}.AdminSettings .logo-placeholder{display:flex;flex-direction:column;align-items:center;gap:4px;color:var(--dark-gray);font-size:var(--extrasmallfont)}.AdminSettings .logo-placeholder svg{font-size:var(--heading6)}.AdminSettings .notification-group,.AdminSettings .security-group{display:flex;flex-direction:column;gap:var(--basefont)}.AdminSettings .notification-group h4,.AdminSettings .security-group h4{margin:0;font-size:var(--basefont);color:var(--secondary-color);padding-bottom:var(--smallfont);border-bottom:1px solid var(--bg-gray)}.AdminSettings .toggle-setting{display:flex;justify-content:space-between;align-items:center;padding:var(--basefont);border:1px solid var(--light-gray);border-radius:var(--border-radius);transition:all .3s ease}.AdminSettings .toggle-setting:hover{border-color:var(--btn-color);box-shadow:var(--box-shadow-light)}.AdminSettings .toggle-info{display:flex;flex-direction:column;gap:4px}.AdminSettings .toggle-label{font-size:var(--smallfont);font-weight:600;color:var(--secondary-color)}.AdminSettings .toggle-description{font-size:var(--extrasmallfont);color:var(--dark-gray)}.AdminSettings .toggle-switch{position:relative;display:inline-block;width:50px;height:24px}.AdminSettings .toggle-switch input{opacity:0;width:0;height:0}.AdminSettings .toggle-slider{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:var(--light-gray);transition:.3s;border-radius:24px}.AdminSettings .toggle-slider:before{position:absolute;content:"";height:18px;width:18px;left:3px;bottom:3px;background-color:var(--white);transition:.3s;border-radius:50%}.AdminSettings .toggle-switch input:checked+.toggle-slider{background-color:var(--btn-color)}.AdminSettings .toggle-switch input:checked+.toggle-slider:before{transform:translate(26px)}.AdminSettings .roles-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:var(--basefont)}.AdminSettings .role-card{border:1px solid var(--light-gray);border-radius:var(--border-radius);padding:var(--basefont);transition:all .3s ease}.AdminSettings .role-card:hover{border-color:var(--btn-color);box-shadow:var(--box-shadow-light)}.AdminSettings .role-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--basefont)}.AdminSettings .role-header h4{margin:0;font-size:var(--basefont);color:var(--secondary-color)}.AdminSettings .role-count{font-size:var(--extrasmallfont);color:var(--dark-gray);background-color:var(--bg-gray);padding:2px 8px;border-radius:12px}.AdminSettings .role-permissions{display:flex;flex-wrap:wrap;gap:4px;margin-bottom:var(--basefont)}.AdminSettings .permission-tag{font-size:var(--extrasmallfont);background-color:var(--bg-blue);color:var(--btn-color);padding:2px 6px;border-radius:8px;font-weight:500}.AdminSettings .btn{display:flex;align-items:center;gap:var(--smallfont);padding:var(--smallfont) var(--basefont);border:none;border-radius:var(--border-radius);font-size:var(--smallfont);font-weight:600;cursor:pointer;transition:all .3s ease;text-decoration:none}.AdminSettings .btn.btn-primary{background-color:var(--btn-color);color:var(--white)}.AdminSettings .btn.btn-primary:hover{background-color:#d32f2f}.AdminSettings .btn.btn-outline{background-color:transparent;color:var(--secondary-color);border:1px solid var(--light-gray)}.AdminSettings .btn.btn-outline:hover{background-color:var(--bg-gray)}.AdminSettings .settings-actions{padding:0 var(--heading6) var(--heading6) var(--heading6);display:flex;justify-content:flex-end}@media (max-width: 768px){.AdminSettings .AdminSettings__nav{flex-direction:column}.AdminSettings .nav-tab{justify-content:flex-start}.AdminSettings .form-row{grid-template-columns:1fr}.AdminSettings .logo-upload{flex-direction:column;align-items:flex-start}.AdminSettings .toggle-setting{flex-direction:column;align-items:flex-start;gap:var(--smallfont)}.AdminSettings .roles-grid{grid-template-columns:1fr}.AdminSettings .settings-actions{padding:var(--basefont)}}@media (max-width: 480px){.AdminSettings .AdminSettings__nav{padding:2px}.AdminSettings .nav-tab{padding:var(--smallfont);font-size:var(--extrasmallfont)}.AdminSettings .nav-tab svg{display:none}.AdminSettings .settings-section{padding:var(--basefont)}.AdminSettings .role-header{flex-direction:column;align-items:flex-start;gap:4px}}
