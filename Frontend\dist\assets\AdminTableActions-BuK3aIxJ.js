import{j as e,by as x,aV as j,bd as u}from"./index-ctFdmWBt.js";const w=({item:a,onView:t,onEdit:r,onDelete:s,permissions:c={view:!0,edit:!0,delete:!0},tooltips:n={view:"View Details",edit:"Edit",delete:"Delete"},className:b="",disabled:l=!1})=>{const d=(o,i)=>{l||!i||i(a)};return e.jsxs("div",{className:`admin-table-actions ${b}`,children:[c.view&&t&&e.jsx("button",{className:"admin-action-btn admin-action-view",title:n.view,onClick:()=>d("view",t),disabled:l,"aria-label":`${n.view} for ${(a==null?void 0:a.name)||(a==null?void 0:a.title)||"item"}`,children:e.jsx(x,{})}),c.edit&&r&&e.jsx("button",{className:"admin-action-btn admin-action-edit",title:n.edit,onClick:()=>d("edit",r),disabled:l,"aria-label":`${n.edit} ${(a==null?void 0:a.name)||(a==null?void 0:a.title)||"item"}`,children:e.jsx(j,{})}),c.delete&&s&&e.jsx("button",{className:"admin-action-btn admin-action-delete",title:n.delete,onClick:()=>d("delete",s),disabled:l,"aria-label":`${n.delete} ${(a==null?void 0:a.name)||(a==null?void 0:a.title)||"item"}`,children:e.jsx(u,{})})]})};export{w as A};
