.AdminUserManagement{display:flex;flex-direction:column;gap:var(--heading6)}.AdminUserManagement .AdminUserManagement__header{display:flex;justify-content:space-between;align-items:center;gap:var(--heading6);flex-wrap:wrap}.AdminUserManagement .header-left{flex:1;max-width:400px;min-width:280px}.AdminUserManagement .search-container{position:relative;display:flex;align-items:center}.AdminUserManagement .search-icon{position:absolute;left:var(--smallfont);color:var(--dark-gray);font-size:var(--basefont);z-index:1}.AdminUserManagement .search-input{width:100%;padding:var(--smallfont) var(--smallfont) var(--smallfont) 40px;border:1px solid var(--light-gray);border-radius:var(--border-radius);font-size:var(--basefont);background-color:var(--white);transition:all .3s ease;font-size:12px}.AdminUserManagement .search-input:focus{outline:none;border-color:var(--btn-color);box-shadow:0 0 0 3px #ee34251a}.AdminUserManagement .search-input::placeholder{color:var(--dark-gray)}.AdminUserManagement .header-right{display:flex;gap:var(--smallfont);flex-wrap:wrap}.AdminUserManagement .btn{display:flex;align-items:center;gap:var(--smallfont);padding:var(--smallfont) var(--basefont);border:none;border-radius:var(--border-radius);font-size:var(--smallfont);font-weight:600;cursor:pointer;transition:all .3s ease;text-decoration:none}.AdminUserManagement .btn.btn-primary{background-color:var(--btn-color);color:var(--white)}.AdminUserManagement .btn.btn-primary:hover{background-color:#d32f2f}.AdminUserManagement .btn.btn-outline{background-color:transparent;color:var(--secondary-color);border:1px solid var(--light-gray)}.AdminUserManagement .btn.btn-outline:hover{background-color:var(--bg-gray);color:var(--white)}.AdminUserManagement .btn.btn-danger{background-color:var(--btn-color);color:var(--white)}.AdminUserManagement .btn.btn-danger:hover{background-color:var(--btn-color)}.AdminUserManagement .AdminUserManagement__filters{display:flex;align-items:center;gap:var(--basefont);border-radius:var(--border-radius)}.AdminUserManagement .filter-group{display:flex;align-items:center;gap:var(--smallfont)}.AdminUserManagement .filter-icon{color:var(--dark-gray);font-size:var(--basefont)}.AdminUserManagement .filter-select{padding:var(--smallfont);border:1px solid var(--light-gray);border-radius:var(--border-radius);font-size:var(--smallfont);background-color:var(--white);cursor:pointer}.AdminUserManagement .filter-select:focus{outline:none;border-color:var(--btn-color)}.AdminUserManagement .bulk-actions{display:flex;align-items:center;gap:var(--smallfont);margin-left:auto}.AdminUserManagement .selected-count{font-size:var(--smallfont);color:var(--secondary-color);font-weight:600}.AdminUserManagement__main{display:flex;align-items:center;gap:10px;width:100%}.AdminUserManagement .AdminUserManagement__table{background-color:var(--white);border-radius:var(--border-radius);box-shadow:var(--box-shadow-light);overflow:hidden}.AdminUserManagement .table-container{overflow-x:auto;border:1px solid var(--light-gray);border-radius:var(--border-radius)}.AdminUserManagement .users-table{width:100%;border-collapse:collapse}.AdminUserManagement .users-table th,.AdminUserManagement .users-table td{padding:var(--basefont);text-align:left;border-bottom:1px solid var(--bg-gray)}.AdminUserManagement .users-table th{background-color:var(--bg-gray);font-weight:600;font-size:var(--smallfont);color:var(--secondary-color)}.AdminUserManagement .users-table td{font-size:var(--smallfont);color:var(--text-color)}.AdminUserManagement .users-table tr:hover{background-color:var(--bg-gray)}.AdminUserManagement .user-info{display:flex;align-items:center;gap:var(--smallfont)}.AdminUserManagement .user-avatar{width:40px;height:40px;border-radius:50%;background-color:var(--bg-blue);display:flex;align-items:center;justify-content:center;color:var(--btn-color);font-size:var(--basefont);overflow:hidden;flex-shrink:0}.AdminUserManagement .user-avatar img{width:40px;height:40px;object-fit:cover;object-position:center;border-radius:50%}.AdminUserManagement .user-details{display:flex;flex-direction:column}.AdminUserManagement .user-name{font-weight:600;color:var(--secondary-color)}.AdminUserManagement .role-badge{display:inline-block;padding:4px 8px;border-radius:12px;font-size:var(--extrasmallfont);font-weight:600;text-transform:capitalize}.AdminUserManagement .role-badge.buyer{background-color:#dbeafe;color:#1e40af}.AdminUserManagement .role-badge.seller{background-color:#dcfce7;color:#166534}.AdminUserManagement .role-badge.admin{background-color:#fef3c7;color:#92400e}.AdminUserManagement .status-toggle{display:flex;align-items:center;gap:var(--smallfont)}.AdminUserManagement .status-badge{display:inline-block;padding:4px 8px;border-radius:var(--border-radius);font-size:var(--extrasmallfont);font-weight:600;text-transform:capitalize}.AdminUserManagement .status-badge.active{background-color:#dcfce7;color:#166534}.AdminUserManagement .status-badge.inactive{background-color:#fef2f2;color:#991b1b}.AdminUserManagement .status-badge.deleted{background-color:#f3f4f6;color:#6b7280}.AdminUserManagement .toggle-btn{background:none;border:none;cursor:pointer;font-size:var(--heading6);transition:all .3s ease}.AdminUserManagement .toggle-on{color:#10b981}.AdminUserManagement .toggle-off{color:var(--light-gray)}.AdminUserManagement .toggle-btn:hover .toggle-on{color:#059669}.AdminUserManagement .toggle-btn:hover .toggle-off{color:var(--dark-gray)}.AdminUserManagement .table-actions{display:flex;gap:var(--smallfont);align-items:center;justify-content:center}.AdminUserManagement .btn-action{display:flex;align-items:center;justify-content:center;width:32px;height:32px;border:none;border-radius:var(--border-radius);font-size:var(--smallfont);cursor:pointer;transition:all .3s ease;position:relative}.AdminUserManagement .btn-action.edit{background-color:var(--bg-gray);color:var(--secondary-color)}.AdminUserManagement .btn-action.edit:hover{background-color:var(--light-gray);transform:scale(1.05)}.AdminUserManagement .btn-action.delete{background-color:#fef2f2;color:#ef4444}.AdminUserManagement .btn-action.delete:hover{background-color:#fee2e2;transform:scale(1.05)}.AdminUserManagement .btn-action:before{content:attr(title);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background-color:var(--secondary-color);color:var(--white);padding:4px 8px;border-radius:var(--border-radius);font-size:var(--extrasmallfont);white-space:nowrap;opacity:0;visibility:hidden;transition:all .3s ease;z-index:var(--z-index-tooltip);pointer-events:none}.AdminUserManagement .btn-action:hover:before{opacity:1;visibility:visible;bottom:calc(100% + 8px)}.AdminUserManagement .no-results{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--heading3);text-align:center}.AdminUserManagement .no-results-icon{font-size:var(--heading2);color:var(--light-gray);margin-bottom:var(--basefont)}.AdminUserManagement .no-results h3{margin:0 0 var(--smallfont) 0;color:var(--secondary-color)}.AdminUserManagement .no-results p{margin:0;color:var(--dark-gray);font-size:var(--smallfont)}.AdminUserManagement .AdminUserManagement__pagination{display:flex;justify-content:space-between;align-items:center;padding:var(--basefont);background-color:var(--white);border-radius:var(--border-radius)}.AdminUserManagement .pagination-info{font-size:var(--smallfont);color:var(--dark-gray)}.AdminUserManagement .pagination-controls{display:flex;align-items:center;gap:var(--smallfont)}.AdminUserManagement .page-number{display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:var(--border-radius);font-size:var(--smallfont);font-weight:600;cursor:pointer;transition:all .3s ease}.AdminUserManagement .page-number.active{background-color:var(--btn-color);color:var(--white)}@media (max-width: 1024px){.AdminUserManagement .AdminUserManagement__header{gap:var(--basefont)}.AdminUserManagement .header-left{max-width:350px}.AdminUserManagement .users-table{font-size:var(--smallfont)}.AdminUserManagement .users-table th,.AdminUserManagement .users-table td{padding:var(--smallfont) 8px}}@media (max-width: 768px){.AdminUserManagement .AdminUserManagement__header{flex-direction:column;align-items:stretch;gap:var(--smallfont)}.AdminUserManagement .header-left{max-width:none;min-width:auto}.AdminUserManagement .header-right{justify-content:flex-start}.AdminUserManagement .AdminUserManagement__filters{flex-wrap:wrap;gap:var(--smallfont);padding:var(--smallfont)}.AdminUserManagement .filter-group{flex:1;min-width:120px}.AdminUserManagement .bulk-actions{margin-left:0;margin-top:var(--smallfont);width:100%;justify-content:space-between}.AdminUserManagement .table-container{overflow-x:auto;-webkit-overflow-scrolling:touch}.AdminUserManagement .users-table{font-size:var(--extrasmallfont);min-width:600px}.AdminUserManagement .users-table th,.AdminUserManagement .users-table td{padding:var(--smallfont) 6px}.AdminUserManagement .user-avatar{width:32px;height:32px;font-size:var(--smallfont)}.AdminUserManagement .user-avatar img{width:32px;height:32px}.AdminUserManagement .user-details{gap:2px}.AdminUserManagement .user-name{font-size:var(--extrasmallfont)}.AdminUserManagement .table-actions{gap:4px}.AdminUserManagement .btn-action{width:28px;height:28px;font-size:var(--extrasmallfont)}.AdminUserManagement .AdminUserManagement__pagination{flex-direction:column;gap:var(--smallfont);align-items:center}.AdminUserManagement .pagination-controls{order:-1}}@media (max-width: 480px){.AdminUserManagement{gap:var(--smallfont)}.AdminUserManagement .AdminUserManagement__header{gap:8px}.AdminUserManagement .header-right{flex-direction:column;gap:8px}.AdminUserManagement .btn{width:100%;justify-content:center}.AdminUserManagement .AdminUserManagement__filters{flex-direction:column;align-items:stretch}.AdminUserManagement .filter-group{width:100%;justify-content:space-between}.AdminUserManagement .bulk-actions{flex-direction:column;gap:8px}.AdminUserManagement .users-table{min-width:500px}.AdminUserManagement .table-actions{flex-direction:column;gap:2px}.AdminUserManagement .btn-action{width:24px;height:24px;font-size:10px}.AdminUserManagement .btn-action:before{display:none}.AdminUserManagement .status-toggle{flex-direction:column;align-items:flex-start;gap:4px}.AdminUserManagement .toggle-btn{font-size:var(--smallfont)}}@media (max-width: 360px){.AdminUserManagement .AdminUserManagement__table{margin:0 -8px}.AdminUserManagement .table-container{border-radius:0}.AdminUserManagement .users-table{min-width:450px}.AdminUserManagement .user-info{gap:6px}.AdminUserManagement .user-avatar{width:28px;height:28px;font-size:10px}.AdminUserManagement .user-avatar img{width:28px;height:28px}}
