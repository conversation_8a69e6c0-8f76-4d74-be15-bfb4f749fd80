import{b as W,d as N,bl as X,bm as Y,bn as Z,r as c,bo as o,j as t,b5 as ee,bp as te,bq as D,br as ae,bs as se,bt as le,bu as r,bv as ne,bw as _,bi as h,k as g,s as u,bh as ie,bk as ce,bx as oe}from"./index-ctFdmWBt.js";import{A as re}from"./AdminLayout-D3bHW2Uz.js";import{U as de}from"./UserDetailModal-30vfmYkc.js";import{C as me}from"./ConfirmationModal-3QP_xvbZ.js";import{T as he}from"./Table-j5pMA9qi.js";import{A as ge}from"./AdminTableActions-BuK3aIxJ.js";const we=()=>{var S,A;const s=W(),U=N(X),l=N(Y),k=N(Z),[p,C]=c.useState(""),[x,$]=c.useState("all"),[y,F]=c.useState("all"),[n,d]=c.useState({isOpen:!1,type:"single",user:null,userIds:[],action:null}),[I,b]=c.useState(!1);c.useEffect(()=>{s(o())},[s]);const m=(U.data||[]).filter(e=>{const a=(e==null?void 0:e.firstName)||"",i=(e==null?void 0:e.lastName)||"",f=(e==null?void 0:e.email)||"",w=(e==null?void 0:e.role)||"",H=(e==null?void 0:e.status)||"";if(w==="admin")return!1;const K=a.toLowerCase().includes(p.toLowerCase())||i.toLowerCase().includes(p.toLowerCase())||f.toLowerCase().includes(p.toLowerCase()),Q=w==="admin"?w:(e==null?void 0:e.activeRole)||"buyer";return K&&(x==="all"||Q===x)&&(y==="all"||H===y)}),M=e=>{e.target.checked?s(r(m.map(a=>a.id))):s(r([]))},O=e=>{const a=l.includes(e)?l.filter(i=>i!==e):[...l,e];s(r(a))},v=(e,a)=>{switch(a){case"view":case"edit":s(ne(e));break;case"delete":T(e);break;case"toggle":L(e);break}},T=e=>{d({isOpen:!0,type:"single",user:e,userIds:[],action:"delete"})},R=async e=>{try{b(!0),await s(ce(e.id||e._id)).unwrap(),s(h({id:Date.now(),type:"user_deletion",description:`User deleted: ${e.firstName} ${e.lastName}`,timestamp:new Date().toISOString(),user:"Admin"})),s(o()),g(`User "${e.firstName} ${e.lastName}" has been deleted successfully!`),d({isOpen:!1,type:"single",user:null,userIds:[],action:null})}catch(a){console.error("Failed to delete user:",a),u(`Failed to delete user: ${a.message||"Please try again."}`)}finally{b(!1)}},L=async e=>{const a=e.status===1?0:1,i=a===1?"activate":"deactivate";if(window.confirm(`Are you sure you want to ${i} this user?`))try{await s(ie({id:e.id||e._id,userData:{status:a}})).unwrap(),s(h({id:Date.now(),type:"user_status_change",description:`User ${a===1?"activated":"deactivated"}: ${e.firstName} ${e.lastName}`,timestamp:new Date().toISOString(),user:"Admin"})),s(o()),g(`User has been ${i}d successfully!`)}catch(f){console.error("Failed to update user status:",f),u(`Failed to ${i} user: ${f.message||"Please try again."}`)}},j=async e=>{if(l.length===0){alert("Please select users first");return}switch(e){case"activate":if(window.confirm(`Activate ${l.length} selected users?`))try{await s(_({userIds:l,action:"activate"})).unwrap(),s(h({id:Date.now(),type:"bulk_user_activation",description:`Bulk activated ${l.length} users`,timestamp:new Date().toISOString(),user:"Admin"})),s(r([])),s(o()),g(`${l.length} users have been activated successfully!`)}catch(a){console.error("Failed to activate users:",a),u(`Failed to activate users: ${a.message||"Please try again."}`)}break;case"deactivate":if(window.confirm(`Deactivate ${l.length} selected users?`))try{await s(_({userIds:l,action:"deactivate"})).unwrap(),s(h({id:Date.now(),type:"bulk_user_deactivation",description:`Bulk deactivated ${l.length} users`,timestamp:new Date().toISOString(),user:"Admin"})),s(r([])),s(o()),g(`${l.length} users have been deactivated successfully!`)}catch(a){console.error("Failed to deactivate users:",a),u(`Failed to deactivate users: ${a.message||"Please try again."}`)}break;case"delete":d({isOpen:!0,type:"bulk",user:null,userIds:l,action:"delete"});break}},B=async e=>{try{b(!0),await s(oe(e)).unwrap(),s(h({id:Date.now(),type:"bulk_user_deletion",description:`Bulk deleted ${e.length} users`,timestamp:new Date().toISOString(),user:"Admin"})),s(r([])),s(o()),g(`${e.length} users have been deleted successfully!`),d({isOpen:!1,type:"bulk",user:null,userIds:[],action:null})}catch(a){console.error("Failed to delete users:",a),u(`Failed to delete users: ${a.message||"Please try again."}`)}finally{b(!1)}},E=()=>{n.type==="single"&&n.user?R(n.user):n.type==="bulk"&&n.userIds.length>0&&B(n.userIds)},P=()=>{d({isOpen:!1,type:"single",user:null,userIds:[],action:null})},J=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),V=e=>{switch(e){case 1:return"status-badge active";case 0:return"status-badge inactive";case-1:return"status-badge deleted";default:return"status-badge inactive"}},q=e=>{switch(e){case 1:return"Active";case 0:return"Inactive";case-1:return"Deleted";default:return"Unknown"}},G=e=>{switch(e){case"buyer":return"role-badge buyer";case"seller":return"role-badge seller";case"admin":return"role-badge admin";default:return"role-badge"}},z=[{key:"select",label:t.jsx("input",{type:"checkbox",onChange:M,checked:l.length===m.length&&m.length>0}),render:e=>t.jsx("input",{type:"checkbox",checked:l.includes((e==null?void 0:e._id)||(e==null?void 0:e.id)),onChange:()=>O((e==null?void 0:e._id)||(e==null?void 0:e.id))}),className:"select-column"},{key:"user",label:"User",render:e=>t.jsxs("div",{className:"user-info",children:[t.jsx("div",{className:"user-avatar",children:e!=null&&e.profileImage?t.jsx("img",{src:ee+e.profileImage,alt:(e==null?void 0:e.firstName)||"User"}):(((e==null?void 0:e.role)==="admin"?e==null?void 0:e.role:e==null?void 0:e.activeRole)||"buyer")==="seller"?t.jsx(te,{}):t.jsx(D,{})}),t.jsx("div",{className:"user-details",children:t.jsxs("span",{className:"user-name",children:[(e==null?void 0:e.firstName)||"Unknown"," ",(e==null?void 0:e.lastName)||"User"]})})]})},{key:"email",label:"Email",render:e=>(e==null?void 0:e.email)||"No email"},{key:"role",label:"Role",render:e=>{const a=(e==null?void 0:e.role)==="admin"?e==null?void 0:e.role:e==null?void 0:e.activeRole;return t.jsx("span",{className:G(a),children:a||"buyer"})}},{key:"status",label:"Status",render:e=>t.jsxs("div",{className:"status-toggle",children:[t.jsx("span",{className:V(e==null?void 0:e.status),children:q(e==null?void 0:e.status)}),t.jsx("button",{className:"toggle-btn",onClick:()=>v(e,"toggle"),title:`${(e==null?void 0:e.status)===1?"Deactivate":"Activate"} user`,disabled:(e==null?void 0:e.status)===-1,children:(e==null?void 0:e.status)===1?t.jsx(ae,{className:"toggle-on"}):t.jsx(se,{className:"toggle-off"})})]})},{key:"dateJoined",label:"Join Date",render:e=>J((e==null?void 0:e.dateJoined)||new Date)},{key:"actions",label:"Actions",render:e=>t.jsx(ge,{item:e,onView:()=>v(e,"view"),onEdit:()=>v(e,"edit"),onDelete:()=>v(e,"delete"),tooltips:{view:"View User",edit:"Edit User",delete:"Delete User"}}),className:"actions-column"}];return t.jsx(re,{children:t.jsxs("div",{className:"AdminUserManagement",children:[t.jsxs("div",{className:"AdminUserManagement__main",children:[t.jsx("div",{className:"AdminUserManagement__header",children:t.jsx("div",{className:"header-left",children:t.jsxs("div",{className:"search-container",children:[t.jsx(le,{className:"search-icon"}),t.jsx("input",{type:"text",placeholder:"Search users by name or email...",value:p,onChange:e=>C(e.target.value),className:"search-input"})]})})}),t.jsxs("div",{className:"AdminUserManagement__filters",children:[t.jsx("div",{className:"filter-group",children:t.jsxs("select",{value:x,onChange:e=>$(e.target.value),className:"filter-select",children:[t.jsx("option",{value:"all",children:"All Roles"}),t.jsx("option",{value:"buyer",children:"Buyers"}),t.jsx("option",{value:"seller",children:"Sellers"}),t.jsx("option",{value:"admin",children:"Admins"})]})}),t.jsx("div",{className:"filter-group",children:t.jsxs("select",{value:y,onChange:e=>F(e.target.value),className:"filter-select",children:[t.jsx("option",{value:"all",children:"All Status"}),t.jsx("option",{value:"active",children:"Active"}),t.jsx("option",{value:"inactive",children:"Inactive"})]})}),l.length>0&&t.jsxs("div",{className:"bulk-actions",children:[t.jsxs("span",{className:"selected-count",children:[l.length," selected"]}),t.jsx("button",{className:"btn btn-outline",onClick:()=>j("activate"),children:"Activate"}),t.jsx("button",{className:"btn btn-outline",onClick:()=>j("deactivate"),children:"Deactivate"}),t.jsx("button",{className:"btn btn-danger",onClick:()=>j("delete"),children:"Delete"})]})]})]}),t.jsx("div",{className:"AdminUserManagement__table",children:t.jsx(he,{columns:z,data:m,isAdmin:!0,loading:{isLoading:(S=k.loading)==null?void 0:S.users,message:"Loading users..."},emptyMessage:t.jsxs("div",{className:"no-results",children:[t.jsx(D,{className:"no-results-icon"}),t.jsx("h3",{children:"No users found"}),t.jsx("p",{children:"Try adjusting your search or filter criteria"})]}),className:"users-table"})}),t.jsxs("div",{className:"AdminUserManagement__pagination",children:[t.jsxs("div",{className:"pagination-info",children:["Showing ",m.length," of ",((A=U.data)==null?void 0:A.length)||0," users"]}),t.jsxs("div",{className:"pagination-controls",children:[t.jsx("button",{className:"btn btn-outline",disabled:!0,children:"Previous"}),t.jsx("span",{className:"page-number active",children:"1"}),t.jsx("button",{className:"btn btn-outline",disabled:!0,children:"Next"})]})]}),k.showUserDetailModal&&t.jsx(de,{}),t.jsx(me,{isOpen:n.isOpen,onClose:P,onConfirm:E,title:n.type==="single"?"Delete User":"Delete Users",message:n.type==="single"&&n.user?t.jsxs("div",{children:[t.jsxs("p",{children:["Are you sure you want to delete user"," ",t.jsxs("strong",{children:['"',n.user.firstName," ",n.user.lastName,'"']}),"?"]}),t.jsx("p",{className:"warning-text"})]}):n.type==="bulk"?t.jsxs("div",{children:[t.jsxs("p",{children:["Are you sure you want to delete"," ",t.jsxs("strong",{children:[n.userIds.length," selected users"]}),"?"]}),t.jsx("p",{className:"warning-text",children:"This action will mark all selected users as deleted but preserve their data for integrity purposes."})]}):null,confirmText:"Delete",cancelText:"Cancel",type:"danger",isLoading:I})]})})};export{we as default};
