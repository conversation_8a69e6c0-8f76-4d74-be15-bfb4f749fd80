const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-ctFdmWBt.js","assets/index-CieivHBP.css"])))=>i.map(i=>d[i]);
import{c as C,b as j,d as P,r as m,j as n,L as N,o as f,p as k,t as i,q as I,v as L,_ as G}from"./index-ctFdmWBt.js";import{P as _,G as D,f as y,v as O}from"./firebaseService-Bi10J-DQ.js";const F=()=>{const c=C(),l=j(),{isLoading:u}=P(o=>o.auth),[r,g]=m.useState({phone:"",countryCode:"+91"}),[d,p]=m.useState({}),v=o=>{const{name:e,value:a,type:s,checked:t}=o.target;g({...r,[e]:s==="checkbox"?t:a}),d[e]&&p({...d,[e]:null})},b=o=>{g({...r,countryCode:o.target.value})},S=()=>{const o={},e=O(r.countryCode,r.phone);return e.isValid||(o.phone=e.error),o},w=async o=>{var a;o.preventDefault();const e=S();if(Object.keys(e).length>0){p(e);return}l(f());try{const s=`${r.countryCode}${r.phone}`,t=await l(k({mobile:s})).unwrap();i.otp.success("OTP sent successfully!"),c("/otp-verification",{state:{userId:t.userId,phoneNumber:`${r.countryCode} ${r.phone}`,cooldownSeconds:t.cooldownSeconds||60,isLogin:!0,developmentOtp:t.developmentOtp}})}catch(s){console.error("Login error:",s);let t="Login failed. Please try again.";if(typeof s=="string"?t=s:s!=null&&s.message&&(t=s.message),t.includes("wait")&&t.includes("seconds")){const h=((a=t.match(/\d+/))==null?void 0:a[0])||60;i.otp.cooldown(parseInt(h))}else t.includes("User not found")?i.error("No account found with this mobile number. Please sign up first."):i.error(t)}},x=async()=>{try{if(l(f()),!y.isInitialized()){i.error("Firebase is not initialized. Please check your configuration.");return}const o=await y.signInWithGoogle();try{const e=await l(I(o.idToken)).unwrap();if(i.auth.loginSuccess(),e.user.role==="buyer")c("/content");else if(e.user.role==="seller"){const a=L(e.user);c(a)}else e.user.role==="admin"?c("/admin/dashboard"):c("/")}catch(e){const a=typeof e=="string"?e:(e==null?void 0:e.message)||"";if(a.includes("not found")||a.includes("does not exist"))try{const{googleSignUp:s}=await G(async()=>{const{googleSignUp:t}=await import("./index-ctFdmWBt.js").then(h=>h.eL);return{googleSignUp:t}},__vite__mapDeps([0,1]));await l(s({idToken:o.idToken,role:"buyer"})).unwrap(),i.auth.registrationSuccess(),c("/content")}catch(s){console.error("Google sign-up error:",s);let t="Failed to complete registration. Please try again.";typeof s=="string"?t=s:s!=null&&s.message&&(t=s.message),i.error(t)}else throw e}}catch(o){console.error("Google sign-in error:",o);let e="Failed to sign in with Google. Please try again.";typeof o=="string"?e=o:o!=null&&o.message&&(e=o.message),i.error(e)}};return n.jsx("div",{className:"auth-page auth-container",children:n.jsxs("div",{className:"auth-form-container",children:[n.jsx("h1",{className:"auth-title",children:"Login in to your account"}),n.jsxs("form",{onSubmit:w,className:"auth-form",children:[n.jsx("div",{className:"auth-form-input form-input-container",children:n.jsx(_,{countryCode:r.countryCode,phone:r.phone,onCountryCodeChange:b,onPhoneChange:v,error:d.phone,placeholder:"00000 00000",required:!0,name:"phone"})}),n.jsx("button",{type:"submit",className:"signin-button btn-primary",disabled:u,children:u?"Sending OTP...":"Send Code Now"}),n.jsx("div",{className:"auth-divider",children:n.jsx("span",{children:"or"})}),n.jsx(D,{onClick:x,isLoading:u,text:"Sign in with Google",variant:"secondary"}),n.jsxs("p",{className:"signup-link mt-10",children:["Don't have an account? ",n.jsx(N,{to:"/signup",children:"Sign Up"})]})]})]})})};export{F as default};
