import{b as d,c as o,d as l,cb as n,j as a,ae as b,bM as t,a6 as u,cc as _,aM as y,aW as h,cd as m,R as x,ce as j}from"./index-ctFdmWBt.js";const S=()=>{const c=d(),e=o(),r=l(n),s=i=>{switch(c(x(i)),i){case"dashboard":e("/buyer/account/dashboard");break;case"profile":e("/buyer/account/profile");break;case"downloads":e("/buyer/account/downloads");break;case"requests":e("/buyer/account/requests");break;case"bids":e("/buyer/account/bids");break;case"offers":e("/buyer/account/offers");break;case"cards":e("/buyer/account/cards");break;case"logout":c(j()),e("/");break;default:e("/buyer/account/dashboard")}};return a.jsx("div",{className:"BuyerSidebar",children:a.jsx("div",{className:"BuyerSidebar__container",children:a.jsxs("ul",{className:"BuyerSidebar__menu",children:[a.jsxs("li",{className:`BuyerSidebar__item ${r==="dashboard"?"active":""}`,onClick:()=>s("dashboard"),children:[a.jsx(b,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"Dashboard"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="downloads"?"active":""}`,onClick:()=>s("downloads"),children:[a.jsx(t,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Downloads"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="bids"?"active":""}`,onClick:()=>s("bids"),children:[a.jsx(u,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Bids"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="offers"?"active":""}`,onClick:()=>s("offers"),children:[a.jsx(_,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Offers"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="cards"?"active":""}`,onClick:()=>s("cards"),children:[a.jsx(y,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Cards"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="profile"?"active":""}`,onClick:()=>s("profile"),children:[a.jsx(h,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Profile"})]}),a.jsxs("li",{className:"BuyerSidebar__item BuyerSidebar__logout",onClick:()=>s("logout"),children:[a.jsx(m,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"Logout"})]})]})})})};export{S as B};
