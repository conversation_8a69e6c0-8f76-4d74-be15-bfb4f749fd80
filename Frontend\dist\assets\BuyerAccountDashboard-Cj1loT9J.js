import{j as s,b as W,c as z,d as h,cm as G,cw as H,cx as Z,cy as J,cz as K,bZ as Q,b_ as X,r as Y,cA as R,cn as _,cB as q,cC as C,cD as M,ae as $,P as v,bM as ss,a5 as es,a6 as as,a7 as ts,L as g,ax as k,c8 as ls,b5 as A}from"./index-ctFdmWBt.js";import{T as w}from"./Table-j5pMA9qi.js";import{D as cs,a as D,L as S}from"./LoadingSkeleton-DFCyGuTF.js";import{f as B}from"./dateValidation-cL5kH0gD.js";const F=({children:a,title:f,icon:i,className:d=""})=>s.jsxs("div",{className:`SectionWrapper ${d}`,children:[f&&s.jsx("div",{className:"bordrdiv mb-30 ",children:s.jsxs("h2",{className:"SectionWrapper__title",children:[i,f]})}),s.jsx("div",{className:"SectionWrapper__content",children:a})]}),ns=()=>{const a=W(),f=z(),i=h(G),d=h(H),u=h(Z),x=h(J),l=h(K),c=h(Q),t=h(X);Y.useEffect(()=>{a(R()),a(_({limit:2})),a(q({limit:2})),a(C({limit:2})),a(M({limit:2}))},[a]);const p=e=>{f(e)},n=e=>{switch(a(ls(e)),e){case"stats":a(R());break;case"downloads":a(_({limit:2}));break;case"requests":a(q({limit:2}));break;case"bids":a(C({limit:2}));break;case"offers":a(M({limit:2}));break}};if(c.stats&&c.downloads&&c.requests&&c.bids&&c.offers)return s.jsx("div",{className:"BuyerAccountDashboard",children:s.jsx(F,{icon:s.jsx($,{className:"BuyerSidebar__icon"}),title:"Dashboard",children:s.jsx(cs,{})})});const O=[{key:"no",label:"No.",className:"no"},{key:"order-id",label:"Order Id",className:"order-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"amount",label:"Amount",className:"amount"},{key:"status",label:"Status",className:"status"}],U=[{key:"no",label:"No.",className:"no"},{key:"order-id",label:"Order Id",className:"order-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"amount",label:"Requested Amount",className:"amount"},{key:"status",label:"Status",className:"status"}],E=[{key:"no",label:"No.",className:"no"},{key:"order-id",label:"Bid Id",className:"order-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"amount",label:"Bid Amount",className:"amount"},{key:"status",label:"Status",className:"status"}],L=[{key:"no",label:"No.",className:"no"},{key:"order-id",label:"Offer Id",className:"order-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"amount",label:"Offer Amount",className:"amount"},{key:"status",label:"Status",className:"status"}],V=(e,m)=>{var r,o;return[s.jsx("td",{className:"no",children:m+1},"no"),s.jsxs("td",{className:"order-id",children:["#",((r=e.orderId)==null?void 0:r.slice(-8))||e.id]},"order-id"),s.jsx("td",{className:"video",children:s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:s.jsx("img",{src:e.thumbnailUrl?`${A}${e.thumbnailUrl}`:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:e.title})}),s.jsxs("div",{className:"content-info",children:[s.jsx("div",{className:"content-title",children:e.title}),s.jsxs("div",{className:"content-coach",children:["By ",e.coach]})]})]})},"video"),s.jsx("td",{className:"date",children:B(e.downloadDate)},"date"),s.jsxs("td",{className:"amount",children:["$",((o=e.amount)==null?void 0:o.toFixed(2))||"0.00"]},"amount"),s.jsx("td",{className:"status",children:s.jsx("span",{className:"status-badge downloaded",children:"Downloaded"})},"status")]},I=(e,m)=>[s.jsx("td",{className:"no",children:m+1},"no"),s.jsxs("td",{className:"order-id",children:["#",e.id]},"order-id"),s.jsx("td",{className:"video",children:s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:s.jsx("img",{src:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:e.title})}),s.jsxs("div",{className:"content-info",children:[s.jsx("div",{className:"content-title",children:e.title}),s.jsx("div",{className:"content-coach",children:"By Coach"})]})]})},"video"),s.jsxs("td",{className:"date",children:[e.date," | 4:30PM"]},"date"),s.jsxs("td",{className:"amount",children:["$",(Math.random()*30+20).toFixed(2)]},"amount"),s.jsx("td",{className:"status",children:s.jsx("span",{className:`status-badge ${e.status}`,children:e.status})},"status")],T=(e,m)=>{var r,o,N,j,b,y;return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"table-cell no",children:m+1}),s.jsxs("div",{className:"table-cell order-id",children:["#",((r=e._id)==null?void 0:r.slice(-6))||"N/A"]}),s.jsx("div",{className:"table-cell video",children:s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:s.jsx("img",{src:(o=e.content)!=null&&o.thumbnailUrl?`${A}${e.content.thumbnailUrl}`:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:(N=e.content)==null?void 0:N.title})}),s.jsxs("div",{className:"content-info",children:[s.jsx("div",{className:"content-title",children:(j=e.content)==null?void 0:j.title}),s.jsxs("div",{className:"content-coach",children:["By"," ",(b=e.content)!=null&&b.seller?`${e.content.seller.firstName} ${e.content.seller.lastName}`:"Unknown Coach"]})]})]})}),s.jsx("div",{className:"table-cell date",children:B(e.createdAt)}),s.jsxs("div",{className:"table-cell amount",children:["$",(e.amount||0).toFixed(2)]}),s.jsx("div",{className:"table-cell status",children:s.jsx("span",{className:`status-badge ${(y=e.status)==null?void 0:y.toLowerCase()}`,children:e.status})})]})},P=(e,m)=>{var r,o,N,j,b,y;return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"table-cell no",children:m+1}),s.jsxs("div",{className:"table-cell order-id",children:["#",((r=e._id)==null?void 0:r.slice(-6))||"N/A"]}),s.jsx("div",{className:"table-cell video",children:s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:s.jsx("img",{src:(o=e.content)!=null&&o.thumbnailUrl?`${A}${e.content.thumbnailUrl}`:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:(N=e.content)==null?void 0:N.title})}),s.jsxs("div",{className:"content-info",children:[s.jsx("div",{className:"content-title",children:(j=e.content)==null?void 0:j.title}),s.jsxs("div",{className:"content-coach",children:["By"," ",(b=e.content)!=null&&b.seller?`${e.content.seller.firstName} ${e.content.seller.lastName}`:"Unknown Coach"]})]})]})}),s.jsx("div",{className:"table-cell date",children:B(e.createdAt)}),s.jsxs("div",{className:"table-cell amount",children:["$",(e.amount||0).toFixed(2)]}),s.jsx("div",{className:"table-cell status",children:s.jsx("span",{className:`status-badge ${(y=e.status)==null?void 0:y.toLowerCase()}`,children:e.status})})]})};return s.jsx("div",{className:"BuyerAccountDashboard",children:s.jsxs(F,{icon:s.jsx($,{className:"BuyerSidebar__icon"}),title:"Dashboard",children:[t.stats?s.jsx(v,{error:t.stats,onRetry:()=>n("stats"),title:"Failed to load dashboard stats",className:"stats-error"}):s.jsx("div",{className:"stats",children:c.stats?s.jsxs(s.Fragment,{children:[s.jsx(D,{}),s.jsx(D,{}),s.jsx(D,{}),s.jsx(D,{})]}):s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"stat-card downloads",onClick:()=>p("/buyer/account/downloads"),style:{cursor:"pointer"},children:[s.jsxs("div",{className:"stat-number",children:[((l==null?void 0:l.totalDownloads)||(i==null?void 0:i.length)||0).toString().padStart(2,"0"),s.jsx("div",{className:"stat-label",children:"Downloads"})]}),s.jsx("div",{className:"icon-round",children:s.jsx(ss,{})})]}),s.jsxs("div",{className:"stat-card requests",onClick:()=>p("/buyer/account/requests"),style:{cursor:"pointer"},children:[s.jsxs("div",{className:"stat-number",children:[((l==null?void 0:l.totalRequests)||(d==null?void 0:d.length)||0).toString().padStart(2,"0"),s.jsx("div",{className:"stat-label",children:"Requests"})]}),s.jsx("div",{className:"icon-roundtwo",children:s.jsx(es,{})})]}),s.jsxs("div",{className:"stat-card bids",onClick:()=>p("/buyer/account/bids"),style:{cursor:"pointer"},children:[s.jsxs("div",{className:"stat-number",children:[((l==null?void 0:l.totalBids)||(u==null?void 0:u.length)||0).toString().padStart(2,"0"),s.jsx("div",{className:"stat-label",children:"Bids"})]}),s.jsx("div",{className:"icon-roundthree",children:s.jsx(as,{})})]}),s.jsxs("div",{className:"stat-card offers",onClick:()=>p("/buyer/account/offers"),style:{cursor:"pointer"},children:[s.jsxs("div",{className:"stat-number",children:[((l==null?void 0:l.totalOffers)||(x==null?void 0:x.length)||0).toString().padStart(2,"0"),s.jsx("div",{className:"stat-label",children:"Offers"})]}),s.jsx("div",{className:"icon-roundfour",children:s.jsx(ts,{})})]})]})}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{className:"section-title",children:"My Downloads"}),s.jsxs("div",{className:"section-actions",children:[s.jsx(g,{to:"/buyer/account/downloads",className:"view-all",children:"View All Downloads"}),t.downloads&&s.jsx("button",{className:"retry-btn",onClick:()=>n("downloads"),title:"Retry loading downloads",children:s.jsx(k,{})})]})]}),t.downloads?s.jsx(v,{error:t.downloads,onRetry:()=>n("downloads"),title:"Failed to load downloads"}):c.downloads?s.jsx(S,{count:2,height:"60px",className:"table-row-skeleton"}):s.jsx(w,{columns:O,data:Array.isArray(i)?i.slice(0,2):[],renderRow:V,className:"BuyerAccountDashboard__downloads-table",emptyMessage:"No downloads yet."})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{className:"section-title",children:"My Requests"}),s.jsxs("div",{className:"section-actions",children:[s.jsx(g,{to:"/buyer/account/requests",className:"view-all",children:"View All Requests"}),t.requests&&s.jsx("button",{className:"retry-btn",onClick:()=>n("requests"),title:"Retry loading requests",children:s.jsx(k,{})})]})]}),t.requests?s.jsx(v,{error:t.requests,onRetry:()=>n("requests"),title:"Failed to load requests"}):c.requests?s.jsx(S,{count:2,height:"60px",className:"table-row-skeleton"}):s.jsx(w,{columns:U,data:Array.isArray(d)?d.slice(0,2):[],renderRow:I,className:"BuyerAccountDashboard__requests-table",emptyMessage:"No requests yet."})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{className:"section-title",children:"My Bids"}),s.jsxs("div",{className:"section-actions",children:[s.jsx(g,{to:"/buyer/account/bids",className:"view-all",children:"View All Bids"}),t.bids&&s.jsx("button",{className:"retry-btn",onClick:()=>n("bids"),title:"Retry loading bids",children:s.jsx(k,{})})]})]}),t.bids?s.jsx(v,{error:t.bids,onRetry:()=>n("bids"),title:"Failed to load bids"}):c.bids?s.jsx(S,{count:2,height:"60px",className:"table-row-skeleton"}):s.jsx(w,{columns:E,data:Array.isArray(u)?u.slice(0,2):[],renderRow:T,variant:"grid",gridTemplate:"0.5fr 1fr 3fr 1.5fr 1fr 1fr",className:"BuyerAccountDashboard__bids-table",emptyMessage:"No bids yet."})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{className:"section-title",children:"My Offers"}),s.jsxs("div",{className:"section-actions",children:[s.jsx(g,{to:"/buyer/account/offers",className:"view-all",children:"View All Offers"}),t.offers&&s.jsx("button",{className:"retry-btn",onClick:()=>n("offers"),title:"Retry loading offers",children:s.jsx(k,{})})]})]}),t.offers?s.jsx(v,{error:t.offers,onRetry:()=>n("offers"),title:"Failed to load offers"}):c.offers?s.jsx(S,{count:2,height:"60px",className:"table-row-skeleton"}):s.jsx(w,{columns:L,data:Array.isArray(x)?x.slice(0,2):[],renderRow:P,variant:"grid",gridTemplate:"0.5fr 1fr 3fr 1.5fr 1fr 1fr",className:"BuyerAccountDashboard__offers-table",emptyMessage:"No offers yet."})]})]})})},ms=Object.freeze(Object.defineProperty({__proto__:null,default:ns},Symbol.toStringTag,{value:"Module"}));export{ns as B,F as S,ms as a};
