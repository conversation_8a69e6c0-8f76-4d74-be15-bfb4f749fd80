import{a as k,c as A,b as C,d as I,r as i,Q as N,R as B,j as e,P as L,S as R,e as P,f as g}from"./index-ctFdmWBt.js";import{L as U}from"./LoadingSkeleton-DFCyGuTF.js";import{f as $}from"./dateValidation-cL5kH0gD.js";import l from"./BuyerAccount-DRF3BFmz.js";import{D as F}from"./DynamicHeading-CldNZOm6.js";/* empty css                        */import"./timezoneUtils-BuH33ask.js";import"./BuyerAccount-OySet9IK.js";import"./BuyerAccountDashboard-Cj1loT9J.js";import"./Table-j5pMA9qi.js";import"./Pagination-GDpQMGEO.js";import"./stripe-DT3_Ek51.js";/* empty css                       */const ee=()=>{var m,h,x,j,u,_,p,O,D;const{id:n}=k(),o=A(),r=C(),{buyerOffers:a,isLoading:v,isError:b,error:c}=I(t=>t.offer),[s,y]=i.useState(null),[w,T]=i.useState(!1);i.useEffect(()=>{(!a||a.length===0)&&r(N())},[r,a]),i.useEffect(()=>{if(a&&a.length>0&&n){const t=a.find(f=>f._id===n);y(t)}},[a,n]),i.useEffect(()=>{r(B("offers"))},[r]);const S=t=>$(t),d=t=>`$${parseFloat(t).toFixed(2)}`,E=t=>{const f={Pending:"status-pending",Accepted:"status-accepted",Rejected:"status-rejected",Cancelled:"status-cancelled",Expired:"status-expired"};return e.jsx("span",{className:`status-badge ${f[t]||""}`,children:t})};return v&&!s?e.jsx(l,{children:e.jsx("div",{className:"OfferDetails",children:e.jsx(U,{type:"table",rows:1})})}):b?e.jsx(l,{children:e.jsx("div",{className:"OfferDetails",children:e.jsx(L,{title:"Error Loading Offer Details",message:(c==null?void 0:c.message)||"Failed to load offer details",onRetry:()=>r(N())})})}):s?e.jsx(l,{children:e.jsxs("div",{className:"OfferDetails",children:[e.jsx("div",{className:"bordrdiv mb-30",children:e.jsx(F,{title:"Offer Details",onBack:()=>o("/buyer/account/offers"),backIcon:e.jsx(R,{style:{fontSize:20}}),backLabel:"Back"})}),e.jsx("div",{className:"OfferDetails__content",children:e.jsxs("div",{className:"OfferDetails__main-section",children:[e.jsx("div",{className:"OfferDetails__content-card",children:e.jsxs("div",{className:"OfferDetails__content-info",children:[e.jsx("img",{src:(m=s.content)!=null&&m.thumbnailUrl?P(s.content.thumbnailUrl):g(200,120,"No image"),alt:((h=s.content)==null?void 0:h.title)||"Content",className:"OfferDetails__content-image",onError:t=>{t.target.src=g(200,120,"Image not found")}}),e.jsxs("div",{className:"OfferDetails__content-details",children:[e.jsx("h3",{className:"OfferDetails__content-title",children:((x=s.content)==null?void 0:x.title)||"Untitled Content"}),e.jsx("p",{className:"OfferDetails__content-sport",children:((j=s.content)==null?void 0:j.sport)||"Sports Content"}),e.jsxs("p",{className:"OfferDetails__content-price",children:["Listed Price:"," ",d(((u=s.content)==null?void 0:u.price)||0)]})]})]})}),e.jsxs("div",{className:"OfferDetails__offer-card",children:[e.jsx("h4",{children:"Offer Information"}),e.jsxs("div",{className:"OfferDetails__info-grid",children:[e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Offer ID:"}),e.jsxs("span",{children:["#",(_=s._id)==null?void 0:_.substring(0,8)]})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Offer Amount:"}),e.jsx("span",{className:"OfferDetails__amount",children:d(s.amount)})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Date Submitted:"}),e.jsx("span",{children:S(s.createdAt)})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Status:"}),E(s.status)]})]})]}),e.jsxs("div",{className:"OfferDetails__buyer-card",children:[e.jsx("h4",{children:"Seller Information"}),e.jsxs("div",{className:"OfferDetails__buyer-info",children:[e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Name:"}),e.jsx("span",{children:`${((p=s.seller)==null?void 0:p.firstName)||""} ${((O=s.seller)==null?void 0:O.lastName)||""}`.trim()||"Unknown Seller"})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Email:"}),e.jsx("span",{children:((D=s.seller)==null?void 0:D.email)||"N/A"})]})]})]}),s.message&&e.jsxs("div",{className:"OfferDetails__message-card",children:[e.jsx("h4",{children:"Your Message"}),e.jsx("div",{className:"OfferDetails__message-content",children:s.message})]}),s.sellerResponse&&e.jsxs("div",{className:"OfferDetails__response-card",children:[e.jsx("h4",{children:"Seller's Response"}),e.jsx("div",{className:"OfferDetails__response-content",children:s.sellerResponse})]})]})})]})}):e.jsx(l,{children:e.jsx("div",{className:"OfferDetails",children:e.jsxs("div",{className:"OfferDetails__error",children:[e.jsx("h3",{children:"Offer Not Found"}),e.jsx("p",{children:"The offer you're looking for could not be found."}),e.jsx("button",{className:"btn-primary",onClick:()=>o("/buyer/account/offers"),children:"Back to Offers"})]})})})};export{ee as default};
