import{a as x,c as p,r as n,j as e,J as h}from"./index-ctFdmWBt.js";const j=()=>{const{slug:r}=x(),d=p(),[a,m]=n.useState(null),[g,i]=n.useState(!0),[c,s]=n.useState(null);return n.useEffect(()=>{r&&(async()=>{try{i(!0);const t=await fetch(`http://localhost:5000/api/cms/${r}`);if(!t.ok){t.status===404?s("Page not found"):s("Failed to load page");return}const o=await t.json();o.success?m(o.data):s("Failed to load page")}catch(t){console.error("Error fetching CMS page:",t),s("Failed to load page")}finally{i(!1)}})()},[r]),g?e.jsx("div",{className:"CMSPage",children:e.jsx("div",{className:"CMSPage__container max-containermax-container",children:e.jsxs("div",{className:"CMSPage__loading",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading page..."})]})})}):c?e.jsx("div",{className:"CMSPage",children:e.jsx("div",{className:"CMSPage__container max-container",children:e.jsxs("div",{className:"CMSPage__error",children:[e.jsx("h1",{children:"Page Not Found"}),e.jsx("p",{children:c}),e.jsx("button",{className:"btn btn-primary",onClick:()=>d("/"),children:"Go Home"})]})})}):a?e.jsxs(e.Fragment,{children:[e.jsxs(h,{children:[e.jsxs("title",{children:[a.metaTitle||a.title," - XOSportsHub"]}),e.jsx("meta",{name:"description",content:a.metaDescription||`${a.title} - XOSportsHub`}),e.jsx("meta",{name:"keywords",content:a.metaKeywords||""}),e.jsx("meta",{property:"og:title",content:a.metaTitle||a.title}),e.jsx("meta",{property:"og:description",content:a.metaDescription||`${a.title} - XOSportsHub`}),a.featuredImage&&e.jsx("meta",{property:"og:image",content:a.featuredImage}),e.jsx("meta",{property:"og:type",content:"article"})]}),e.jsx("div",{className:"CMSPage",children:e.jsx("div",{className:"CMSPage__container max-container",children:e.jsxs("article",{className:"CMSPage__article",children:[a.featuredImage&&e.jsx("div",{className:"CMSPage__featured-image",children:e.jsx("img",{src:a.featuredImage,alt:a.title,onError:l=>{l.target.style.display="none"}})}),e.jsxs("header",{className:"CMSPage__header",children:[e.jsx("h1",{className:"CMSPage__title",children:a.title}),a.metaDescription&&e.jsx("p",{className:"CMSPage__description",children:a.metaDescription}),e.jsx("div",{className:"CMSPage__meta",children:e.jsx("time",{className:"CMSPage__date",children:new Date(a.updatedAt||a.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})})]}),e.jsx("div",{className:"CMSPage__content",children:e.jsx("div",{className:"CMSPage__html-content",dangerouslySetInnerHTML:{__html:a.content}})})]})})})]}):null};export{j as default};
