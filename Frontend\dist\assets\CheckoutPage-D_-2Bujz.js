import{b as le,r as c,d as Y,aH as ue,cs as he,ct as pe,j as e,aM as ye,aJ as fe,N as b,cu as xe,a as je,c as Ne,cv as ge,O as ne,P as ie,b5 as ve}from"./index-ctFdmWBt.js";import{s as be}from"./stripe-DT3_Ek51.js";import{L as Ce}from"./LoadingSkeleton-DFCyGuTF.js";import{f as ce}from"./dateValidation-cL5kH0gD.js";/* empty css                        */import"./timezoneUtils-BuH33ask.js";const we=({order:i,onSuccess:h,onError:S,onCancel:y})=>{const j=le(),N=c.useRef(null),g=c.useRef(null),u=c.useRef(null),{paymentIntent:a,isLoading:k}=Y(t=>t.payment),{cards:r}=Y(t=>t.cards),[D,_]=c.useState(!1),[$,z]=c.useState(!1),[G,f]=c.useState(null),[E,F]=c.useState(!1),[T,v]=c.useState(!1),[l,C]=c.useState("new"),[x,d]=c.useState(""),[p,w]=c.useState({name:"",email:""});c.useEffect(()=>{(async()=>{try{const s=await be;if(!s)throw new Error("Stripe failed to load");N.current=s,F(!0)}catch{console.error("Failed to load payment form. Please refresh the page.")}})()},[]),c.useEffect(()=>(E&&(async()=>{if((l==="new"||!r||r.length===0)&&N.current){if(v(!0),u.current){try{u.current.destroy()}catch{console.error("Card element already destroyed")}u.current=null}try{const o=N.current.elements().create("card",{style:{base:{color:"#424770",fontFamily:"inherit",fontSmoothing:"antialiased",fontSize:"16px","::placeholder":{color:"#aab7c4"}},invalid:{color:"#dc2626",iconColor:"#dc2626"},complete:{color:"#16a34a",iconColor:"#16a34a"}},hidePostalCode:!0});u.current=o,setTimeout(()=>{if(g.current&&u.current)try{o.mount(g.current),o.on("change",m=>{z(m.complete),f(m.error?m.error.message:null)}),o.on("ready",()=>{v(!1)}),o.on("focus",()=>{f(null)})}catch(m){console.error("Error mounting card element:",m),f("Failed to load payment form. Please refresh the page."),v(!1)}},200)}catch(s){console.error("Error creating card element:",s),f("Failed to initialize payment form. Please refresh the page."),v(!1)}}else v(!1)})(),()=>{if(u.current){try{u.current.destroy()}catch{console.log("Cleanup - card element already destroyed")}u.current=null}}),[l,r,E]),c.useEffect(()=>{j(ue())},[j]),c.useEffect(()=>{if(r&&r.length>0){const t=r.find(s=>s.isDefault);t&&(C("saved"),d(t._id))}else r&&r.length===0&&(C("new"),d(""))},[r]),c.useEffect(()=>{if(l==="saved"&&x&&r){const t=r.find(s=>s._id===x);t&&t.cardholderName&&w(s=>({...s,name:t.cardholderName}))}else l==="new"&&w(t=>({...t,name:""}))},[l,x,r]),c.useEffect(()=>{i&&i._id&&j(he())},[j,i==null?void 0:i._id]),c.useEffect(()=>{i&&i._id&&!a&&j(pe({orderId:i._id})).unwrap().then(()=>{}).catch(t=>{console.error("Error creating payment intent:",t)})},[j,i==null?void 0:i._id,a]);const L=t=>{w({...p,[t.target.name]:t.target.value})},R=t=>{w({...p,name:t.target.value})},A=()=>{if(l==="saved"&&x){const t=r.find(s=>s._id===x);return(t==null?void 0:t.cardholderName)||""}return p.name},O=async t=>{if(t.preventDefault(),!N.current||!a){f("Payment system not ready. Please try again.");return}if(l==="new"){if(!u.current){f("Payment system not ready. Please try again.");return}if(!$){f("Please complete your card information");return}}else if(l==="saved"&&!x){f("Please select a payment method");return}const s=A();if(!s.trim()){b.error("Please enter the cardholder name");return}_(!0),f(null);try{let o;if(l==="saved"){const m=r.find(B=>B._id===x);if(!m)throw new Error("Selected payment method not found");const{error:P,paymentIntent:U}=await N.current.confirmCardPayment(a.clientSecret,{payment_method:m.stripePaymentMethodId});if(P)throw new Error(P.message);o=U}else{const{error:m,paymentIntent:P}=await N.current.confirmCardPayment(a.clientSecret,{payment_method:{card:u.current,billing_details:{name:s,email:p.email}}});if(m)throw new Error(m.message);o=P}if(o.status==="succeeded"){const m=await j(xe({orderId:i._id,paymentIntentId:o.id})).unwrap();b.success("Payment successful!"),_(!1),h&&h(m)}}catch(o){f(o.message||"An error occurred while processing your payment"),_(!1),S&&S(o)}},M=()=>{y&&y()};return i?e.jsx("div",{className:"stripe-payment-container",children:e.jsxs("div",{className:"stripe-payment-form",children:[e.jsxs("form",{onSubmit:O,className:"payment-form",children:[e.jsxs("div",{className:"payment-header",children:[e.jsx("h3",{children:"Payment Method"}),e.jsx("p",{children:"Complete your purchase securely with Stripe"})]}),r&&r.length>0?e.jsxs("div",{className:"payment-method-selection",children:[e.jsxs("div",{className:"saved-cards-section",children:[e.jsxs("div",{className:"payment-option",children:[e.jsx("input",{type:"radio",id:"saved-card",name:"paymentMethod",value:"saved",checked:l==="saved",onChange:t=>C(t.target.value)}),e.jsx("label",{htmlFor:"saved-card",children:"Use saved card"})]}),l==="saved"&&e.jsx("div",{className:"saved-cards-list",children:r.map(t=>{var s;return e.jsxs("div",{className:"saved-card-item",children:[e.jsx("input",{type:"radio",id:`card-${t._id}`,name:"selectedCard",value:t._id,checked:x===t._id,onChange:o=>d(o.target.value)}),e.jsx("label",{htmlFor:`card-${t._id}`,className:"card-label",children:e.jsxs("div",{className:"card-info",children:[e.jsx(ye,{className:"card-icon"}),e.jsxs("div",{className:"card-details",children:[e.jsxs("div",{className:"cards-details-style",children:[e.jsxs("span",{className:"card-number",children:["**** **** **** ",t.lastFourDigits]}),e.jsxs("div",{className:"gap-10 flex",children:[e.jsx("span",{className:"card-type",children:(s=t.cardType)==null?void 0:s.toUpperCase()}),e.jsxs("span",{className:"card-expiry",children:[t.expiryMonth,"/",t.expiryYear]})]})]}),t.isDefault&&e.jsx("span",{className:"default-badge",children:"⭐"})]})]})})]},t._id)})})]}),e.jsxs("div",{className:"payment-option",children:[e.jsx("input",{type:"radio",id:"new-card",name:"paymentMethod",value:"new",checked:l==="new",onChange:t=>C(t.target.value)}),e.jsxs("label",{htmlFor:"new-card",children:[e.jsx(fe,{className:"plus-icon"}),"Add new card"]})]})]}):e.jsx(e.Fragment,{}),(l==="new"||!r||r.length===0)&&e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Card Details*"}),e.jsx("input",{type:"text",name:"name",value:p.name,onChange:R,placeholder:"Name on card",required:!0,className:"card-element-container"}),e.jsxs("div",{className:"card-element-container",children:[T&&e.jsxs("div",{className:"card-element-loading",children:[e.jsx("span",{className:"spinner"}),e.jsx("span",{children:"Loading payment form..."})]}),e.jsx("div",{ref:g,className:"stripe-card-element"})]})]}),e.jsx("div",{className:"billing-details",children:e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",children:"Email (optional)"}),e.jsx("input",{type:"email",id:"email",name:"email",value:p.email,onChange:L,placeholder:"Enter email for receipt",className:"form-input"})]})}),e.jsxs("div",{className:"order-summary-payment",children:[e.jsxs("div",{className:"summary-row",children:[e.jsx("span",{children:"Content Price:"}),e.jsxs("span",{children:["$",(i.amount||0).toFixed(2)]})]}),e.jsxs("div",{className:"summary-row total",children:[e.jsx("span",{children:"Total:"}),e.jsxs("span",{children:["$",(i.amount||0).toFixed(2)]})]}),i.platformFee>0&&e.jsx("div",{className:"fee-explanation",children:e.jsx("small",{children:"Platform fee is deducted from seller earnings"})})]}),e.jsxs("div",{className:"payment-actions",children:[e.jsx("button",{type:"button",onClick:M,className:"btn-secondary cancel-btn",disabled:D,children:"Cancel"}),e.jsx("button",{type:"submit",disabled:!E||D||k||l==="new"&&!$||l==="saved"&&!x,className:`btn-primary pay-btn ${D?"processing":""}`,children:D||k?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"spinner"}),"Processing..."]}):`Pay $${(i.amount||0).toFixed(2)}`})]})]}),e.jsx("div",{className:"security-notice",children:e.jsx("p",{children:"🔒 Your payment information is secure and encrypted"})})]})}):e.jsx("div",{className:"stripe-payment-form",children:e.jsx("div",{className:"payment-error",children:"No order information available. Please try again."})})},$e=()=>{var E,F,T,v,l,C,x;const{orderId:i}=je(),h=Ne(),S=le(),{user:y}=Y(d=>d.auth),{isLoading:j,error:N}=Y(d=>d.order),[g,u]=c.useState("loading"),[a,k]=c.useState(null),[r,D]=c.useState(!1),_=()=>{D(!r)};c.useEffect(()=>{if(!y){b.error("Please log in to complete your purchase"),h("/login");return}if((y.role==="admin"?y.role:y.activeRole||y.role)!=="buyer"&&y.role!=="admin"){b.error("Only buyers can make purchases"),h("/");return}if(!ge.isValidId(i)){console.error("Invalid order ID:",i),b.error("Invalid order ID. Please try creating a new order."),h("/buyer/dashboard");return}S(ne(i)).unwrap().then(p=>{k(p.data),u("payment")}).catch(p=>{console.error("Error fetching order:",p),b.error("Order not found or you do not have permission to view it"),h("/buyer/dashboard")})},[S,i,y,h]);const $=async d=>{var p,w,L,R,A,O,M,t,s,o,m,P,U,B,V,J,q,H,W,K,Q,X,Z,ee,ae,te,se;b.success("Payment completed successfully!"),u("success");try{const n=(await S(ne(a._id)).unwrap()).data,oe=I=>({visa:"Visa",mastercard:"Mastercard",amex:"American Express",discover:"Discover",diners:"Diners Club",jcb:"JCB",unionpay:"UnionPay",unknown:"Card"})[I==null?void 0:I.toLowerCase()]||"Card Payment",de=I=>I?`**** **** **** ${I}`:"**** **** **** ****",me={orderId:`#${((p=n._id)==null?void 0:p.slice(-8))||"12345678"}`,date:ce(n.createdAt||Date.now()),time:new Date(n.createdAt||Date.now()).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),items:1,totalAmount:`$${n.amount||"0.00"}`,customerDetails:{name:(w=n.buyer)!=null&&w.firstName&&((L=n.buyer)!=null&&L.lastName)?`${n.buyer.firstName} ${n.buyer.lastName}`:((R=n.buyer)==null?void 0:R.name)||"Customer",email:((A=n.buyer)==null?void 0:A.email)||"<EMAIL>",phone:((O=n.buyer)==null?void 0:O.mobile)||((M=n.buyer)==null?void 0:M.phone)||"Not provided"},paymentDetails:{method:oe((t=n.cardDetails)==null?void 0:t.cardType),cardNumber:de((s=n.cardDetails)==null?void 0:s.lastFourDigits),cardType:((o=n.cardDetails)==null?void 0:o.cardType)||"unknown"},itemInfo:{title:((m=n.content)==null?void 0:m.title)||"Digital Content",category:((P=n.content)==null?void 0:P.category)||((U=n.content)==null?void 0:U.sport)||"Sports Content",image:((B=n.content)==null?void 0:B.thumbnail)||((V=n.content)==null?void 0:V.thumbnailUrl)||"https://via.placeholder.com/80x80/f0f0f0/666666?text=Content"},fullOrder:n,paymentResult:d};setTimeout(()=>{h("/thank-you",{state:{orderData:me}})},2e3)}catch(re){console.error("Error fetching updated order:",re);const n={orderId:`#${((J=a._id)==null?void 0:J.slice(-8))||"12345678"}`,date:ce(a.createdAt||Date.now()),time:new Date(a.createdAt||Date.now()).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),items:1,totalAmount:`$${a.amount||"0.00"}`,customerDetails:{name:(q=a.buyer)!=null&&q.firstName&&((H=a.buyer)!=null&&H.lastName)?`${a.buyer.firstName} ${a.buyer.lastName}`:((W=a.buyer)==null?void 0:W.name)||"Customer",email:((K=a.buyer)==null?void 0:K.email)||"<EMAIL>",phone:((Q=a.buyer)==null?void 0:Q.mobile)||((X=a.buyer)==null?void 0:X.phone)||"Not provided"},paymentDetails:{method:"Card Payment",cardNumber:"**** **** **** ****",cardType:"unknown"},itemInfo:{title:((Z=a.content)==null?void 0:Z.title)||"Digital Content",category:((ee=a.content)==null?void 0:ee.category)||((ae=a.content)==null?void 0:ae.sport)||"Sports Content",image:((te=a.content)==null?void 0:te.thumbnail)||((se=a.content)==null?void 0:se.thumbnailUrl)||"https://via.placeholder.com/80x80/f0f0f0/666666?text=Content"},fullOrder:a,paymentResult:d};setTimeout(()=>{h("/thank-you",{state:{orderData:n}})},200)}},z=d=>{console.error("Payment error:",d),b.error(d.message||"Payment failed. Please try again."),u("error")},G=()=>{var d;h(`/buyer/details/${((d=a==null?void 0:a.content)==null?void 0:d._id)||(a==null?void 0:a.content)}`)},f=()=>{u("payment")};return j||g==="loading"?e.jsx(Ce,{type:"checkout"}):N||!a?e.jsx(ie,{title:"Order Not Found",message:N||"The order you're looking for doesn't exist or you don't have permission to view it.",onRetry:()=>h("/buyer/dashboard"),retryText:"Go to Dashboard"}):a.buyer._id!==y._id&&a.buyer!==y._id?e.jsx(ie,{title:"Access Denied",message:"You don't have permission to view this order.",onRetry:()=>h("/buyer/dashboard"),retryText:"Go to Dashboard"}):a.paymentStatus==="Completed"?e.jsx("div",{className:"checkout-page",children:e.jsx("div",{className:"max-container",children:e.jsx("div",{children:e.jsxs("div",{className:"order-already-paid",children:[e.jsx("h2",{children:"Order Already Paid"}),e.jsx("p",{children:"This order has already been completed."}),e.jsx("button",{className:"btn-primary",onClick:()=>h("/buyer/downloads"),children:"View Downloads"})]})})})}):e.jsx("div",{className:"checkout-page",children:e.jsx("div",{className:"max-container",children:e.jsxs("div",{className:"checkout-content",children:[e.jsx("div",{className:"checkout-left",children:e.jsxs("div",{className:"checkout-form-container",children:[e.jsx("h1",{className:"checkout-title",children:"Checkout"}),g==="loading"&&e.jsx("div",{className:"payment-loading",children:e.jsx("p",{children:"Loading order details..."})}),g==="payment"&&a?e.jsx(e.Fragment,{children:e.jsx(we,{order:a,onSuccess:$,onError:z,onCancel:G})}):e.jsx("div",{className:"debug-info"}),g==="success"&&e.jsxs("div",{className:"payment-success",children:[e.jsx("div",{className:"success-icon",children:"✅"}),e.jsx("h3",{children:"Payment Successful!"}),e.jsx("p",{children:"Your payment has been processed successfully. Redirecting..."})]}),g==="error"&&e.jsxs("div",{className:"payment-error",children:[e.jsx("div",{className:"error-icon",children:"❌"}),e.jsx("h3",{children:"Payment Failed"}),e.jsx("p",{children:"There was an issue processing your payment. Please try again."}),e.jsx("button",{className:"btn-primary retry-btn",onClick:f,children:"Retry Payment"})]})]})}),e.jsx("div",{className:"checkout-right",children:e.jsxs("div",{className:"order-summary",children:[e.jsx("h2",{className:"order-title",children:"Order Summary"}),e.jsx("div",{className:"rightbackgrounddiv",children:e.jsxs("div",{className:"item-info-section",children:[e.jsxs("div",{className:"item-info-header",onClick:_,children:[e.jsx("h3",{className:"item-info-title",children:"Item Info"}),e.jsx("button",{className:"dropdown-toggle","aria-label":"Toggle item info",children:e.jsx("svg",{className:`dropdown-arrow ${r?"expanded":""}`,width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:e.jsx("path",{d:"M4 6L8 10L12 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsx("div",{className:`item-content-container ${r?"expanded":"collapsed"}`,children:e.jsxs("div",{className:"item-details",children:[e.jsx("div",{className:"item-image",children:e.jsx("img",{src:(E=a.content)!=null&&E.thumbnailUrl?`${ve}${a.content.thumbnailUrl}`:"https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG",alt:((F=a.content)==null?void 0:F.title)||"Content",className:"item-thumbnail"})}),e.jsxs("div",{className:"item-description",children:[e.jsx("h4",{className:"item-name",children:((T=a.content)==null?void 0:T.title)||"Content Title"}),e.jsxs("p",{className:"item-coach",children:["By ",((v=a.content)==null?void 0:v.coachName)||"Coach"]}),e.jsx("p",{className:"item-type",children:((l=a.content)==null?void 0:l.contentType)||"Digital Content"})]})]})}),e.jsx("div",{className:"order-info-section",children:e.jsxs("div",{className:"order-details",children:[e.jsxs("div",{className:"order-row",children:[e.jsx("span",{children:"Order ID:"}),e.jsxs("span",{children:["#",(C=a._id)==null?void 0:C.slice(-8).toUpperCase()]})]}),e.jsxs("div",{className:"order-row",children:[e.jsx("span",{children:"Order Type:"}),e.jsx("span",{children:a.orderType})]}),e.jsxs("div",{className:"order-row",children:[e.jsx("span",{children:"Status:"}),e.jsx("span",{className:`status ${(x=a.status)==null?void 0:x.toLowerCase()}`,children:a.status})]}),e.jsxs("div",{className:"price-row",children:[e.jsx("span",{className:"price-label",children:"Content Price"}),e.jsxs("span",{className:"price-value",children:["$",(a.amount||0).toFixed(2)]})]}),e.jsxs("div",{className:"price-row total-row",children:[e.jsx("span",{className:"price-label",children:"Total"}),e.jsxs("span",{className:"price-value",children:["$",(a.amount||0).toFixed(2)]})]}),a.platformFee>0&&e.jsx("div",{className:"fee-explanation",children:e.jsx("small",{children:"You pay the listed price. Platform fee is deducted from seller earnings."})})]})})]})})]})})]})})})};export{$e as default};
