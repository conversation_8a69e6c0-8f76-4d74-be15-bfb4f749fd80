import{j as a,aw as x,aB as h}from"./index-ctFdmWBt.js";const u=({isOpen:s,onClose:o,onConfirm:e,title:r="Confirm Action",message:m="Are you sure you want to proceed?",confirmText:t="Confirm",cancelText:d="Cancel",type:i="warning",isLoading:n=!1})=>{const f=l=>{l.target===l.currentTarget&&!n&&o()},_=()=>{n||e()},c=()=>{n||o()};return s?a.jsx("div",{className:"confirmation-modal-overlay",onClick:f,children:a.jsxs("div",{className:"confirmation-modal",children:[a.jsxs("div",{className:"confirmation-modal__header",children:[a.jsx("div",{className:`confirmation-modal__icon confirmation-modal__icon--${i}`,children:a.jsx(x,{})}),a.jsx("button",{className:"confirmation-modal__close",onClick:c,disabled:n,children:a.jsx(h,{})})]}),a.jsxs("div",{className:"confirmation-modal__content",children:[a.jsx("h3",{className:"confirmation-modal__title",children:r}),a.jsx("p",{className:"confirmation-modal__message",children:m})]}),a.jsxs("div",{className:"confirmation-modal__actions",children:[a.jsx("button",{className:"confirmation-modal__cancel",onClick:c,disabled:n,children:d}),a.jsx("button",{className:`confirmation-modal__confirm confirmation-modal__confirm--${i}`,onClick:_,disabled:n,children:n?"Processing...":t})]})]})}):null};export{u as C};
