import{r as o,j as e,aw as j}from"./index-ctFdmWBt.js";import{S as v}from"./SimplePDFViewer-RiyMKPYj.js";const f=({fileUrl:n,fileName:s="",title:m="Document",className:a="",height:c="400px",showDownload:d=!1,onDownload:t=null,showFallbackOptions:u=!0})=>{const[x,i]=o.useState(!0),[p,l]=o.useState(!1);o.useEffect(()=>{if(!n){l(!0),i(!1);return}i(!1),l(!1)},[s,n]);const h=()=>{if(!n)return;const r=document.createElement("a");r.href=n,r.target="_blank",r.rel="noopener noreferrer",s&&(r.download=s),document.body.appendChild(r),r.click(),document.body.removeChild(r)};return e.jsx("div",{className:`document-viewer ${a}`,style:{height:c},children:x?e.jsxs("div",{className:"document-viewer__loading",children:[e.jsx("div",{className:"document-viewer__spinner"}),e.jsx("p",{children:"Loading PDF document..."})]}):p?e.jsxs("div",{className:"document-viewer__error",children:[e.jsx(j,{className:"document-viewer__error-icon"}),e.jsx("h3",{children:"Error Loading PDF Document"}),e.jsx("p",{children:"Unable to load the PDF preview. Please check the file URL."}),(d||t)&&e.jsx("button",{className:"document-viewer__download-btn",onClick:t||h,children:"Download"})]}):e.jsx(v,{fileUrl:n,title:m,className:a,height:c,showDownload:d,onDownload:t,showFallbackOptions:u})})};export{f as D};
