import{j as a}from"./index-ctFdmWBt.js";/* empty css                       */const r=({icon:n,title:e,className:c="",onBack:s,backLabel:m="Back",backIcon:i})=>a.jsx("div",{className:`DynamicHeading ${c}`,children:a.jsxs("div",{className:"DynamicHeading__border",children:[a.jsxs("h2",{className:"DynamicHeading__title",children:[s&&a.jsxs("button",{className:"DynamicHeading__back-btn",onClick:s,type:"button",children:[i&&a.jsx("span",{className:"DynamicHeading__back-icon",children:i}),a.jsx("span",{className:"DynamicHeading__back-label",children:m})]}),n&&a.jsx("span",{className:"DynamicHeading__icon",children:n})]}),a.jsx("span",{className:"DynamicHeading__text",children:e})]})});export{r as D};
