import{a as _e,b as Se,c as xe,d as be,r as o,g as je,s as _,j as e,F as W,M as ve,e as Ae,f as X,h as Ne,i as De,k as M,l as Y}from"./index-ctFdmWBt.js";import{S as $}from"./SellerLayout-EbrVdrvL.js";import{S as O}from"./SummernoteEditor-CakKAUcC.js";import{i as Z,g as we,T as Q,a as ee,U as Ce,v as te,F as ae}from"./TimezoneErrorBoundary-BPlQ-kmB.js";import{D as Pe}from"./DocumentViewer-CrWHT6xN.js";import{P as Te}from"./PreviewModal-bMzyFP6E.js";import{f as P,t as v}from"./timezoneUtils-BuH33ask.js";import"./SimplePDFViewer-RiyMKPYj.js";const ie={maxSize:5*1024*1024,allowedTypes:["image/jpeg","image/jpg","image/png","image/gif"],allowedExtensions:[".jpg",".jpeg",".png",".gif"]},se=u=>u>=1024*1024*1024?`${(u/(1024*1024*1024)).toFixed(1)}GB`:u>=1024*1024?`${(u/(1024*1024)).toFixed(0)}MB`:u>=1024?`${(u/1024).toFixed(0)}KB`:`${u}B`,$e=()=>{var K,H;const{id:u}=_e(),b=Se(),T=xe(),{singleContent:l,isLoading:A}=be(s=>s.content),[j,U]=o.useState(""),[t,m]=o.useState({title:"",category:"",coachName:"",description:"",fileUrl:"",aboutCoach:"",strategicContent:"",sport:"Other",contentType:"Video",previewUrl:"",thumbnailUrl:"",duration:"",videoLength:"",fileSize:"",tags:[],difficulty:"Intermediate",language:"English",prerequisites:[],learningObjectives:[],equipment:[],saleType:"Fixed",price:0,allowCustomRequests:!1,customRequestPrice:"",status:"Draft",visibility:"Public",auctionDetails:{basePrice:"",auctionStartDate:"",auctionEndDate:"",minimumBidIncrement:"",allowOfferBeforeAuctionStart:!1}}),[d,E]=o.useState(null),[B,F]=o.useState(!1),[le,N]=o.useState(!1),[re,D]=o.useState(""),[ne,w]=o.useState(""),[V,S]=o.useState(0),[z,R]=o.useState(""),[ce,L]=o.useState(!0),[r,y]=o.useState({}),[oe,de]=o.useState(!1),[ue,G]=o.useState(!1),[g,me]=o.useState(!1),[C,J]=o.useState(!1),[ge,I]=o.useState(null);o.useEffect(()=>{u&&!C&&(J(!0),I(null),b(je(u)).unwrap().then(()=>{}).catch(s=>{console.error("Failed to fetch strategy data:",s),I(s.message||"Failed to load strategy data"),_("Failed to load strategy data. Please try again.")}))},[b,u,C]),o.useEffect(()=>{var s,a,i,n,c;l&&m({title:l.title||"",category:l.category||"",coachName:l.coachName||"",description:l.description||"",fileUrl:l.fileUrl||"",aboutCoach:l.aboutCoach||"",strategicContent:l.strategicContent||"",sport:l.sport||"Basketball",contentType:l.contentType||"",thumbnailUrl:l.thumbnailUrl||"",tags:l.tags||[],difficulty:l.difficulty||"",saleType:l.saleType||"",price:l.price||"",allowCustomRequests:l.allowCustomRequests||!1,auctionDetails:{basePrice:((s=l.auctionDetails)==null?void 0:s.basePrice)||"",auctionStartDate:(a=l.auctionDetails)!=null&&a.auctionStartDate?P(new Date(l.auctionDetails.auctionStartDate)):"",auctionEndDate:(i=l.auctionDetails)!=null&&i.auctionEndDate?P(new Date(l.auctionDetails.auctionEndDate)):"",minimumBidIncrement:((n=l.auctionDetails)==null?void 0:n.minimumBidIncrement)||"",allowOfferBeforeAuctionStart:((c=l.auctionDetails)==null?void 0:c.allowOfferBeforeAuctionStart)||!1},previewUrl:l.previewUrl||"",duration:l.duration||"",videoLength:l.videoLength||"",fileSize:l.fileSize||"",prerequisites:l.prerequisites||[],learningObjectives:l.learningObjectives||[],equipment:l.equipment||[],customRequestPrice:l.customRequestPrice||"",status:l.status||"Published",visibility:l.visibility||"Public"})},[l]);const h=s=>{const{name:a,value:i,type:n,checked:c}=s.target;if(m(p=>({...p,[a]:n==="checkbox"?c:i})),a==="contentType"&&d&&!te(d,i).isValid){E(null),m(fe=>({...fe,fileUrl:"",fileSize:""})),_(`Current file is not valid for ${i} content type. Please upload a new file.`);const x=document.querySelector('input[type="file"]');x&&(x.value="")}q(a,n==="checkbox"?c:i)},k=(s,a)=>{m(i=>({...i,[s]:a})),q(s,a)},f=s=>{const{name:a,value:i}=s.target;q(a,i)},q=(s,a)=>{const i={...r};switch(s){case"title":a.trim()?delete i.title:i.title="Strategy title is required";break;case"category":a?delete i.category:i.category="Please select a category";break;case"coachName":a.trim()?delete i.coachName:i.coachName="Coach/Seller/Academy name is required";break;case"description":{a.replace(/<[^>]*>/g,"").trim()?delete i.description:i.description="Strategy description is required";break}case"aboutCoach":{a.replace(/<[^>]*>/g,"").trim()?delete i.aboutCoach:i.aboutCoach="About the coach information is required";break}case"strategicContent":{a.replace(/<[^>]*>/g,"").trim()?delete i.strategicContent:i.strategicContent="Strategic content description is required";break}case"contentType":a?delete i.contentType:i.contentType="Please select a content type";break;case"difficulty":a?delete i.difficulty:i.difficulty="Please select a difficulty level";break;case"saleType":a?delete i.saleType:i.saleType="Please select a sale type";break;case"price":!a||a<=0?i.price="Please enter a valid price greater than $0":delete i.price;break}y(i)},pe=async s=>{const a=s.target.files[0];if(a){if(!t.contentType){_("Please select a content type before uploading a file"),s.target.value="";return}const i=te(a,t.contentType);if(!i.isValid){_(i.message),s.target.value="";return}E(a),N(!0),D("content file"),w(a.name),S(0);const n=new FormData;n.append("file",a),n.append("type","content");try{const c=await b(Y({formData:n,onProgress:x=>S(x)})).unwrap();m(x=>({...x,fileUrl:c.data.fileUrl,fileSize:c.data.fileSize||a.size}));const p={...r};delete p.fileUpload,y(p),M("Content file uploaded successfully!")}catch(c){console.error("File upload failed:",c),E(null),_("Failed to upload content file. Please try again.")}finally{N(!1),D(""),w(""),S(0)}}},he=async s=>{const a=s.target.files[0];if(a){R(""),L(!1);try{if(!ie.allowedTypes.includes(a.type))throw new Error("Only JPG, JPEG, PNG, and GIF formats are supported for thumbnails");if(a.size>ie.maxSize)throw new Error("Thumbnail file size must be less than 5MB");N(!0),D("thumbnail"),w(a.name),S(0);const i=new FormData;i.append("file",a),i.append("type","thumbnail");const n=await b(Y({formData:i,onProgress:p=>S(p)})).unwrap();if(!n.data||!n.data.fileUrl)throw new Error("Invalid response from server");const c=n.data.fileUrl;m(p=>({...p,thumbnailUrl:c})),L(!0),M("Thumbnail uploaded successfully!")}catch(i){console.error("Thumbnail upload failed:",i),R(i.message||"Failed to upload thumbnail. Please try again."),m(n=>({...n,thumbnailUrl:""}))}finally{N(!1),D(""),w(""),S(0)}}},ye=async s=>{s.preventDefault(),me(!0),F(!0);const a={};if(t.title.trim()||(a.title="Strategy title is required"),t.category||(a.category="Please select a category"),t.coachName.trim()||(a.coachName="Coach/Seller/Academy name is required"),t.description.replace(/<[^>]*>/g,"").trim()||(a.description="Strategy description is required"),t.contentType||(a.contentType="Please select a content type"),!t.fileUrl&&!d&&(a.fileUpload="Please upload a video or document file"),t.aboutCoach.replace(/<[^>]*>/g,"").trim()||(a.aboutCoach="About the coach information is required"),t.strategicContent.replace(/<[^>]*>/g,"").trim()||(a.strategicContent="Strategic content description is required"),t.thumbnailUrl||(a.thumbnailUpload="Please upload a thumbnail image"),t.difficulty||(a.difficulty="Please select a difficulty level"),t.saleType||(a.saleType="Please select a sale type"),t.saleType==="Fixed"&&(!t.price||t.price<=0)&&(a.price="Please enter a valid price greater than $0"),t.saleType==="Auction"&&((!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0)&&(a.auctionBasePrice="Please enter a valid starting bid price greater than $0"),(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0)&&(a.auctionMinIncrement="Please enter a valid minimum bid increment greater than $0"),t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate)<=new Date&&(a.auctionStartDate="Auction start date must be in the future"):a.auctionStartDate="Please select an auction start date",t.auctionDetails.auctionEndDate||(a.auctionEndDate="Please select an auction end date"),t.auctionDetails.auctionStartDate&&t.auctionDetails.auctionEndDate)){const i=new Date(t.auctionDetails.auctionStartDate);new Date(t.auctionDetails.auctionEndDate)<=i&&(a.auctionDateRange="Auction end date must be after start date")}if(Object.keys(a).length>0){y(a),de(!0),F(!1),setTimeout(()=>{const i=document.querySelector(".AddStrategy__validation-error");i&&i.scrollIntoView({behavior:"smooth",block:"center"})},100);return}try{const i={...t,sport:t.category||"Other",coachName:t.coachName||"Coach",thumbnailUrl:t.thumbnailUrl};i.saleType==="Auction"&&(i.auctionDetails.auctionStartDate&&(i.auctionDetails.auctionStartDate=v(new Date(i.auctionDetails.auctionStartDate))),i.auctionDetails.auctionEndDate&&(i.auctionDetails.auctionEndDate=v(new Date(i.auctionDetails.auctionEndDate)))),await b(De({id:u,contentData:i})).unwrap(),M("🎉 Strategy updated successfully!"),T("/seller/my-sports-strategies")}catch(i){console.error("Content update failed:",i);let n="Failed to update strategy. Please try again.";i.message?n=i.message:i.errors&&i.errors.length>0?n=i.errors[0].msg||n:typeof i=="string"&&(n=i),_(`❌ ${n}`)}finally{F(!1)}};return A&&!l||!l&&!C?e.jsx($,{children:e.jsx("div",{className:"AddStrategy",children:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading strategy details..."})]})})}):!l&&C&&!A?e.jsx($,{children:e.jsx("div",{className:"AddStrategy",children:e.jsxs("div",{className:"error-container",children:[e.jsx("h3",{children:"Strategy not found"}),e.jsx("p",{children:ge||"The strategy you're trying to edit doesn't exist or has been removed."}),e.jsxs("div",{className:"error-actions",children:[e.jsx("button",{className:"btn btn-primary",onClick:()=>{J(!1),I(null)},children:"Try Again"}),e.jsx("button",{className:"btn btn-outline",onClick:()=>T("/seller/my-sports-strategies"),children:"Back to Strategies"})]})]})})}):e.jsx($,{children:e.jsxs("div",{className:"AddStrategy",children:[e.jsx("div",{className:"AddStrategy__header",children:e.jsx("p",{className:"AddStrategy__subtitle",children:"Update your strategy details"})}),e.jsxs("form",{className:"AddStrategy__form",onSubmit:ye,children:[e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Strategy Title"}),e.jsx("input",{type:"text",name:"title",className:"AddStrategy__input",placeholder:"Add title for Strategy",value:t.title,onChange:h,onBlur:f}),(r.title||g&&!t.title.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.title||"Strategy title is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Category"}),e.jsxs("select",{name:"category",className:"AddStrategy__select",value:t.category,onChange:h,onBlur:f,children:[e.jsx("option",{value:"",children:"Select Category"}),e.jsx("option",{value:"Basketball",children:"Basketball"}),e.jsx("option",{value:"Football",children:"Football"}),e.jsx("option",{value:"Soccer",children:"Soccer"}),e.jsx("option",{value:"Baseball",children:"Baseball"})]}),(r.category||g&&!t.category)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.category||"Please select a category"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Coach/Seller/Academy Name"}),e.jsx("input",{type:"text",name:"coachName",className:"AddStrategy__input",placeholder:"Enter coach, seller, or academy name",value:t.coachName,onChange:h,onBlur:f}),(r.coachName||oe&&!t.coachName.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.coachName||"Coach/Seller/Academy name is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Description for Strategy"}),e.jsx(O,{value:t.description,onChange:s=>k("description",s),placeholder:"Enter a detailed description of your strategy...",height:200,className:"AddStrategy__summernote",contentKey:`desc-${u}`}),(r.description||g&&!t.description.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.description||"Strategy description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Content Type"}),e.jsxs("select",{name:"contentType",className:"AddStrategy__select",value:t.contentType,onChange:h,onBlur:f,children:[e.jsx("option",{value:"",children:"Select Content Type"}),e.jsx("option",{value:"Video",children:"Video"}),e.jsx("option",{value:"Document",children:"Document"})]}),(r.contentType||g&&!t.contentType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.contentType||"Please select a content type"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsxs("label",{className:"AddStrategy__label",children:["Upload ",t.contentType||"Video/Document"]}),t.contentType&&e.jsx("p",{className:"AddStrategy__format-note",children:t.contentType==="Video"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:se(ae.Video)})," • Supported formats: ",e.jsx("span",{children:"MP4, MOV, AVI, WEBM"})]}):t.contentType==="Document"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:se(ae.Document)})," • Supported formats: ",e.jsx("span",{children:"PDF"})]}):"Please select a content type to see upload requirements"}),e.jsxs("label",{htmlFor:"file-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"file-upload",className:"AddStrategy__file-input",onChange:pe,accept:we(t.contentType),disabled:Z(t.contentType),style:{display:"none"}}),e.jsxs("div",{className:`AddStrategy__upload-content ${Z(t.contentType)?"AddStrategy__upload-content--disabled":""}`,children:[e.jsx(W,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:d?d.name:t.fileUrl?"Current file uploaded":"Click to upload file"})]}),(d||t.fileUrl)&&e.jsxs("div",{className:"AddStrategy__file-info",children:[e.jsx("p",{className:"AddStrategy__file-name",children:d?d.name:"Current file uploaded"}),d&&e.jsxs("p",{className:"AddStrategy__file-size",children:[(d.size/1024/1024).toFixed(2)," MB"]})]})]}),(r.fileUpload||g&&!t.fileUrl&&!d)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.fileUpload||"Please upload a video or document file"})}),t.fileUrl&&e.jsxs("div",{className:"AddStrategy__file-preview",children:[e.jsxs("div",{className:"AddStrategy__preview-header",children:[e.jsx("h4",{className:"AddStrategy__preview-title",children:"Current File Preview"}),e.jsxs("button",{className:"btn-outline AddStrategy__previewBtn",onClick:()=>G(!0),title:"Preview Document/Video in Full Screen",type:"button",children:[e.jsx(ve,{}),"Preview"]})]}),t.contentType==="Video"&&e.jsx("div",{className:"AddStrategy__video-preview",children:e.jsxs("video",{className:"AddStrategy__video-element",controls:!0,controlsList:"nodownload nofullscreen noremoteplayback",disablePictureInPicture:!0,style:{width:"100%",maxHeight:"300px"},onError:s=>{console.error("Video preview error:",s)},children:[e.jsx("source",{src:t.fileUrl,type:"video/mp4"}),"Your browser does not support the video tag."]})}),t.contentType==="Document"&&e.jsx("div",{className:"AddStrategy__document-preview",children:e.jsx(Pe,{fileUrl:t.fileUrl,fileName:(d==null?void 0:d.name)||((K=t.fileUrl)==null?void 0:K.split("/").pop())||"document",title:"Document Preview",className:"AddStrategy__document-element",height:"300px",showDownload:!1})})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"About The Coach"}),e.jsx(O,{value:t.aboutCoach,onChange:s=>k("aboutCoach",s),placeholder:"Share your background, experience, and expertise...",height:200,className:"AddStrategy__summernote",contentKey:`coach-${u}`}),(r.aboutCoach||g&&!t.aboutCoach.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.aboutCoach||"About the coach information is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Includes Strategic Content"}),e.jsx(O,{value:t.strategicContent,onChange:s=>k("strategicContent",s),placeholder:"Describe what strategic content is included...",height:200,className:"AddStrategy__summernote",contentKey:`strategic-${u}`}),(r.strategicContent||g&&!t.strategicContent.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.strategicContent||"Strategic content description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Thumbnail Image"}),e.jsxs("p",{className:"AddStrategy__format-note",children:["Maximum size: ",e.jsx("span",{children:"5MB"})," • Supported formats: ",e.jsx("span",{children:"JPG, JPEG, PNG, GIF"})]}),e.jsxs("label",{htmlFor:"thumbnail-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"thumbnail-upload",className:"AddStrategy__file-input",accept:"image/jpeg,image/jpg,image/png,image/gif",onChange:he,style:{display:"none"}}),e.jsxs("div",{className:"AddStrategy__upload-content",children:[e.jsx(W,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:t.thumbnailUrl?"Thumbnail uploaded":"Click to upload thumbnail"})]}),z&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:z})}),(r.thumbnailUpload||g&&!t.thumbnailUrl)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.thumbnailUpload||"Please upload a thumbnail image"})}),t.thumbnailUrl&&ce&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:Ae(t.thumbnailUrl),alt:"Thumbnail preview",onError:s=>{s.target.src=X(),_("Failed to load thumbnail preview")},style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)"}})}),!t.thumbnailUrl&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:X(200,120,"No thumbnail"),alt:"No thumbnail",style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)",opacity:.7}})})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Difficulty Level"}),e.jsxs("select",{name:"difficulty",className:"AddStrategy__select",value:t.difficulty,onChange:h,onBlur:f,children:[e.jsx("option",{value:"",children:"Select Difficulty"}),e.jsx("option",{value:"Beginner",children:"Beginner"}),e.jsx("option",{value:"Intermediate",children:"Intermediate"}),e.jsx("option",{value:"Advanced",children:"Advanced"}),e.jsx("option",{value:"Professional",children:"Professional"})]}),(r.difficulty||g&&!t.difficulty)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.difficulty||"Please select a difficulty level"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Language"}),e.jsxs("select",{name:"language",className:"AddStrategy__select",value:t.language,onChange:h,children:[e.jsx("option",{value:"",children:"Select Language"}),e.jsx("option",{value:"English",children:"English"}),e.jsx("option",{value:"Spanish",children:"Spanish"}),e.jsx("option",{value:"French",children:"French"}),e.jsx("option",{value:"German",children:"German"}),e.jsx("option",{value:"Chinese",children:"Chinese"}),e.jsx("option",{value:"Japanese",children:"Japanese"}),e.jsx("option",{value:"Other",children:"Other"})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Tags"}),e.jsxs("div",{className:"AddStrategy__array-field",children:[e.jsxs("div",{className:"AddStrategy__array-input",children:[e.jsx("input",{type:"text",className:"AddStrategy__input",placeholder:"Add a tag (e.g., basketball, technique, training)...",value:j,onChange:s=>U(s.target.value),onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),j.trim()&&(m(a=>({...a,tags:[...a.tags,j.trim()]})),U("")))}}),e.jsx("button",{type:"button",className:"btn-primary",onClick:()=>{j.trim()&&(m(s=>({...s,tags:[...s.tags,j.trim()]})),U(""))},children:"Add"})]}),t.tags.length>0&&e.jsx("div",{className:"AddStrategy__array-items",children:t.tags.map((s,a)=>e.jsxs("div",{className:"AddStrategy__array-item",children:[s,e.jsx("button",{type:"button",className:"AddStrategy__remove-btn",onClick:()=>{m(i=>({...i,tags:i.tags.filter((n,c)=>c!==a)}))},children:"X"})]},a))})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Sale Type"}),e.jsxs("select",{name:"saleType",className:"AddStrategy__select",value:t.saleType,onChange:h,onBlur:f,children:[e.jsx("option",{value:"",children:"Select Sale Type"}),e.jsx("option",{value:"Fixed",children:"Fixed Price"}),e.jsx("option",{value:"Auction",children:"Auction"})]}),(r.saleType||g&&!t.saleType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.saleType||"Please select a sale type"})})]}),t.saleType==="Fixed"&&e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Price ($)"}),e.jsx("input",{type:"number",name:"price",className:"AddStrategy__input",placeholder:"Enter price",value:t.price,onChange:h,onBlur:f,min:"0",step:"0.01"}),(r.price||g&&(!t.price||t.price<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.price||"Please enter a valid price greater than $0"})})]}),t.allowCustomRequests&&e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Custom Request Price ($)"}),e.jsx("input",{type:"number",name:"customRequestPrice",className:"AddStrategy__input",placeholder:"Enter custom request price",value:t.customRequestPrice,onChange:h,min:"0",step:"0.01"})]}),t.saleType==="Auction"&&e.jsxs("div",{className:"AddStrategy__auction-section",children:[e.jsx("h3",{className:"AddStrategy__section-title",children:"Auction Settings"}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Starting Bid Price ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.basePrice",className:"AddStrategy__input",placeholder:"Enter starting bid price",value:t.auctionDetails.basePrice,onChange:s=>m(a=>({...a,auctionDetails:{...a.auctionDetails,basePrice:s.target.value}})),min:"0",step:"0.01"}),(r.auctionBasePrice||g&&(!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionBasePrice||"Please enter a valid starting bid price greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Minimum Bid Increment ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.minimumBidIncrement",className:"AddStrategy__input",placeholder:"Enter minimum bid increment",value:t.auctionDetails.minimumBidIncrement,onChange:s=>m(a=>({...a,auctionDetails:{...a.auctionDetails,minimumBidIncrement:s.target.value}})),min:"0.01",step:"0.01"}),(r.auctionMinIncrement||g&&(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionMinIncrement||"Please enter a valid minimum bid increment greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction Start Date"}),e.jsxs(Q,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionStartDate",className:"AddStrategy__input",value:t.auctionDetails.auctionStartDate,min:P(new Date),onChange:s=>{const a=s.target.value;m(i=>({...i,auctionDetails:{...i.auctionDetails,auctionStartDate:a}})),a&&(v(new Date(a))<=new Date?y(c=>({...c,auctionStartDate:"Auction start date must be in the future"})):y(c=>{const p={...c};return delete p.auctionStartDate,p}))}}),e.jsx(ee,{})]}),(r.auctionStartDate||g&&!t.auctionDetails.auctionStartDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionStartDate||"Please select an auction start date"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction End Date"}),e.jsxs(Q,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionEndDate",className:"AddStrategy__input",value:t.auctionDetails.auctionEndDate,min:P(t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate):new Date),onChange:s=>{const a=s.target.value;if(m(i=>({...i,auctionDetails:{...i.auctionDetails,auctionEndDate:a}})),a&&t.auctionDetails.auctionStartDate){const i=v(new Date(t.auctionDetails.auctionStartDate));v(new Date(a))<=i?y(c=>({...c,auctionDateRange:"Auction end date must be after start date"})):y(c=>{const p={...c};return delete p.auctionDateRange,p})}}}),e.jsx(ee,{})]}),(r.auctionEndDate||r.auctionDateRange||g&&!t.auctionDetails.auctionEndDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionEndDate||r.auctionDateRange||"Please select an auction end date"})})]}),e.jsx("div",{className:"AddStrategy__field",children:e.jsxs("label",{className:"AddStrategy__checkbox-label",children:[e.jsx("input",{type:"checkbox",name:"auctionDetails.allowOfferBeforeAuctionStart",checked:t.auctionDetails.allowOfferBeforeAuctionStart,onChange:s=>m(a=>({...a,auctionDetails:{...a.auctionDetails,allowOfferBeforeAuctionStart:s.target.checked}})),className:"AddStrategy__checkbox"}),"Allow offers before auction starts"]})}),e.jsx("div",{className:"AddStrategy__field",children:e.jsx("div",{className:"AddStrategy__auction-note",children:e.jsxs("p",{children:[e.jsx("strong",{children:"Note:"})," Once the auction starts, the strategy content cannot be edited."]})})})]}),e.jsxs("div",{className:"AddStrategy__actions",children:[e.jsx("button",{type:"submit",className:"btn-primary",disabled:B||A,children:B?"Updating...":"Update Strategy"}),e.jsx("button",{type:"button",className:" btn-outline",onClick:()=>T(`/seller/strategy-details/${u}`),disabled:B||A,children:"Cancel"})]})]}),e.jsx(Ce,{progress:V,isVisible:le,fileName:ne,uploadType:re,uploadProgress:V}),e.jsx(Te,{isOpen:ue,onClose:()=>G(!1),fileUrl:Ne(t.fileUrl),fileName:(d==null?void 0:d.name)||((H=t.fileUrl)==null?void 0:H.split("/").pop())||t.title||"document",title:t.title||"Strategy Preview",contentType:t.contentType})]})})};export{$e as default};
