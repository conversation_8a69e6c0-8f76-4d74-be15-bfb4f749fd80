import{r as T,j as m,c as ti,b as Or,eI as ei,c3 as Nr,G as xt,L as Kt,ea as Wr,eJ as Ur,eK as Hr}from"./index-ctFdmWBt.js";import{i as Kr}from"./herosideimg-B5KTUGzn.js";import{o as zr}from"./ourmissionimage-imuutOi1.js";/* empty css                   */import{S as $r}from"./StrategyCard-D3kMF0Ka.js";import{a as Gr}from"./index-D-wstcb_.js";const ni=T.createContext({});function re(t){const e=T.useRef(null);return e.current===null&&(e.current=t()),e.current}const Ge=typeof window<"u",Ye=Ge?T.useLayoutEffect:T.useEffect,_e=T.createContext(null);function Xe(t,e){t.indexOf(e)===-1&&t.push(e)}function qe(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const Z=(t,e,n)=>n>e?e:n<t?t:n;let Lt=()=>{};const Q={},si=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function ii(t){return typeof t=="object"&&t!==null}const ri=t=>/^0[^.\s]+$/u.test(t);function Ze(t){let e;return()=>(e===void 0&&(e=t()),e)}const W=t=>t,Yr=(t,e)=>n=>e(t(n)),Nt=(...t)=>t.reduce(Yr),yt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class Je{constructor(){this.subscriptions=[]}add(e){return Xe(this.subscriptions,e),()=>qe(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const _=t=>t*1e3,X=t=>t/1e3;function Qe(t,e){return e?t*(1e3/e):0}const oi=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,_r=1e-7,Xr=12;function qr(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=oi(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>_r&&++a<Xr);return r}function Wt(t,e,n,s){if(t===e&&n===s)return W;const i=o=>qr(o,0,1,t,n);return o=>o===0||o===1?o:oi(i(o),e,s)}const ai=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,li=t=>e=>1-t(1-e),ci=Wt(.33,1.53,.69,.99),tn=li(ci),ui=ai(tn),hi=t=>(t*=2)<1?.5*tn(t):.5*(2-Math.pow(2,-10*(t-1))),en=t=>1-Math.sin(Math.acos(t)),di=li(en),fi=ai(en),Zr=Wt(.42,0,1,1),Jr=Wt(0,0,.58,1),mi=Wt(.42,0,.58,1),Qr=t=>Array.isArray(t)&&typeof t[0]!="number",pi=t=>Array.isArray(t)&&typeof t[0]=="number",to={linear:W,easeIn:Zr,easeInOut:mi,easeOut:Jr,circIn:en,circInOut:fi,circOut:di,backIn:tn,backInOut:ui,backOut:ci,anticipate:hi},eo=t=>typeof t=="string",Ln=t=>{if(pi(t)){Lt(t.length===4);const[e,n,s,i]=t;return Wt(e,n,s,i)}else if(eo(t))return to[t];return t},zt=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function no(t,e){let n=new Set,s=new Set,i=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function l(c){r.has(c)&&(u.schedule(c),t()),c(a)}const u={schedule:(c,h=!1,d=!1)=>{const p=d&&i?n:s;return h&&r.add(c),p.has(c)||p.add(c),c},cancel:c=>{s.delete(c),r.delete(c)},process:c=>{if(a=c,i){o=!0;return}i=!0,[n,s]=[s,n],n.forEach(l),n.clear(),i=!1,o&&(o=!1,u.process(c))}};return u}const so=40;function gi(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=zt.reduce((v,C)=>(v[C]=no(o),v),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:d,render:f,postRender:p}=r,y=()=>{const v=Q.useManualTiming?i.timestamp:performance.now();n=!1,Q.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(v-i.timestamp,so),1)),i.timestamp=v,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),f.process(i),p.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(y))},x=()=>{n=!0,s=!0,i.isProcessing||t(y)};return{schedule:zt.reduce((v,C)=>{const P=r[C];return v[C]=(M,j=!1,A=!1)=>(n||x(),P.schedule(M,j,A)),v},{}),cancel:v=>{for(let C=0;C<zt.length;C++)r[zt[C]].cancel(v)},state:i,steps:r}}const{schedule:V,cancel:G,state:k,steps:he}=gi(typeof requestAnimationFrame<"u"?requestAnimationFrame:W,!0);let _t;function io(){_t=void 0}const N={now:()=>(_t===void 0&&N.set(k.isProcessing||Q.useManualTiming?k.timestamp:performance.now()),_t),set:t=>{_t=t,queueMicrotask(io)}},yi=t=>e=>typeof e=="string"&&e.startsWith(t),nn=yi("--"),ro=yi("var(--"),sn=t=>ro(t)?oo.test(t.split("/*")[0].trim()):!1,oo=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Tt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},jt={...Tt,transform:t=>Z(0,1,t)},$t={...Tt,default:1},Ct=t=>Math.round(t*1e5)/1e5,rn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function ao(t){return t==null}const lo=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,on=(t,e)=>n=>!!(typeof n=="string"&&lo.test(n)&&n.startsWith(t)||e&&!ao(n)&&Object.prototype.hasOwnProperty.call(n,e)),vi=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(rn);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},co=t=>Z(0,255,t),de={...Tt,transform:t=>Math.round(co(t))},ot={test:on("rgb","red"),parse:vi("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+de.transform(t)+", "+de.transform(e)+", "+de.transform(n)+", "+Ct(jt.transform(s))+")"};function uo(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Ae={test:on("#"),parse:uo,transform:ot.transform},Ut=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),tt=Ut("deg"),q=Ut("%"),w=Ut("px"),ho=Ut("vh"),fo=Ut("vw"),jn={...q,parse:t=>q.parse(t)/100,transform:t=>q.transform(t*100)},ht={test:on("hsl","hue"),parse:vi("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+q.transform(Ct(e))+", "+q.transform(Ct(n))+", "+Ct(jt.transform(s))+")"},L={test:t=>ot.test(t)||Ae.test(t)||ht.test(t),parse:t=>ot.test(t)?ot.parse(t):ht.test(t)?ht.parse(t):Ae.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?ot.transform(t):ht.transform(t),getAnimatableNone:t=>{const e=L.parse(t);return e.alpha=0,L.transform(e)}},mo=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function po(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(rn))==null?void 0:e.length)||0)+(((n=t.match(mo))==null?void 0:n.length)||0)>0}const xi="number",Ti="color",go="var",yo="var(",kn="${}",vo=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function kt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(vo,l=>(L.test(l)?(s.color.push(o),i.push(Ti),n.push(L.parse(l))):l.startsWith(yo)?(s.var.push(o),i.push(go),n.push(l)):(s.number.push(o),i.push(xi),n.push(parseFloat(l))),++o,kn)).split(kn);return{values:n,split:a,indexes:s,types:i}}function bi(t){return kt(t).values}function wi(t){const{split:e,types:n}=kt(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===xi?o+=Ct(i[r]):a===Ti?o+=L.transform(i[r]):o+=i[r]}return o}}const xo=t=>typeof t=="number"?0:L.test(t)?L.getAnimatableNone(t):t;function To(t){const e=bi(t);return wi(t)(e.map(xo))}const nt={test:po,parse:bi,createTransformer:wi,getAnimatableNone:To};function fe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function bo({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=fe(l,a,t+1/3),o=fe(l,a,t),r=fe(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function te(t,e){return n=>n>0?e:t}const D=(t,e,n)=>t+(e-t)*n,me=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},wo=[Ae,ot,ht],So=t=>wo.find(e=>e.test(t));function Bn(t){const e=So(t);if(!e)return!1;let n=e.parse(t);return e===ht&&(n=bo(n)),n}const Fn=(t,e)=>{const n=Bn(t),s=Bn(e);if(!n||!s)return te(t,e);const i={...n};return o=>(i.red=me(n.red,s.red,o),i.green=me(n.green,s.green,o),i.blue=me(n.blue,s.blue,o),i.alpha=D(n.alpha,s.alpha,o),ot.transform(i))},Ce=new Set(["none","hidden"]);function Po(t,e){return Ce.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Ao(t,e){return n=>D(t,e,n)}function an(t){return typeof t=="number"?Ao:typeof t=="string"?sn(t)?te:L.test(t)?Fn:Mo:Array.isArray(t)?Si:typeof t=="object"?L.test(t)?Fn:Co:te}function Si(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>an(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function Co(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=an(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function Vo(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const o=e.types[i],r=t.indexes[o][s[o]],a=t.values[r]??0;n[i]=a,s[o]++}return n}const Mo=(t,e)=>{const n=nt.createTransformer(e),s=kt(t),i=kt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Ce.has(t)&&!i.values.length||Ce.has(e)&&!s.values.length?Po(t,e):Nt(Si(Vo(s,i),i.values),n):te(t,e)};function Pi(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?D(t,e,n):an(t)(t,e)}const Do=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>V.update(e,n),stop:()=>G(e),now:()=>k.isProcessing?k.timestamp:N.now()}},Ai=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=Math.round(t(o/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},ee=2e4;function ln(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<ee;)e+=n,s=t.next(e);return e>=ee?1/0:e}function Eo(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(ln(s),ee);return{type:"keyframes",ease:o=>s.next(i*o).value/e,duration:X(i)}}const Ro=5;function Ci(t,e,n){const s=Math.max(e-Ro,0);return Qe(n-t(s),e-s)}const E={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},In=.001;function Lo({duration:t=E.duration,bounce:e=E.bounce,velocity:n=E.velocity,mass:s=E.mass}){let i,o,r=1-e;r=Z(E.minDamping,E.maxDamping,r),t=Z(E.minDuration,E.maxDuration,X(t)),r<1?(i=u=>{const c=u*r,h=c*t,d=c-n,f=Ve(u,r),p=Math.exp(-h);return In-d/f*p},o=u=>{const h=u*r*t,d=h*n+n,f=Math.pow(r,2)*Math.pow(u,2)*t,p=Math.exp(-h),y=Ve(Math.pow(u,2),r);return(-i(u)+In>0?-1:1)*((d-f)*p)/y}):(i=u=>{const c=Math.exp(-u*t),h=(u-n)*t+1;return-.001+c*h},o=u=>{const c=Math.exp(-u*t),h=(n-u)*(t*t);return c*h});const a=5/t,l=ko(i,o,a);if(t=_(t),isNaN(l))return{stiffness:E.stiffness,damping:E.damping,duration:t};{const u=Math.pow(l,2)*s;return{stiffness:u,damping:r*2*Math.sqrt(s*u),duration:t}}}const jo=12;function ko(t,e,n){let s=n;for(let i=1;i<jo;i++)s=s-t(s)/e(s);return s}function Ve(t,e){return t*Math.sqrt(1-e*e)}const Bo=["duration","bounce"],Fo=["stiffness","damping","mass"];function On(t,e){return e.some(n=>t[n]!==void 0)}function Io(t){let e={velocity:E.velocity,stiffness:E.stiffness,damping:E.damping,mass:E.mass,isResolvedFromDuration:!1,...t};if(!On(t,Fo)&&On(t,Bo))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*Z(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:E.mass,stiffness:i,damping:o}}else{const n=Lo(t);e={...e,...n,mass:E.mass},e.isResolvedFromDuration=!0}return e}function ne(t=E.visualDuration,e=E.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:f}=Io({...n,velocity:-X(n.velocity||0)}),p=d||0,y=u/(2*Math.sqrt(l*c)),x=r-o,g=X(Math.sqrt(l/c)),b=Math.abs(x)<5;s||(s=b?E.restSpeed.granular:E.restSpeed.default),i||(i=b?E.restDelta.granular:E.restDelta.default);let v;if(y<1){const P=Ve(g,y);v=M=>{const j=Math.exp(-y*g*M);return r-j*((p+y*g*x)/P*Math.sin(P*M)+x*Math.cos(P*M))}}else if(y===1)v=P=>r-Math.exp(-g*P)*(x+(p+g*x)*P);else{const P=g*Math.sqrt(y*y-1);v=M=>{const j=Math.exp(-y*g*M),A=Math.min(P*M,300);return r-j*((p+y*g*x)*Math.sinh(A)+P*x*Math.cosh(A))/P}}const C={calculatedDuration:f&&h||null,next:P=>{const M=v(P);if(f)a.done=P>=h;else{let j=P===0?p:0;y<1&&(j=P===0?_(p):Ci(v,P,M));const A=Math.abs(j)<=s,O=Math.abs(r-M)<=i;a.done=A&&O}return a.value=a.done?r:M,a},toString:()=>{const P=Math.min(ln(C),ee),M=Ai(j=>C.next(P*j).value,P,30);return P+"ms "+M},toTransition:()=>{}};return C}ne.applyToOptions=t=>{const e=Eo(t,100,ne);return t.ease=e.ease,t.duration=_(e.duration),t.type="keyframes",t};function Me({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},f=A=>a!==void 0&&A<a||l!==void 0&&A>l,p=A=>a===void 0?l:l===void 0||Math.abs(a-A)<Math.abs(l-A)?a:l;let y=n*e;const x=h+y,g=r===void 0?x:r(x);g!==x&&(y=g-h);const b=A=>-y*Math.exp(-A/s),v=A=>g+b(A),C=A=>{const O=b(A),U=v(A);d.done=Math.abs(O)<=u,d.value=d.done?g:U};let P,M;const j=A=>{f(d.value)&&(P=A,M=ne({keyframes:[d.value,p(d.value)],velocity:Ci(v,A,d.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:A=>{let O=!1;return!M&&P===void 0&&(O=!0,C(A),j(A)),P!==void 0&&A>=P?M.next(A-P):(!O&&C(A),d)}}}function Oo(t,e,n){const s=[],i=n||Q.mix||Pi,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||W:e;a=Nt(l,a)}s.push(a)}return s}function cn(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(Lt(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=Oo(e,s,i),l=a.length,u=c=>{if(r&&c<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(c<t[h+1]);h++);const d=yt(t[h],t[h+1],c);return a[h](d)};return n?c=>u(Z(t[0],t[o-1],c)):u}function No(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=yt(0,e,s);t.push(D(n,1,i))}}function Vi(t){const e=[0];return No(e,t.length-1),e}function Wo(t,e){return t.map(n=>n*e)}function Uo(t,e){return t.map(()=>e||mi).splice(0,t.length-1)}function Vt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=Qr(s)?s.map(Ln):Ln(s),o={done:!1,value:e[0]},r=Wo(n&&n.length===e.length?n:Vi(e),t),a=cn(r,e,{ease:Array.isArray(i)?i:Uo(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const Ho=t=>t!==null;function un(t,{repeat:e,repeatType:n="loop"},s,i=1){const o=t.filter(Ho),a=i<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||s===void 0?o[a]:s}const Ko={decay:Me,inertia:Me,tween:Vt,keyframes:Vt,spring:ne};function Mi(t){typeof t.type=="string"&&(t.type=Ko[t.type])}class hn{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const zo=t=>t/100;class dn extends hn{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var s,i;const{motionValue:n}=this.options;n&&n.updatedAt!==N.now()&&this.tick(N.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(i=(s=this.options).onStop)==null||i.call(s))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;Mi(e);const{type:n=Vt,repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=n||Vt;l!==Vt&&typeof a[0]!="number"&&(this.mixKeyframes=Nt(zo,Pi(a[0],a[1])),a=[0,100]);const u=l({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-r})),u.calculatedDuration===null&&(u.calculatedDuration=ln(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=u}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:f,type:p,onUpdate:y,finalKeyframe:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let v=this.currentTime,C=s;if(h){const A=Math.min(this.currentTime,i)/a;let O=Math.floor(A),U=A%1;!U&&A>=1&&(U=1),U===1&&O--,O=Math.min(O,h+1),!!(O%2)&&(d==="reverse"?(U=1-U,f&&(U-=f/a)):d==="mirror"&&(C=r)),v=Z(0,1,U)*a}const P=b?{done:!1,value:c[0]}:C.next(v);o&&(P.value=o(P.value));let{done:M}=P;!b&&l!==null&&(M=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const j=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&M);return j&&p!==Me&&(P.value=un(c,this.options,x,this.speed)),y&&y(P.value),j&&this.finish(),P}then(e,n){return this.finished.then(e,n)}get duration(){return X(this.calculatedDuration)}get time(){return X(this.currentTime)}set time(e){var n;e=_(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(N.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=X(this.currentTime))}play(){var i,o;if(this.isStopped)return;const{driver:e=Do,startTime:n}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),(o=(i=this.options).onPlay)==null||o.call(i);const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(N.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}function $o(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const at=t=>t*180/Math.PI,De=t=>{const e=at(Math.atan2(t[1],t[0]));return Ee(e)},Go={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:De,rotateZ:De,skewX:t=>at(Math.atan(t[1])),skewY:t=>at(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ee=t=>(t=t%360,t<0&&(t+=360),t),Nn=De,Wn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Un=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Yo={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Wn,scaleY:Un,scale:t=>(Wn(t)+Un(t))/2,rotateX:t=>Ee(at(Math.atan2(t[6],t[5]))),rotateY:t=>Ee(at(Math.atan2(-t[2],t[0]))),rotateZ:Nn,rotate:Nn,skewX:t=>at(Math.atan(t[4])),skewY:t=>at(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Re(t){return t.includes("scale")?1:0}function Le(t,e){if(!t||t==="none")return Re(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Yo,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Go,i=a}if(!i)return Re(e);const o=s[e],r=i[1].split(",").map(Xo);return typeof o=="function"?o(r):r[o]}const _o=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Le(n,e)};function Xo(t){return parseFloat(t.trim())}const bt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],wt=new Set(bt),Hn=t=>t===Tt||t===w,qo=new Set(["x","y","z"]),Zo=bt.filter(t=>!qo.has(t));function Jo(t){const e=[];return Zo.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const lt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Le(e,"x"),y:(t,{transform:e})=>Le(e,"y")};lt.translateX=lt.x;lt.translateY=lt.y;const ct=new Set;let je=!1,ke=!1,Be=!1;function Di(){if(ke){const t=Array.from(ct).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=Jo(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{var a;(a=s.getValue(o))==null||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}ke=!1,je=!1,ct.forEach(t=>t.complete(Be)),ct.clear()}function Ei(){ct.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ke=!0)})}function Qo(){Be=!0,Ei(),Di(),Be=!1}class fn{constructor(e,n,s,i,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(ct.add(this),je||(je=!0,V.read(Ei),V.resolveKeyframes(Di))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const o=i==null?void 0:i.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const a=s.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),i&&o===void 0&&i.set(e[0])}$o(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ct.delete(this)}cancel(){this.state==="scheduled"&&(ct.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const ta=t=>t.startsWith("--");function ea(t,e,n){ta(e)?t.style.setProperty(e,n):t.style[e]=n}const Ri=Ze(()=>window.ScrollTimeline!==void 0),na={};function sa(t,e){const n=Ze(t);return()=>na[e]??n()}const Li=sa(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),At=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Kn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:At([0,.65,.55,1]),circOut:At([.55,0,1,.45]),backIn:At([.31,.01,.66,-.59]),backOut:At([.33,1.53,.69,.99])};function ji(t,e){if(t)return typeof t=="function"?Li()?Ai(t,e):"ease-out":pi(t)?At(t):Array.isArray(t)?t.map(n=>ji(n,e)||Kn.easeOut):Kn[t]}function ia(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=ji(a,i);Array.isArray(h)&&(c.easing=h);const d={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return u&&(d.pseudoElement=u),t.animate(c,d)}function ki(t){return typeof t=="function"&&"applyToOptions"in t}function ra({type:t,...e}){return ki(t)&&Li()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class oa extends hn{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,Lt(typeof e.type!="string");const u=ra(e);this.animation=ia(n,s,i,u,o),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const c=un(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(c):ea(n,s,c),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,s;const e=((s=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:s.call(n).duration)||0;return X(Number(e))}get time(){return X(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=_(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&Ri()?(this.animation.timeline=e,W):n(this)}}const Bi={anticipate:hi,backInOut:ui,circInOut:fi};function aa(t){return t in Bi}function la(t){typeof t.ease=="string"&&aa(t.ease)&&(t.ease=Bi[t.ease])}const zn=10;class ca extends oa{constructor(e){la(e),Mi(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new dn({...r,autoplay:!1}),l=_(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-zn).value,a.sample(l).value,zn),a.stop()}}const $n=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(nt.test(t)||t==="0")&&!t.startsWith("url("));function ua(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function ha(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=$n(i,e),a=$n(o,e);return!r||!a?!1:ua(t)||(n==="spring"||ki(n))&&s}function mn(t){return ii(t)&&"offsetHeight"in t}const da=new Set(["opacity","clipPath","filter","transform"]),fa=Ze(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function ma(t){var u;const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:o,type:r}=t;if(!mn((u=e==null?void 0:e.owner)==null?void 0:u.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return fa()&&n&&da.has(n)&&(n!=="transform"||!l)&&!a&&!s&&i!=="mirror"&&o!==0&&r!=="inertia"}const pa=40;class ga extends hn{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:u,element:c,...h}){var p;super(),this.stop=()=>{var y,x;this._animation&&(this._animation.stop(),(y=this.stopTimeline)==null||y.call(this)),(x=this.keyframeResolver)==null||x.cancel()},this.createdAt=N.now();const d={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,name:l,motionValue:u,element:c,...h},f=(c==null?void 0:c.KeyframeResolver)||fn;this.keyframeResolver=new f(a,(y,x,g)=>this.onKeyframesResolved(y,x,d,!g),l,u,c),(p=this.keyframeResolver)==null||p.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:u,onUpdate:c}=s;this.resolvedAt=N.now(),ha(e,o,r,a)||((Q.instantAnimations||!l)&&(c==null||c(un(e,s,n))),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const d={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>pa?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},f=!u&&ma(d)?new ca({...d,element:d.motionValue.owner.current}):new dn(d);f.finished.then(()=>this.notifyFinished()).catch(W),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),Qo()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const ya=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function va(t){const e=ya.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Fi(t,e,n=1){const[s,i]=va(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return si(r)?parseFloat(r):r}return sn(i)?Fi(i,e,n+1):i}function pn(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const Ii=new Set(["width","height","top","left","right","bottom",...bt]),xa={test:t=>t==="auto",parse:t=>t},Oi=t=>e=>e.test(t),Ni=[Tt,w,q,tt,fo,ho,xa],Gn=t=>Ni.find(Oi(t));function Ta(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||ri(t):!0}const ba=new Set(["brightness","contrast","saturate","opacity"]);function wa(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(rn)||[];if(!s)return t;const i=n.replace(s,"");let o=ba.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const Sa=/\b([a-z-]*)\(.*?\)/gu,Fe={...nt,getAnimatableNone:t=>{const e=t.match(Sa);return e?e.map(wa).join(" "):t}},Yn={...Tt,transform:Math.round},Pa={rotate:tt,rotateX:tt,rotateY:tt,rotateZ:tt,scale:$t,scaleX:$t,scaleY:$t,scaleZ:$t,skew:tt,skewX:tt,skewY:tt,distance:w,translateX:w,translateY:w,translateZ:w,x:w,y:w,z:w,perspective:w,transformPerspective:w,opacity:jt,originX:jn,originY:jn,originZ:w},gn={borderWidth:w,borderTopWidth:w,borderRightWidth:w,borderBottomWidth:w,borderLeftWidth:w,borderRadius:w,radius:w,borderTopLeftRadius:w,borderTopRightRadius:w,borderBottomRightRadius:w,borderBottomLeftRadius:w,width:w,maxWidth:w,height:w,maxHeight:w,top:w,right:w,bottom:w,left:w,padding:w,paddingTop:w,paddingRight:w,paddingBottom:w,paddingLeft:w,margin:w,marginTop:w,marginRight:w,marginBottom:w,marginLeft:w,backgroundPositionX:w,backgroundPositionY:w,...Pa,zIndex:Yn,fillOpacity:jt,strokeOpacity:jt,numOctaves:Yn},Aa={...gn,color:L,backgroundColor:L,outlineColor:L,fill:L,stroke:L,borderColor:L,borderTopColor:L,borderRightColor:L,borderBottomColor:L,borderLeftColor:L,filter:Fe,WebkitFilter:Fe},Wi=t=>Aa[t];function Ui(t,e){let n=Wi(t);return n!==Fe&&(n=nt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Ca=new Set(["auto","none","0"]);function Va(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!Ca.has(o)&&kt(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Ui(n,i)}class Ma extends fn{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let u=e[l];if(typeof u=="string"&&(u=u.trim(),sn(u))){const c=Fi(u,n.current);c!==void 0&&(e[l]=c),l===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Ii.has(s)||e.length!==2)return;const[i,o]=e,r=Gn(i),a=Gn(o);if(r!==a)if(Hn(r)&&Hn(a))for(let l=0;l<e.length;l++){const u=e[l];typeof u=="string"&&(e[l]=parseFloat(u))}else lt[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Ta(e[i]))&&s.push(i);s.length&&Va(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=lt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var a;const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,r=s[o];s[o]=lt[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,u])=>{e.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function Hi(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){const i=document.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const Ki=(t,e)=>e&&typeof t=="number"?e.transform(t):t,_n=30,Da=t=>!isNaN(parseFloat(t)),Mt={current:void 0};class Ea{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{var r,a;const o=N.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();i&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=N.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Da(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Je);const s=this.events[e].add(n);return e==="change"?()=>{s(),V.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return Mt.current&&Mt.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const e=N.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>_n)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,_n);return Qe(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function $(t,e){return new Ea(t,e)}const{schedule:yn}=gi(queueMicrotask,!1),z={x:!1,y:!1};function zi(){return z.x||z.y}function Ra(t){return t==="x"||t==="y"?z[t]?null:(z[t]=!0,()=>{z[t]=!1}):z.x||z.y?null:(z.x=z.y=!0,()=>{z.x=z.y=!1})}function $i(t,e){const n=Hi(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function Xn(t){return!(t.pointerType==="touch"||zi())}function La(t,e,n={}){const[s,i,o]=$i(t,n),r=a=>{if(!Xn(a))return;const{target:l}=a,u=e(l,a);if(typeof u!="function"||!l)return;const c=h=>{Xn(h)&&(u(h),l.removeEventListener("pointerleave",c))};l.addEventListener("pointerleave",c,i)};return s.forEach(a=>{a.addEventListener("pointerenter",r,i)}),o}const Gi=(t,e)=>e?t===e?!0:Gi(t,e.parentElement):!1,vn=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,ja=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function ka(t){return ja.has(t.tagName)||t.tabIndex!==-1}const Xt=new WeakSet;function qn(t){return e=>{e.key==="Enter"&&t(e)}}function pe(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const Ba=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=qn(()=>{if(Xt.has(n))return;pe(n,"down");const i=qn(()=>{pe(n,"up")}),o=()=>pe(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function Zn(t){return vn(t)&&!zi()}function Fa(t,e,n={}){const[s,i,o]=$i(t,n),r=a=>{const l=a.currentTarget;if(!Zn(a))return;Xt.add(l);const u=e(l,a),c=(f,p)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",d),Xt.has(l)&&Xt.delete(l),Zn(f)&&typeof u=="function"&&u(f,{success:p})},h=f=>{c(f,l===window||l===document||n.useGlobalTarget||Gi(l,f.target))},d=f=>{c(f,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",d,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,i),mn(a)&&(a.addEventListener("focus",u=>Ba(u,i)),!ka(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function xn(t){return ii(t)&&"ownerSVGElement"in t}const qt=new WeakMap;let et;const Yi=(t,e,n)=>(s,i)=>i&&i[0]?i[0][t+"Size"]:xn(s)&&"getBBox"in s?s.getBBox()[e]:s[n],Ia=Yi("inline","width","offsetWidth"),Oa=Yi("block","height","offsetHeight");function Na({target:t,borderBoxSize:e}){var n;(n=qt.get(t))==null||n.forEach(s=>{s(t,{get width(){return Ia(t,e)},get height(){return Oa(t,e)}})})}function Wa(t){t.forEach(Na)}function Ua(){typeof ResizeObserver>"u"||(et=new ResizeObserver(Wa))}function Ha(t,e){et||Ua();const n=Hi(t);return n.forEach(s=>{let i=qt.get(s);i||(i=new Set,qt.set(s,i)),i.add(e),et==null||et.observe(s)}),()=>{n.forEach(s=>{const i=qt.get(s);i==null||i.delete(e),i!=null&&i.size||et==null||et.unobserve(s)})}}const Zt=new Set;let dt;function Ka(){dt=()=>{const t={get width(){return window.innerWidth},get height(){return window.innerHeight}};Zt.forEach(e=>e(t))},window.addEventListener("resize",dt)}function za(t){return Zt.add(t),dt||Ka(),()=>{Zt.delete(t),!Zt.size&&typeof dt=="function"&&(window.removeEventListener("resize",dt),dt=void 0)}}function $a(t,e){return typeof t=="function"?za(t):Ha(t,e)}function _i(t,e){let n;const s=()=>{const{currentTime:i}=e,r=(i===null?0:i.value)/100;n!==r&&t(r),n=r};return V.preUpdate(s,!0),()=>G(s)}function Ga(t){return xn(t)&&t.tagName==="svg"}function Ya(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],i=t[1+n],o=t[2+n],r=t[3+n],a=cn(i,o,r);return e?a(s):a}const B=t=>!!(t&&t.getVelocity),_a=[...Ni,L,nt],Xa=t=>_a.find(Oi(t)),Tn=T.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});function qa(t=!0){const e=T.useContext(_e);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,o=T.useId();T.useEffect(()=>{if(t)return i(o)},[t]);const r=T.useCallback(()=>t&&s&&s(o),[o,s,t]);return!n&&s?[!1,r]:[!0]}const Xi=T.createContext({strict:!1}),Jn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},vt={};for(const t in Jn)vt[t]={isEnabled:e=>Jn[t].some(n=>!!e[n])};function Za(t){for(const e in t)vt[e]={...vt[e],...t[e]}}const Ja=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function se(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Ja.has(t)}let qi=t=>!se(t);function Qa(t){typeof t=="function"&&(qi=e=>e.startsWith("on")?!se(e):t(e))}try{Qa(require("@emotion/is-prop-valid").default)}catch{}function tl(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(qi(i)||n===!0&&se(i)||!e&&!se(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function el(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const oe=T.createContext({});function ae(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function Bt(t){return typeof t=="string"||Array.isArray(t)}const bn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],wn=["initial",...bn];function le(t){return ae(t.animate)||wn.some(e=>Bt(t[e]))}function Zi(t){return!!(le(t)||t.variants)}function nl(t,e){if(le(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Bt(n)?n:void 0,animate:Bt(s)?s:void 0}}return t.inherit!==!1?e:{}}function sl(t){const{initial:e,animate:n}=nl(t,T.useContext(oe));return T.useMemo(()=>({initial:e,animate:n}),[Qn(e),Qn(n)])}function Qn(t){return Array.isArray(t)?t.join(" "):t}const il=Symbol.for("motionComponentSymbol");function ft(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function rl(t,e,n){return T.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):ft(n)&&(n.current=s))},[e])}const Sn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ol="framerAppearId",Ji="data-"+Sn(ol),Qi=T.createContext({});function al(t,e,n,s,i){var y,x;const{visualElement:o}=T.useContext(oe),r=T.useContext(Xi),a=T.useContext(_e),l=T.useContext(Tn).reducedMotion,u=T.useRef(null);s=s||r.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const c=u.current,h=T.useContext(Qi);c&&!c.projection&&i&&(c.type==="html"||c.type==="svg")&&ll(u.current,n,i,h);const d=T.useRef(!1);T.useInsertionEffect(()=>{c&&d.current&&c.update(n,a)});const f=n[Ji],p=T.useRef(!!f&&!((y=window.MotionHandoffIsComplete)!=null&&y.call(window,f))&&((x=window.MotionHasOptimisedAnimation)==null?void 0:x.call(window,f)));return Ye(()=>{c&&(d.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),yn.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),T.useEffect(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{var g;(g=window.MotionHandoffMarkAsComplete)==null||g.call(window,f)}),p.current=!1))}),c}function ll(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:tr(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&ft(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,crossfade:c,layoutScroll:l,layoutRoot:u})}function tr(t){if(t)return t.options.allowProjection!==!1?t.projection:tr(t.parent)}function cl({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&Za(t);function o(a,l){let u;const c={...T.useContext(Tn),...a,layoutId:ul(a)},{isStatic:h}=c,d=sl(a),f=s(a,h);if(!h&&Ge){hl();const p=dl(c);u=p.MeasureLayout,d.visualElement=al(i,f,c,e,p.ProjectionNode)}return m.jsxs(oe.Provider,{value:d,children:[u&&d.visualElement?m.jsx(u,{visualElement:d.visualElement,...c}):null,n(i,a,rl(f,d.visualElement,l),f,h,d.visualElement)]})}o.displayName=`motion.${typeof i=="string"?i:`create(${i.displayName??i.name??""})`}`;const r=T.forwardRef(o);return r[il]=i,r}function ul({layoutId:t}){const e=T.useContext(ni).id;return e&&t!==void 0?e+"-"+t:t}function hl(t,e){T.useContext(Xi).strict}function dl(t){const{drag:e,layout:n}=vt;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Ft={};function fl(t){for(const e in t)Ft[e]=t[e],nn(e)&&(Ft[e].isCSSVariable=!0)}function er(t,{layout:e,layoutId:n}){return wt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Ft[t]||t==="opacity")}const ml={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},pl=bt.length;function gl(t,e,n){let s="",i=!0;for(let o=0;o<pl;o++){const r=bt[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=Ki(a,gn[r]);if(!l){i=!1;const c=ml[r]||r;s+=`${c}(${u}) `}n&&(e[r]=u)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function Pn(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const u=e[l];if(wt.has(l)){r=!0;continue}else if(nn(l)){i[l]=u;continue}else{const c=Ki(u,gn[l]);l.startsWith("origin")?(a=!0,o[l]=c):s[l]=c}}if(e.transform||(r||n?s.transform=gl(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=o;s.transformOrigin=`${l} ${u} ${c}`}}const An=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nr(t,e,n){for(const s in e)!B(e[s])&&!er(s,n)&&(t[s]=e[s])}function yl({transformTemplate:t},e){return T.useMemo(()=>{const n=An();return Pn(n,e,t),Object.assign({},n.vars,n.style)},[e])}function vl(t,e){const n=t.style||{},s={};return nr(s,n,t),Object.assign(s,yl(t,e)),s}function xl(t,e){const n={},s=vl(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const Tl={offset:"stroke-dashoffset",array:"stroke-dasharray"},bl={offset:"strokeDashoffset",array:"strokeDasharray"};function wl(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?Tl:bl;t[o.offset]=w.transform(-s);const r=w.transform(e),a=w.transform(n);t[o.array]=`${r} ${a}`}function sr(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:o=1,pathOffset:r=0,...a},l,u,c){if(Pn(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=(c==null?void 0:c.transformBox)??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),i!==void 0&&wl(h,i,o,r,!1)}const ir=()=>({...An(),attrs:{}}),rr=t=>typeof t=="string"&&t.toLowerCase()==="svg";function Sl(t,e,n,s){const i=T.useMemo(()=>{const o=ir();return sr(o,e,rr(s),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};nr(o,t.style,t),i.style={...o,...i.style}}return i}const Pl=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Cn(t){return typeof t!="string"||t.includes("-")?!1:!!(Pl.indexOf(t)>-1||/[A-Z]/u.test(t))}function Al(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(Cn(n)?Sl:xl)(s,o,r,n),u=tl(s,typeof n=="string",t),c=n!==T.Fragment?{...u,...l,ref:i}:{},{children:h}=s,d=T.useMemo(()=>B(h)?h.get():h,[h]);return T.createElement(n,{...c,children:d})}}function ts(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function Vn(t,e,n,s){if(typeof e=="function"){const[i,o]=ts(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=ts(s);e=e(n!==void 0?n:t.custom,i,o)}return e}function Jt(t){return B(t)?t.get():t}function Cl({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:Vl(n,s,i,t),renderState:e()}}const or=t=>(e,n)=>{const s=T.useContext(oe),i=T.useContext(_e),o=()=>Cl(t,e,s,i);return n?o():re(o)};function Vl(t,e,n,s){const i={},o=s(t,{});for(const d in o)i[d]=Jt(o[d]);let{initial:r,animate:a}=t;const l=le(t),u=Zi(t);e&&u&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let c=n?n.initial===!1:!1;c=c||r===!1;const h=c?a:r;if(h&&typeof h!="boolean"&&!ae(h)){const d=Array.isArray(h)?h:[h];for(let f=0;f<d.length;f++){const p=Vn(t,d[f]);if(p){const{transitionEnd:y,transition:x,...g}=p;for(const b in g){let v=g[b];if(Array.isArray(v)){const C=c?v.length-1:0;v=v[C]}v!==null&&(i[b]=v)}for(const b in y)i[b]=y[b]}}}return i}function Mn(t,e,n){var o;const{style:s}=t,i={};for(const r in s)(B(s[r])||e.style&&B(e.style[r])||er(r,t)||((o=n==null?void 0:n.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(i[r]=s[r]);return i}const Ml={useVisualState:or({scrapeMotionValuesFromProps:Mn,createRenderState:An})};function ar(t,e,n){const s=Mn(t,e,n);for(const i in t)if(B(t[i])||B(e[i])){const o=bt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}const Dl={useVisualState:or({scrapeMotionValuesFromProps:ar,createRenderState:ir})};function El(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const r={...Cn(s)?Dl:Ml,preloadedFeatures:t,useRender:Al(i),createVisualElement:e,Component:s};return cl(r)}}function It(t,e,n){const s=t.getProps();return Vn(s,e,n!==void 0?n:s.custom,t)}const Ie=t=>Array.isArray(t);function Rl(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,$(n))}function Ll(t){return Ie(t)?t[t.length-1]||0:t}function jl(t,e){const n=It(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=Ll(o[r]);Rl(t,r,a)}}function kl(t){return!!(B(t)&&t.add)}function Oe(t,e){const n=t.getValue("willChange");if(kl(n))return n.add(e);if(!n&&Q.WillChange){const s=new Q.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function lr(t){return t.props[Ji]}const Bl=t=>t!==null;function Fl(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Bl),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[o]}const Il={type:"spring",stiffness:500,damping:25,restSpeed:10},Ol=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Nl={type:"keyframes",duration:.8},Wl={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ul=(t,{keyframes:e})=>e.length>2?Nl:wt.has(t)?t.startsWith("scale")?Ol(e[1]):Il:Wl;function Hl({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Dn=(t,e,n,s={},i,o)=>r=>{const a=pn(s,t)||{},l=a.delay||s.delay||0;let{elapsed:u=0}=s;u=u-_(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:d=>{e.set(d),a.onUpdate&&a.onUpdate(d)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};Hl(a)||Object.assign(c,Ul(t,c)),c.duration&&(c.duration=_(c.duration)),c.repeatDelay&&(c.repeatDelay=_(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let h=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(h=!0)),(Q.instantAnimations||Q.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const d=Fl(c.keyframes,a);if(d!==void 0){V.update(()=>{c.onUpdate(d),c.onComplete()});return}}return a.isSync?new dn(c):new ga(c)};function Kl({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function cr(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;s&&(o=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const c in a){const h=t.getValue(c,t.latestValues[c]??null),d=a[c];if(d===void 0||u&&Kl(u,c))continue;const f={delay:n,...pn(o||{},c)},p=h.get();if(p!==void 0&&!h.isAnimating&&!Array.isArray(d)&&d===p&&!f.velocity)continue;let y=!1;if(window.MotionHandoffAnimation){const g=lr(t);if(g){const b=window.MotionHandoffAnimation(g,c,V);b!==null&&(f.startTime=b,y=!0)}}Oe(t,c),h.start(Dn(c,h,d,t.shouldReduceMotion&&Ii.has(c)?{type:!1}:f,t,y));const x=h.animation;x&&l.push(x)}return r&&Promise.all(l).then(()=>{V.update(()=>{r&&jl(t,r)})}),l}function Ne(t,e,n={}){var l;const s=It(t,e,n.type==="exit"?(l=t.presenceContext)==null?void 0:l.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(cr(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:h,staggerDirection:d}=i;return zl(t,e,u,c,h,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[u,c]=a==="beforeChildren"?[o,r]:[r,o];return u().then(()=>c())}else return Promise.all([o(),r(n.delay)])}function zl(t,e,n=0,s=0,i=0,o=1,r){const a=[],l=t.variantChildren.size,u=(l-1)*i,c=typeof s=="function",h=c?d=>s(d,l):o===1?(d=0)=>d*i:(d=0)=>u-d*i;return Array.from(t.variantChildren).sort($l).forEach((d,f)=>{d.notify("AnimationStart",e),a.push(Ne(d,e,{...r,delay:n+(c?0:s)+h(f)}).then(()=>d.notify("AnimationComplete",e)))}),Promise.all(a)}function $l(t,e){return t.sortNodePosition(e)}function Gl(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>Ne(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=Ne(t,e,n);else{const i=typeof e=="function"?It(t,e,n.custom):e;s=Promise.all(cr(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function ur(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const Yl=wn.length;function hr(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?hr(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<Yl;n++){const s=wn[n],i=t.props[s];(Bt(i)||i===!1)&&(e[s]=i)}return e}const _l=[...bn].reverse(),Xl=bn.length;function ql(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Gl(t,n,s)))}function Zl(t){let e=ql(t),n=es(),s=!0;const i=l=>(u,c)=>{var d;const h=It(t,c,l==="exit"?(d=t.presenceContext)==null?void 0:d.custom:void 0);if(h){const{transition:f,transitionEnd:p,...y}=h;u={...u,...y,...p}}return u};function o(l){e=l(t)}function r(l){const{props:u}=t,c=hr(t.parent)||{},h=[],d=new Set;let f={},p=1/0;for(let x=0;x<Xl;x++){const g=_l[x],b=n[g],v=u[g]!==void 0?u[g]:c[g],C=Bt(v),P=g===l?b.isActive:null;P===!1&&(p=x);let M=v===c[g]&&v!==u[g]&&C;if(M&&s&&t.manuallyAnimateOnMount&&(M=!1),b.protectedKeys={...f},!b.isActive&&P===null||!v&&!b.prevProp||ae(v)||typeof v=="boolean")continue;const j=Jl(b.prevProp,v);let A=j||g===l&&b.isActive&&!M&&C||x>p&&C,O=!1;const U=Array.isArray(v)?v:[v];let ut=U.reduce(i(g),{});P===!1&&(ut={});const{prevResolvedValues:En={}}=b,Ir={...En,...ut},Rn=F=>{A=!0,d.has(F)&&(O=!0,d.delete(F)),b.needsAnimating[F]=!0;const J=t.getValue(F);J&&(J.liveStyle=!1)};for(const F in Ir){const J=ut[F],ce=En[F];if(f.hasOwnProperty(F))continue;let ue=!1;Ie(J)&&Ie(ce)?ue=!ur(J,ce):ue=J!==ce,ue?J!=null?Rn(F):d.add(F):J!==void 0&&d.has(F)?Rn(F):b.protectedKeys[F]=!0}b.prevProp=v,b.prevResolvedValues=ut,b.isActive&&(f={...f,...ut}),s&&t.blockInitialAnimation&&(A=!1),A&&(!(M&&j)||O)&&h.push(...U.map(F=>({animation:F,options:{type:g}})))}if(d.size){const x={};if(typeof u.initial!="boolean"){const g=It(t,Array.isArray(u.initial)?u.initial[0]:u.initial);g&&g.transition&&(x.transition=g.transition)}d.forEach(g=>{const b=t.getBaseTarget(g),v=t.getValue(g);v&&(v.liveStyle=!0),x[g]=b??null}),h.push({animation:x})}let y=!!h.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(y=!1),s=!1,y?e(h):Promise.resolve()}function a(l,u){var h;if(n[l].isActive===u)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(d=>{var f;return(f=d.animationState)==null?void 0:f.setActive(l,u)}),n[l].isActive=u;const c=r(l);for(const d in n)n[d].protectedKeys={};return c}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=es(),s=!0}}}function Jl(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!ur(e,t):!1}function it(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function es(){return{animate:it(!0),whileInView:it(),whileHover:it(),whileTap:it(),whileDrag:it(),whileFocus:it(),exit:it()}}class st{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Ql extends st{constructor(e){super(e),e.animationState||(e.animationState=Zl(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();ae(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let tc=0;class ec extends st{constructor(){super(...arguments),this.id=tc++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const nc={animation:{Feature:Ql},exit:{Feature:ec}};function Ot(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Ht(t){return{point:{x:t.pageX,y:t.pageY}}}const sc=t=>e=>vn(e)&&t(e,Ht(e));function Dt(t,e,n,s){return Ot(t,e,sc(n),s)}function dr({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function ic({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function rc(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}const fr=1e-4,oc=1-fr,ac=1+fr,mr=.01,lc=0-mr,cc=0+mr;function I(t){return t.max-t.min}function uc(t,e,n){return Math.abs(t-e)<=n}function ns(t,e,n,s=.5){t.origin=s,t.originPoint=D(e.min,e.max,t.origin),t.scale=I(n)/I(e),t.translate=D(n.min,n.max,t.origin)-t.originPoint,(t.scale>=oc&&t.scale<=ac||isNaN(t.scale))&&(t.scale=1),(t.translate>=lc&&t.translate<=cc||isNaN(t.translate))&&(t.translate=0)}function Et(t,e,n,s){ns(t.x,e.x,n.x,s?s.originX:void 0),ns(t.y,e.y,n.y,s?s.originY:void 0)}function ss(t,e,n){t.min=n.min+e.min,t.max=t.min+I(e)}function hc(t,e,n){ss(t.x,e.x,n.x),ss(t.y,e.y,n.y)}function is(t,e,n){t.min=e.min-n.min,t.max=t.min+I(e)}function Rt(t,e,n){is(t.x,e.x,n.x),is(t.y,e.y,n.y)}const rs=()=>({translate:0,scale:1,origin:0,originPoint:0}),mt=()=>({x:rs(),y:rs()}),os=()=>({min:0,max:0}),R=()=>({x:os(),y:os()});function K(t){return[t("x"),t("y")]}function ge(t){return t===void 0||t===1}function We({scale:t,scaleX:e,scaleY:n}){return!ge(t)||!ge(e)||!ge(n)}function rt(t){return We(t)||pr(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function pr(t){return as(t.x)||as(t.y)}function as(t){return t&&t!=="0%"}function ie(t,e,n){const s=t-n,i=e*s;return n+i}function ls(t,e,n,s,i){return i!==void 0&&(t=ie(t,i,s)),ie(t,n,s)+e}function Ue(t,e=0,n=1,s,i){t.min=ls(t.min,e,n,s,i),t.max=ls(t.max,e,n,s,i)}function gr(t,{x:e,y:n}){Ue(t.x,e.translate,e.scale,e.originPoint),Ue(t.y,n.translate,n.scale,n.originPoint)}const cs=.999999999999,us=1.0000000000001;function dc(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&gt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,gr(t,r)),s&&rt(o.latestValues)&&gt(t,o.latestValues))}e.x<us&&e.x>cs&&(e.x=1),e.y<us&&e.y>cs&&(e.y=1)}function pt(t,e){t.min=t.min+e,t.max=t.max+e}function hs(t,e,n,s,i=.5){const o=D(t.min,t.max,i);Ue(t,e,n,o,s)}function gt(t,e){hs(t.x,e.x,e.scaleX,e.scale,e.originX),hs(t.y,e.y,e.scaleY,e.scale,e.originY)}function yr(t,e){return dr(rc(t.getBoundingClientRect(),e))}function fc(t,e,n){const s=yr(t,n),{scroll:i}=e;return i&&(pt(s.x,i.offset.x),pt(s.y,i.offset.y)),s}const vr=({current:t})=>t?t.ownerDocument.defaultView:null,ds=(t,e)=>Math.abs(t-e);function mc(t,e){const n=ds(t.x,e.x),s=ds(t.y,e.y);return Math.sqrt(n**2+s**2)}class xr{constructor(e,n,{transformPagePoint:s,contextWindow:i=window,dragSnapToOrigin:o=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=ve(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,p=mc(d.offset,{x:0,y:0})>=this.distanceThreshold;if(!f&&!p)return;const{point:y}=d,{timestamp:x}=k;this.history.push({...y,timestamp:x});const{onStart:g,onMove:b}=this.handlers;f||(g&&g(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=ye(f,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:p,onSessionEnd:y,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=ve(d.type==="pointercancel"?this.lastMoveEventInfo:ye(f,this.transformPagePoint),this.history);this.startEvent&&p&&p(d,g),y&&y(d,g)},!vn(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.distanceThreshold=r,this.contextWindow=i||window;const a=Ht(e),l=ye(a,this.transformPagePoint),{point:u}=l,{timestamp:c}=k;this.history=[{...u,timestamp:c}];const{onSessionStart:h}=n;h&&h(e,ve(l,this.history)),this.removeListeners=Nt(Dt(this.contextWindow,"pointermove",this.handlePointerMove),Dt(this.contextWindow,"pointerup",this.handlePointerUp),Dt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),G(this.updatePoint)}}function ye(t,e){return e?{point:e(t.point)}:t}function fs(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ve({point:t},e){return{point:t,delta:fs(t,Tr(e)),offset:fs(t,pc(e)),velocity:gc(e,.1)}}function pc(t){return t[0]}function Tr(t){return t[t.length-1]}function gc(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=Tr(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>_(e)));)n--;if(!s)return{x:0,y:0};const o=X(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function yc(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?D(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?D(n,t,s.max):Math.min(t,n)),t}function ms(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function vc(t,{top:e,left:n,bottom:s,right:i}){return{x:ms(t.x,n,i),y:ms(t.y,e,s)}}function ps(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function xc(t,e){return{x:ps(t.x,e.x),y:ps(t.y,e.y)}}function Tc(t,e){let n=.5;const s=I(t),i=I(e);return i>s?n=yt(e.min,e.max-s,t.min):s>i&&(n=yt(t.min,t.max-i,e.min)),Z(0,1,n)}function bc(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const He=.35;function wc(t=He){return t===!1?t=0:t===!0&&(t=He),{x:gs(t,"left","right"),y:gs(t,"top","bottom")}}function gs(t,e,n){return{min:ys(t,e),max:ys(t,n)}}function ys(t,e){return typeof t=="number"?t:t[e]||0}const Sc=new WeakMap;class Pc{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=R(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:n=!1,distanceThreshold:s}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const o=h=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Ht(h).point)},r=(h,d)=>{const{drag:f,dragPropagation:p,onDragStart:y}=this.getProps();if(f&&!p&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Ra(f),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=d,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),K(g=>{let b=this.getAxisMotionValue(g).get()||0;if(q.test(b)){const{projection:v}=this.visualElement;if(v&&v.layout){const C=v.layout.layoutBox[g];C&&(b=I(C)*(parseFloat(b)/100))}}this.originPoint[g]=b}),y&&V.postRender(()=>y(h,d)),Oe(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},a=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d;const{dragPropagation:f,dragDirectionLock:p,onDirectionLock:y,onDrag:x}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:g}=d;if(p&&this.currentDirection===null){this.currentDirection=Ac(g),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",d.point,g),this.updateAxis("y",d.point,g),this.visualElement.render(),x&&x(h,d)},l=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d,this.stop(h,d),this.latestPointerEvent=null,this.latestPanInfo=null},u=()=>K(h=>{var d;return this.getAnimationState(h)==="paused"&&((d=this.getAxisMotionValue(h).animation)==null?void 0:d.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new xr(e,{onSessionStart:o,onStart:r,onMove:a,onSessionEnd:l,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,distanceThreshold:s,contextWindow:vr(this.visualElement)})}stop(e,n){const s=e||this.latestPointerEvent,i=n||this.latestPanInfo,o=this.isDragging;if(this.cancel(),!o||!i||!s)return;const{velocity:r}=i;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&V.postRender(()=>a(s,i))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Gt(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=yc(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:n}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,i=this.constraints;e&&ft(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=vc(s.layoutBox,e):this.constraints=!1,this.elastic=wc(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&K(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=bc(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ft(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=fc(s,i.root,this.visualElement.getTransformPagePoint());let r=xc(i.layout.layoutBox,o);if(n){const a=n(ic(r));this.hasMutatedConstraints=!!a,a&&(r=dr(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=K(c=>{if(!Gt(c,n,this.currentDirection))return;let h=l&&l[c]||{};r&&(h={min:0,max:0});const d=i?200:1e6,f=i?40:1e7,p={type:"inertia",velocity:s?e[c]:0,bounceStiffness:d,bounceDamping:f,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(c,p)});return Promise.all(u).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Oe(this.visualElement,e),s.start(Dn(e,s,0,n,this.visualElement,!1))}stopAnimation(){K(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){K(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){K(n=>{const{drag:s}=this.getProps();if(!Gt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-D(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!ft(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};K(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();i[r]=Tc({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),K(r=>{if(!Gt(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:u}=this.constraints[r];a.set(D(l,u,i[r]))})}addListeners(){if(!this.visualElement.current)return;Sc.set(this.visualElement,this);const e=this.visualElement.current,n=Dt(e,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();ft(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.read(s);const r=Ot(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(K(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=l[c].translate,h.set(h.get()+l[c].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=He,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function Gt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Ac(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Cc extends st{constructor(e){super(e),this.removeGroupControls=W,this.removeListeners=W,this.controls=new Pc(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W}unmount(){this.removeGroupControls(),this.removeListeners()}}const vs=t=>(e,n)=>{t&&V.postRender(()=>t(e,n))};class Vc extends st{constructor(){super(...arguments),this.removePointerDownListener=W}onPointerDown(e){this.session=new xr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:vr(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:vs(e),onStart:vs(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&V.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=Dt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Qt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function xs(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const St={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(w.test(t))t=parseFloat(t);else return t;const n=xs(t,e.target.x),s=xs(t,e.target.y);return`${n}% ${s}%`}},Mc={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=nt.parse(t);if(i.length>5)return s;const o=nt.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const u=D(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=u),typeof i[3+r]=="number"&&(i[3+r]/=u),o(i)}};let Ts=!1;class Dc extends T.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;fl(Ec),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),Ts&&o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Qt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,{projection:r}=s;return r&&(r.isPresent=o,Ts=!0,i||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||V.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),yn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function br(t){const[e,n]=qa(),s=T.useContext(ni);return m.jsx(Dc,{...t,layoutGroup:s,switchLayoutGroup:T.useContext(Qi),isPresent:e,safeToRemove:n})}const Ec={borderRadius:{...St,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:St,borderTopRightRadius:St,borderBottomLeftRadius:St,borderBottomRightRadius:St,boxShadow:Mc};function Rc(t,e,n){const s=B(t)?t:$(t);return s.start(Dn("",s,e,n)),s.animation}const Lc=(t,e)=>t.depth-e.depth;class jc{constructor(){this.children=[],this.isDirty=!1}add(e){Xe(this.children,e),this.isDirty=!0}remove(e){qe(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Lc),this.isDirty=!1,this.children.forEach(e)}}function kc(t,e){const n=N.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(G(s),t(o-e))};return V.setup(s,!0),()=>G(s)}const wr=["TopLeft","TopRight","BottomLeft","BottomRight"],Bc=wr.length,bs=t=>typeof t=="string"?parseFloat(t):t,ws=t=>typeof t=="number"||w.test(t);function Fc(t,e,n,s,i,o){i?(t.opacity=D(0,n.opacity??1,Ic(s)),t.opacityExit=D(e.opacity??1,0,Oc(s))):o&&(t.opacity=D(e.opacity??1,n.opacity??1,s));for(let r=0;r<Bc;r++){const a=`border${wr[r]}Radius`;let l=Ss(e,a),u=Ss(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||ws(l)===ws(u)?(t[a]=Math.max(D(bs(l),bs(u),s),0),(q.test(u)||q.test(l))&&(t[a]+="%")):t[a]=u}(e.rotate||n.rotate)&&(t.rotate=D(e.rotate||0,n.rotate||0,s))}function Ss(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Ic=Sr(0,.5,di),Oc=Sr(.5,.95,W);function Sr(t,e,n){return s=>s<t?0:s>e?1:n(yt(t,e,s))}function Ps(t,e){t.min=e.min,t.max=e.max}function H(t,e){Ps(t.x,e.x),Ps(t.y,e.y)}function As(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Cs(t,e,n,s,i){return t-=e,t=ie(t,1/n,s),i!==void 0&&(t=ie(t,1/i,s)),t}function Nc(t,e=0,n=1,s=.5,i,o=t,r=t){if(q.test(e)&&(e=parseFloat(e),e=D(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=D(o.min,o.max,s);t===o&&(a-=e),t.min=Cs(t.min,e,n,a,i),t.max=Cs(t.max,e,n,a,i)}function Vs(t,e,[n,s,i],o,r){Nc(t,e[n],e[s],e[i],e.scale,o,r)}const Wc=["x","scaleX","originX"],Uc=["y","scaleY","originY"];function Ms(t,e,n,s){Vs(t.x,e,Wc,n?n.x:void 0,s?s.x:void 0),Vs(t.y,e,Uc,n?n.y:void 0,s?s.y:void 0)}function Ds(t){return t.translate===0&&t.scale===1}function Pr(t){return Ds(t.x)&&Ds(t.y)}function Es(t,e){return t.min===e.min&&t.max===e.max}function Hc(t,e){return Es(t.x,e.x)&&Es(t.y,e.y)}function Rs(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Ar(t,e){return Rs(t.x,e.x)&&Rs(t.y,e.y)}function Ls(t){return I(t.x)/I(t.y)}function js(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Kc{constructor(){this.members=[]}add(e){Xe(this.members,e),e.scheduleRender()}remove(e){if(qe(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function zc(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:h,rotateY:d,skewX:f,skewY:p}=n;u&&(s=`perspective(${u}px) ${s}`),c&&(s+=`rotate(${c}deg) `),h&&(s+=`rotateX(${h}deg) `),d&&(s+=`rotateY(${d}deg) `),f&&(s+=`skewX(${f}deg) `),p&&(s+=`skewY(${p}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const xe=["","X","Y","Z"],$c=1e3;let Gc=0;function Te(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function Cr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=lr(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",V,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&Cr(s)}function Vr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=Gc++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Xc),this.nodes.forEach(Qc),this.nodes.forEach(tu),this.nodes.forEach(qc)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new jc)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new Je),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=xn(r)&&!Ga(r),this.instance=r;const{layoutId:a,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let c,h=0;const d=()=>this.root.updateBlockedByResize=!1;V.read(()=>{h=window.innerWidth}),t(r,()=>{const f=window.innerWidth;f!==h&&(h=f,this.root.updateBlockedByResize=!0,c&&c(),c=kc(d,250),Qt.hasAnimatedSinceResize&&(Qt.hasAnimatedSinceResize=!1,this.nodes.forEach(Fs)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||l)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:h,hasRelativeLayoutChanged:d,layout:f})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||ru,{onLayoutAnimationStart:y,onLayoutAnimationComplete:x}=u.getProps(),g=!this.targetLayout||!Ar(this.targetLayout,f),b=!h&&d;if(this.options.layoutRoot||this.resumeFrom||b||h&&(g||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v={...pn(p,"layout"),onPlay:y,onComplete:x};(u.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(c,b)}else h||Fs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=f})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),G(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eu),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Cr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ks);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Bs);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Jc),this.nodes.forEach(Yc),this.nodes.forEach(_c)):this.nodes.forEach(Bs),this.clearAllSnapshots();const a=N.now();k.delta=Z(0,1e3/60,a-k.timestamp),k.timestamp=a,k.isProcessing=!0,he.update.process(k),he.preRender.process(k),he.render.process(k),k.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,yn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Zc),this.sharedNodes.forEach(nu)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!I(this.snapshot.measuredBox.x)&&!I(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=R(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Pr(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;r&&this.instance&&(a||rt(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),ou(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var u;const{visualElement:r}=this.options;if(!r)return R();const a=r.measureViewportBox();if(!(((u=this.scroll)==null?void 0:u.wasRoot)||this.path.some(au))){const{scroll:c}=this.root;c&&(pt(a.x,c.offset.x),pt(a.y,c.offset.y))}return a}removeElementScroll(r){var l;const a=R();if(H(a,r),(l=this.scroll)!=null&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:h,options:d}=c;c!==this.root&&h&&d.layoutScroll&&(h.wasRoot&&H(a,r),pt(a.x,h.offset.x),pt(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const l=R();H(l,r);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&gt(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),rt(c.latestValues)&&gt(l,c.latestValues)}return rt(this.latestValues)&&gt(l,this.latestValues),l}removeTransform(r){const a=R();H(a,r);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!rt(u.latestValues))continue;We(u.latestValues)&&u.updateSnapshot();const c=R(),h=u.measurePageBox();H(c,h),Ms(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return rt(this.latestValues)&&Ms(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==k.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var d;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(d=this.parent)!=null&&d.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:h}=this.options;if(!(!this.layout||!(c||h))){if(this.resolvedRelativeTargetAt=k.timestamp,!this.targetDelta&&!this.relativeTarget){const f=this.getClosestProjectingParent();f&&f.layout&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),Rt(this.relativeTargetOrigin,this.layout.layoutBox,f.layout.layoutBox),H(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=R(),this.targetWithTransforms=R()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),hc(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):H(this.target,this.layout.layoutBox),gr(this.target,this.targetDelta)):H(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),Rt(this.relativeTargetOrigin,this.target,f.target),H(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||We(this.parent.latestValues)||pr(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var p;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||(p=this.parent)!=null&&p.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===k.timestamp&&(l=!1),l)return;const{layout:u,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||c))return;H(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,d=this.treeScale.y;dc(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=R());const{target:f}=r;if(!f){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(As(this.prevProjectionDelta.x,this.projectionDelta.x),As(this.prevProjectionDelta.y,this.projectionDelta.y)),Et(this.projectionDelta,this.layoutCorrected,f,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==d||!js(this.projectionDelta.x,this.prevProjectionDelta.x)||!js(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",f))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=mt(),this.projectionDelta=mt(),this.projectionDeltaWithTransform=mt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},h=mt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const d=R(),f=l?l.source:void 0,p=this.layout?this.layout.source:void 0,y=f!==p,x=this.getStack(),g=!x||x.members.length<=1,b=!!(y&&!g&&this.options.crossfade===!0&&!this.path.some(iu));this.animationProgress=0;let v;this.mixTargetDelta=C=>{const P=C/1e3;Is(h.x,r.x,P),Is(h.y,r.y,P),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Rt(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),su(this.relativeTarget,this.relativeTargetOrigin,d,P),v&&Hc(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=R()),H(v,this.relativeTarget)),y&&(this.animationValues=c,Fc(c,u,this.latestValues,P,b,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){var a,l,u;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(u=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||u.stop(),this.pendingAnimation&&(G(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{Qt.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=$(0)),this.currentAnimation=Rc(this.motionValue,[0,1e3],{...r,velocity:0,isSync:!0,onUpdate:c=>{this.mixTargetDelta(c),r.onUpdate&&r.onUpdate(c)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta($c),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=r;if(!(!a||!l||!u)){if(this!==r&&this.layout&&u&&Mr(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||R();const h=I(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const d=I(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+d}H(a,l),gt(a,c),Et(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new Kc),this.sharedNodes.get(r).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&Te("z",r,u,this.animationValues);for(let c=0;c<xe.length;c++)Te(`rotate${xe[c]}`,r,u,this.animationValues),Te(`skew${xe[c]}`,r,u,this.animationValues);r.render();for(const c in u)r.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);r.scheduleRender()}applyProjectionStyles(r,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){r.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,r.visibility="",r.opacity="",r.pointerEvents=Jt(a==null?void 0:a.pointerEvents)||"",r.transform=l?l(this.latestValues,""):"none";return}const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){this.options.layoutId&&(r.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,r.pointerEvents=Jt(a==null?void 0:a.pointerEvents)||""),this.hasProjected&&!rt(this.latestValues)&&(r.transform=l?l({},""):"none",this.hasProjected=!1);return}r.visibility="";const c=u.animationValues||u.latestValues;this.applyTransformsToTarget();let h=zc(this.projectionDeltaWithTransform,this.treeScale,c);l&&(h=l(c,h)),r.transform=h;const{x:d,y:f}=this.projectionDelta;r.transformOrigin=`${d.origin*100}% ${f.origin*100}% 0`,u.animationValues?r.opacity=u===this?c.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:c.opacityExit:r.opacity=u===this?c.opacity!==void 0?c.opacity:"":c.opacityExit!==void 0?c.opacityExit:0;for(const p in Ft){if(c[p]===void 0)continue;const{correct:y,applyTo:x,isCSSVariable:g}=Ft[p],b=h==="none"?c[p]:y(c[p],u);if(x){const v=x.length;for(let C=0;C<v;C++)r[x[C]]=b}else g?this.options.visualElement.renderState.vars[p]=b:r[p]=b}this.options.layoutId&&(r.pointerEvents=u===this?Jt(a==null?void 0:a.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(ks),this.root.sharedNodes.clear()}}}function Yc(t){t.updateLayout()}function _c(t){var n;const e=((n=t.resumeFrom)==null?void 0:n.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?K(h=>{const d=r?e.measuredBox[h]:e.layoutBox[h],f=I(d);d.min=s[h].min,d.max=d.min+f}):Mr(o,e.layoutBox,s)&&K(h=>{const d=r?e.measuredBox[h]:e.layoutBox[h],f=I(s[h]);d.max=d.min+f,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+f)});const a=mt();Et(a,s,e.layoutBox);const l=mt();r?Et(l,t.applyTransform(i,!0),e.measuredBox):Et(l,s,e.layoutBox);const u=!Pr(a);let c=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:d,layout:f}=h;if(d&&f){const p=R();Rt(p,e.layoutBox,d.layoutBox);const y=R();Rt(y,s,f.layoutBox),Ar(p,y)||(c=!0),h.options.layoutRoot&&(t.relativeTarget=y,t.relativeTargetOrigin=p,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeLayoutChanged:c})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function Xc(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function qc(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Zc(t){t.clearSnapshot()}function ks(t){t.clearMeasurements()}function Bs(t){t.isLayoutDirty=!1}function Jc(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Fs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Qc(t){t.resolveTargetDelta()}function tu(t){t.calcProjection()}function eu(t){t.resetSkewAndRotation()}function nu(t){t.removeLeadSnapshot()}function Is(t,e,n){t.translate=D(e.translate,0,n),t.scale=D(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Os(t,e,n,s){t.min=D(e.min,n.min,s),t.max=D(e.max,n.max,s)}function su(t,e,n,s){Os(t.x,e.x,n.x,s),Os(t.y,e.y,n.y,s)}function iu(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const ru={duration:.45,ease:[.4,0,.1,1]},Ns=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Ws=Ns("applewebkit/")&&!Ns("chrome/")?Math.round:W;function Us(t){t.min=Ws(t.min),t.max=Ws(t.max)}function ou(t){Us(t.x),Us(t.y)}function Mr(t,e,n){return t==="position"||t==="preserve-aspect"&&!uc(Ls(e),Ls(n),.2)}function au(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const lu=Vr({attachResizeListener:(t,e)=>Ot(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),be={current:void 0},Dr=Vr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!be.current){const t=new lu({});t.mount(window),t.setOptions({layoutScroll:!0}),be.current=t}return be.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),cu={pan:{Feature:Vc},drag:{Feature:Cc,ProjectionNode:Dr,MeasureLayout:br}};function Hs(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=s[i];o&&V.postRender(()=>o(e,Ht(e)))}class uu extends st{mount(){const{current:e}=this.node;e&&(this.unmount=La(e,(n,s)=>(Hs(this.node,s,"Start"),i=>Hs(this.node,i,"End"))))}unmount(){}}class hu extends st{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Nt(Ot(this.node.current,"focus",()=>this.onFocus()),Ot(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Ks(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=s[i];o&&V.postRender(()=>o(e,Ht(e)))}class du extends st{mount(){const{current:e}=this.node;e&&(this.unmount=Fa(e,(n,s)=>(Ks(this.node,s,"Start"),(i,{success:o})=>Ks(this.node,i,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ke=new WeakMap,we=new WeakMap,fu=t=>{const e=Ke.get(t.target);e&&e(t)},mu=t=>{t.forEach(fu)};function pu({root:t,...e}){const n=t||document;we.has(n)||we.set(n,{});const s=we.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(mu,{root:t,...e})),s[i]}function gu(t,e,n){const s=pu(e);return Ke.set(t,n),s.observe(t),()=>{Ke.delete(t),s.unobserve(t)}}const yu={some:0,all:1};class vu extends st{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:yu[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),d=u?c:h;d&&d(l)};return gu(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(xu(e,n))&&this.startObserver()}unmount(){}}function xu({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Tu={inView:{Feature:vu},tap:{Feature:du},focus:{Feature:hu},hover:{Feature:uu}},bu={layout:{ProjectionNode:Dr,MeasureLayout:br}},ze={current:null},Er={current:!1};function wu(){if(Er.current=!0,!!Ge)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ze.current=t.matches;t.addEventListener("change",e),e()}else ze.current=!1}const Su=new WeakMap;function Pu(t,e,n){for(const s in e){const i=e[s],o=n[s];if(B(i))t.addValue(s,i);else if(B(o))t.addValue(s,$(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,$(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const zs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Au{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=fn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=N.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,V.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=le(n),this.isVariantNode=Zi(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in h){const f=h[d];l[d]!==void 0&&B(f)&&f.set(l[d],!1)}}mount(e){this.current=e,Su.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Er.current||wu(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ze.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),G(this.notifyUpdate),G(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=wt.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&V.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in vt){const n=vt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):R()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<zs.length;s++){const i=zs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=Pu(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=$(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(si(s)||ri(s))?s=parseFloat(s):!Xa(s)&&nt.test(n)&&(s=Ui(e,n)),this.setBaseTarget(e,B(s)?s.get():s)),B(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var o;const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=Vn(this.props,n,(o=this.presenceContext)==null?void 0:o.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!B(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Je),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Rr extends Au{constructor(){super(...arguments),this.KeyframeResolver=Ma}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;B(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Lr(t,{style:e,vars:n},s,i){const o=t.style;let r;for(r in e)o[r]=e[r];i==null||i.applyProjectionStyles(o,s);for(r in n)o.setProperty(r,n[r])}function Cu(t){return window.getComputedStyle(t)}class Vu extends Rr{constructor(){super(...arguments),this.type="html",this.renderInstance=Lr}readValueFromInstance(e,n){var s;if(wt.has(n))return(s=this.projection)!=null&&s.isProjecting?Re(n):_o(e,n);{const i=Cu(e),o=(nn(n)?i.getPropertyValue(n):i[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(e,{transformPagePoint:n}){return yr(e,n)}build(e,n,s){Pn(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return Mn(e,n,s)}}const jr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Mu(t,e,n,s){Lr(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(jr.has(i)?i:Sn(i),e.attrs[i])}class Du extends Rr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=R}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(wt.has(n)){const s=Wi(n);return s&&s.default||0}return n=jr.has(n)?n:Sn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return ar(e,n,s)}build(e,n,s){sr(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){Mu(e,n,s,i)}mount(e){this.isSVGTag=rr(e.tagName),super.mount(e)}}const Eu=(t,e)=>Cn(t)?new Du(e):new Vu(e,{allowProjection:t!==T.Fragment}),Ru=El({...nc,...Tu,...cu,...bu},Eu),S=el(Ru),Lu=50,$s=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),ju=()=>({time:0,x:$s(),y:$s()}),ku={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Gs(t,e,n,s){const i=n[e],{length:o,position:r}=ku[e],a=i.current,l=n.time;i.current=t[`scroll${r}`],i.scrollLength=t[`scroll${o}`]-t[`client${o}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=yt(0,i.scrollLength,i.current);const u=s-l;i.velocity=u>Lu?0:Qe(i.current-a,u)}function Bu(t,e,n){Gs(t,"x",e,n),Gs(t,"y",e,n),e.time=n}function Fu(t,e){const n={x:0,y:0};let s=t;for(;s&&s!==e;)if(mn(s))n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if(s.tagName==="svg"){const i=s.getBoundingClientRect();s=s.parentElement;const o=s.getBoundingClientRect();n.x+=i.left-o.left,n.y+=i.top-o.top}else if(s instanceof SVGGraphicsElement){const{x:i,y:o}=s.getBBox();n.x+=i,n.y+=o;let r=null,a=s.parentNode;for(;!r;)a.tagName==="svg"&&(r=a),a=s.parentNode;s=r}else break;return n}const $e={start:0,center:.5,end:1};function Ys(t,e,n=0){let s=0;if(t in $e&&(t=$e[t]),typeof t=="string"){const i=parseFloat(t);t.endsWith("px")?s=i:t.endsWith("%")?t=i/100:t.endsWith("vw")?s=i/100*document.documentElement.clientWidth:t.endsWith("vh")?s=i/100*document.documentElement.clientHeight:t=i}return typeof t=="number"&&(s=e*t),n+s}const Iu=[0,0];function Ou(t,e,n,s){let i=Array.isArray(t)?t:Iu,o=0,r=0;return typeof t=="number"?i=[t,t]:typeof t=="string"&&(t=t.trim(),t.includes(" ")?i=t.split(" "):i=[t,$e[t]?t:"0"]),o=Ys(i[0],n,s),r=Ys(i[1],e),o-r}const Nu={All:[[0,0],[1,1]]},Wu={x:0,y:0};function Uu(t){return"getBBox"in t&&t.tagName!=="svg"?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}function Hu(t,e,n){const{offset:s=Nu.All}=n,{target:i=t,axis:o="y"}=n,r=o==="y"?"height":"width",a=i!==t?Fu(i,t):Wu,l=i===t?{width:t.scrollWidth,height:t.scrollHeight}:Uu(i),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=s.length;for(let d=0;d<h;d++){const f=Ou(s[d],u[r],l[r],a[o]);!c&&f!==e[o].interpolatorOffsets[d]&&(c=!0),e[o].offset[d]=f}c&&(e[o].interpolate=cn(e[o].offset,Vi(s),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=Z(0,1,e[o].interpolate(e[o].current))}function Ku(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!==t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}function zu(t,e,n,s={}){return{measure:i=>{Ku(t,s.target,n),Bu(t,n,i),(s.offset||s.target)&&Hu(t,n,s)},notify:()=>e(n)}}const Pt=new WeakMap,_s=new WeakMap,Se=new WeakMap,Xs=t=>t===document.scrollingElement?window:t;function kr(t,{container:e=document.scrollingElement,...n}={}){if(!e)return W;let s=Se.get(e);s||(s=new Set,Se.set(e,s));const i=ju(),o=zu(e,t,i,n);if(s.add(o),!Pt.has(e)){const a=()=>{for(const h of s)h.measure(k.timestamp);V.preUpdate(l)},l=()=>{for(const h of s)h.notify()},u=()=>V.read(a);Pt.set(e,u);const c=Xs(e);window.addEventListener("resize",u,{passive:!0}),e!==document.documentElement&&_s.set(e,$a(e,u)),c.addEventListener("scroll",u,{passive:!0}),u()}const r=Pt.get(e);return V.read(r,!1,!0),()=>{var u;G(r);const a=Se.get(e);if(!a||(a.delete(o),a.size))return;const l=Pt.get(e);Pt.delete(e),l&&(Xs(e).removeEventListener("scroll",l),(u=_s.get(e))==null||u(),window.removeEventListener("resize",l))}}const qs=new Map;function $u(t){const e={value:0},n=kr(s=>{e.value=s[t.axis].progress*100},t);return{currentTime:e,cancel:n}}function Br({source:t,container:e,...n}){const{axis:s}=n;t&&(e=t);const i=qs.get(e)??new Map;qs.set(e,i);const o=n.target??"self",r=i.get(o)??{},a=s+(n.offset??[]).join(",");return r[a]||(r[a]=!n.target&&Ri()?new ScrollTimeline({source:e,axis:s}):$u({container:e,...n})),r[a]}function Gu(t,e){const n=Br(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:s=>(s.pause(),_i(i=>{s.time=s.duration*i},n))})}function Yu(t){return t.length===2}function _u(t,e){return Yu(t)?kr(n=>{t(n[e.axis].progress,n)},e):_i(t,Br(e))}function Xu(t,{axis:e="y",container:n=document.scrollingElement,...s}={}){if(!n)return W;const i={axis:e,container:n,...s};return typeof t=="function"?_u(t,i):Gu(t,i)}const qu=()=>({scrollX:$(0),scrollY:$(0),scrollXProgress:$(0),scrollYProgress:$(0)}),Yt=t=>t?!t.current:!1;function Zu({container:t,target:e,...n}={}){const s=re(qu),i=T.useRef(null),o=T.useRef(!1),r=T.useCallback(()=>(i.current=Xu((a,{x:l,y:u})=>{s.scrollX.set(l.current),s.scrollXProgress.set(l.progress),s.scrollY.set(u.current),s.scrollYProgress.set(u.progress)},{...n,container:(t==null?void 0:t.current)||void 0,target:(e==null?void 0:e.current)||void 0}),()=>{var a;(a=i.current)==null||a.call(i)}),[t,e,JSON.stringify(n.offset)]);return Ye(()=>{if(o.current=!1,Yt(t)||Yt(e)){o.current=!0;return}else return r()},[r]),T.useEffect(()=>{if(o.current)return Lt(!Yt(t)),Lt(!Yt(e)),r()},[r]),s}function Ju(t){const e=re(()=>$(t)),{isStatic:n}=T.useContext(Tn);if(n){const[,s]=T.useState(t);T.useEffect(()=>e.on("change",s),[])}return e}function Fr(t,e){const n=Ju(e()),s=()=>n.set(e());return s(),Ye(()=>{const i=()=>V.preRender(s,!1,!0),o=t.map(r=>r.on("change",i));return()=>{o.forEach(r=>r()),G(s)}}),n}function Qu(t){Mt.current=[],t();const e=Fr(Mt.current,t);return Mt.current=void 0,e}function Zs(t,e,n,s){if(typeof t=="function")return Qu(t);const i=typeof e=="function"?e:Ya(e,n,s);return Array.isArray(t)?Js(t,i):Js([t],([o])=>i(o))}function Js(t,e){const n=re(()=>[]);return Fr(t,()=>{n.length=0;const s=t.length;for(let i=0;i<s;i++)n[i]=t[i].get();return e(n)})}const th="/assets/verticallineimg-Dmf81H1D.svg",eh=({image:t,name:e})=>{const n=ti(),s=Or(),i=()=>{const o=ei();if(!o){n("/auth");return}(o.role==="admin"?o.role:o.activeRole||o.role)==="buyer"?(s(Nr({section:"strategies",filters:{sport:e}})),n(`/content?sport=${encodeURIComponent(e)}`)):n("/auth")};return m.jsxs("div",{className:"sports-card-component sports-card",onClick:i,children:[m.jsx("div",{className:"sports-card-image",children:m.jsx("img",{src:t,alt:e})}),m.jsx("div",{className:"sports-card-overlay",children:m.jsx("h3",{className:"sports-card-name",children:e})})]})};function nh(t){return xt({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M13 21h-6a2 2 0 0 1 -2 -2v-6a2 2 0 0 1 2 -2h10"},child:[]},{tag:"path",attr:{d:"M11 16a1 1 0 1 0 2 0a1 1 0 0 0 -2 0"},child:[]},{tag:"path",attr:{d:"M8 11v-4a4 4 0 1 1 8 0v4"},child:[]},{tag:"path",attr:{d:"M21 15h-2.5a1.5 1.5 0 0 0 0 3h1a1.5 1.5 0 0 1 0 3h-2.5"},child:[]},{tag:"path",attr:{d:"M19 21v1m0 -8v1"},child:[]}]})(t)}function sh(t){return xt({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4"},child:[]},{tag:"path",attr:{d:"M13.5 6.5l4 4"},child:[]},{tag:"path",attr:{d:"M21 15h-2.5a1.5 1.5 0 0 0 0 3h1a1.5 1.5 0 0 1 0 3h-2.5"},child:[]},{tag:"path",attr:{d:"M19 21v1m0 -8v1"},child:[]}]})(t)}function ih(t){return xt({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M16.53 9.78a.75.75 0 0 0-1.06-1.06L11 13.19l-1.97-1.97a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0l5-5Z"},child:[]},{tag:"path",attr:{d:"m12.54.637 8.25 2.675A1.75 1.75 0 0 1 22 4.976V10c0 6.19-3.771 10.704-9.401 12.83a1.704 1.704 0 0 1-1.198 0C5.77 20.705 2 16.19 2 10V4.976c0-.758.489-1.43 1.21-1.664L11.46.637a1.748 1.748 0 0 1 1.08 0Zm-.617 1.426-8.25 2.676a.249.249 0 0 0-.173.237V10c0 5.46 3.28 9.483 8.43 11.426a.199.199 0 0 0 .14 0C17.22 19.483 20.5 15.461 20.5 10V4.976a.25.25 0 0 0-.173-.237l-8.25-2.676a.253.253 0 0 0-.154 0Z"},child:[]}]})(t)}function rh(t){return xt({attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M13 5.698a5 5 0 0 1-.904.525C11.022 6.711 9.573 7 8 7s-3.022-.289-4.096-.777A5 5 0 0 1 3 5.698V7c0 .374.356.875 1.318 1.313C5.234 8.729 6.536 9 8 9c.666 0 1.298-.056 1.876-.156-.43.31-.804.693-1.102 1.132A12 12 0 0 1 8 10c-1.573 0-3.022-.289-4.096-.777A5 5 0 0 1 3 8.698V10c0 .374.356.875 1.318 1.313C5.234 11.729 6.536 12 8 12h.027a4.6 4.6 0 0 0-.017.8A2 2 0 0 0 8 13c-1.573 0-3.022-.289-4.096-.777A5 5 0 0 1 3 11.698V13c0 .374.356.875 1.318 1.313C5.234 14.729 6.536 15 8 15c0 .363.097.704.266.997Q8.134 16.001 8 16c-1.573 0-3.022-.289-4.096-.777C2.875 14.755 2 14.007 2 13V4c0-1.007.875-1.755 1.904-2.223C4.978 1.289 6.427 1 8 1s3.022.289 4.096.777C13.125 2.245 14 2.993 14 4v4.256a4.5 4.5 0 0 0-1.753-.249C12.787 7.654 13 7.289 13 7zm-8.682-3.01C3.356 3.124 3 3.625 3 4c0 .374.356.875 1.318 1.313C5.234 5.729 6.536 6 8 6s2.766-.27 3.682-.687C12.644 4.875 13 4.373 13 4c0-.374-.356-.875-1.318-1.313C10.766 2.271 9.464 2 8 2s-2.766.27-3.682.687Z"},child:[]},{tag:"path",attr:{d:"M9 13a1 1 0 0 1 1-1v-1a2 2 0 1 1 4 0v1a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm3-3a1 1 0 0 0-1 1v1h2v-1a1 1 0 0 0-1-1"},child:[]}]})(t)}function oh(t){return xt({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M120.998 40.998v37.943C136.22 89.471 146 109.278 146 131.001c0 13.71-3.901 26.65-10.598 36.985 3.465 1.35 7.106 2.85 10.15 4.172l122.352-22.783 5.918 54.842-111.748 23.219c-.862 16.261-2.45 32.262-5.289 51.566h336.217V40.998zM96 88.998c-16.595 0-32.002 17.747-32.002 42.004 0 24.257 15.407 42.002 32.002 42.002 16.595 0 32.002-17.745 32.002-42.002S112.595 88.998 96 88.998zm156.096 81.629l-108.592 20.22c-14.24-5.602-4.956-3.035-21.469-8.517-7.476 5.469-16.33 8.672-26.035 8.672-8.6 0-16.53-2.523-23.428-6.9-8.59 3.564-17.655 8.09-25.736 12.654-12.992 7.338-23.722 13.211-27.838 16.033v130.213h20.004V232h17.996v263.002h30.004V326h17.996v169.002h26.004v-171.84l.154-.824c9.514-50.64 12.588-77.384 13.461-109.656l109.56-22.766zm-98.153 126.375c-.952 5.682-1.991 11.64-3.146 17.996H478v-17.996zM208 344.998c-16.595 0-32.002 17.747-32.002 42.004 0 18.198 8.67 32.73 20.01 38.855 3.599-1.662 7.482-2.706 11.68-2.851 4.633-.16 8.98.767 13.052 2.42 10.968-6.352 19.262-20.63 19.262-38.424 0-24.257-15.407-42.004-32.002-42.004zm112 0c-16.595 0-32.002 17.747-32.002 42.004 0 18.198 8.67 32.73 20.01 38.855 3.599-1.662 7.482-2.706 11.68-2.851 4.633-.16 8.98.767 13.052 2.42 10.968-6.352 19.262-20.63 19.262-38.424 0-24.257-15.407-42.004-32.002-42.004zm112 0c-16.595 0-32.002 17.747-32.002 42.004 0 18.198 8.67 32.73 20.01 38.855 3.599-1.662 7.482-2.706 11.68-2.851 4.633-.16 8.98.767 13.052 2.42 10.968-6.352 19.262-20.63 19.262-38.424 0-24.257-15.407-42.004-32.002-42.004zm-223.688 95.996c-3.844.133-8.907 2.93-14.3 8.785-5.394 5.855-10.696 14.25-15.125 22.76-4.226 8.12-7.609 16.16-10.06 22.463h85.339c-3.04-6.436-7.138-14.549-12.133-22.711-5.298-8.658-11.511-17.138-17.668-22.957-6.157-5.819-11.8-8.487-16.053-8.34zm112 0c-3.844.133-8.907 2.93-14.3 8.785-5.394 5.855-10.696 14.25-15.125 22.76-4.226 8.12-7.609 16.16-10.06 22.463h85.339c-3.04-6.436-7.138-14.549-12.133-22.711-5.298-8.658-11.511-17.138-17.668-22.957-6.157-5.819-11.8-8.487-16.052-8.34zm112 0c-3.844.133-8.907 2.93-14.3 8.785-5.394 5.855-10.696 14.25-15.125 22.76-4.226 8.12-7.609 16.16-10.06 22.463h85.339c-3.04-6.436-7.138-14.549-12.133-22.711-5.298-8.658-11.511-17.138-17.668-22.957-6.157-5.819-11.8-8.487-16.052-8.34z"},child:[]}]})(t)}function ah(t){return xt({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",strokeWidth:"2",d:"M2,7 L20,7 M16,2 L21,7 L16,12 M22,17 L4,17 M8,12 L3,17 L8,22"},child:[]}]})(t)}const lh="/assets/Football-D4Iq4SG-.jpg",ch="/assets/baseball-CLn4fO-x.avif",uh="/assets/Basketball-BQuyibjv.jpg",hh="/assets/soccer-Bto9vxJC.avif",dh=[{id:1,name:"Football",image:lh},{id:2,name:"Baseball",image:ch},{id:3,name:"Basketball",image:uh},{id:4,name:"Soccer",image:hh}],Pe={hidden:{opacity:0,y:60},visible:{opacity:1,y:0,transition:{duration:.8,ease:[.25,.46,.45,.94]}}},Qs={hidden:{opacity:0,x:-60},visible:{opacity:1,x:0,transition:{duration:.8,ease:[.25,.46,.45,.94]}}},fh={hidden:{opacity:0,x:60},visible:{opacity:1,x:0,transition:{duration:.8,ease:[.25,.46,.45,.94]}}},mh={hidden:{opacity:0,scale:.8},visible:{opacity:1,scale:1,transition:{duration:.6,ease:[.25,.46,.45,.94]}}},Y={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.1}}},Ph=()=>{const t=ti(),[e,n]=T.useState([]),[s,i]=T.useState(!0),o=T.useRef(null),{scrollYProgress:r}=Zu({target:o,offset:["start start","end start"]}),a=Zs(r,[0,1],["0%","50%"]),l=Zs(r,[0,1],[1,1.2]),u=dh.slice(0,4);T.useEffect(()=>{(async()=>{try{const d=await Hr({sortBy:"rating",limit:8});n(d.data.slice(0,8)),i(!1)}catch(d){console.error("Error fetching top rated strategies:",d),i(!1)}})()},[]);const c=h=>{h.preventDefault();const d=ei();if(!d){t("/auth");return}const f=d.role==="admin"?d.role:d.activeRole||d.role;t(f==="buyer"?"/content":f==="seller"?"/seller/my-sports-strategies":"/auth")};return m.jsxs("div",{className:"home-section",children:[m.jsx(S.section,{ref:o,className:"hero-section p-section",initial:"hidden",animate:"visible",variants:Y,children:m.jsxs("div",{className:"hero-container max-container",children:[m.jsxs(S.div,{className:"hero-content",variants:Y,children:[m.jsx(S.h1,{className:"hero-title mb-10",variants:{hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:1,ease:[.25,.46,.45,.94],staggerChildren:.1,delayChildren:.2}}},children:"Digital Sports Playbook Marketplace".split(" ").map((h,d)=>m.jsx(S.span,{variants:{hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.6}}},style:{display:"inline-block",marginRight:"0.3em"},children:h},d))}),m.jsx(S.p,{className:"hero-tagline mb-20",variants:Qs,transition:{delay:.8},children:'"Elevate Your Game - A Digital Exchange of Sports Strategies"'}),m.jsxs(S.div,{className:"hero-description mb-30",variants:Pe,transition:{delay:1},children:[m.jsx("p",{children:"Discover, Buy, and Sell Winning Sports Strategies."}),m.jsx("p",{children:"Join a secure, innovative marketplace dedicated to premium sports tactics, playbooks, videos, and more."})]}),m.jsx(S.div,{variants:mh,transition:{delay:1.2},whileHover:{scale:1.05,transition:{duration:.2}},whileTap:{scale:.95},children:m.jsx(Kt,{to:"/auth",className:"btn btn-primary",onClick:c,children:"Start Trading Strategies Today"})})]}),m.jsx(S.div,{className:"hero-background",style:{y:a,scale:l},initial:{opacity:0,x:100},animate:{opacity:1,x:0,transition:{duration:1.2,delay:.5,ease:[.25,.46,.45,.94]}},children:m.jsx(S.img,{src:Kr,alt:"Sports Equipment",whileHover:{scale:1.1,rotate:2,transition:{duration:.3}}})})]})}),m.jsx(S.section,{className:"sports-section p-section",initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:Y,children:m.jsxs("div",{className:"sports-container max-container",children:[m.jsx(S.div,{className:"sports-header mb-30",variants:Pe,children:m.jsx(S.h2,{className:"sports-title",variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8,ease:[.25,.46,.45,.94]}}},children:"Sports"})}),m.jsx(S.div,{className:"sports-cards-wrapper",variants:Y,children:m.jsx(S.div,{className:"sports-cards-container",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.2}}},children:u.map(h=>m.jsx(S.div,{variants:{hidden:{opacity:0,y:50,scale:.9},visible:{opacity:1,y:0,scale:1,transition:{duration:.6,ease:[.25,.46,.45,.94]}}},whileHover:{y:-10,scale:1.05,transition:{duration:.3}},children:m.jsx(eh,{image:h.image,name:h.name})},h.id))})})]})}),m.jsx(S.section,{className:"featured-section p-section",initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},variants:Y,children:m.jsxs("div",{className:"featured-container max-container",children:[m.jsxs(S.div,{className:"featured-header mb-30",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.3,delayChildren:.1}}},children:[m.jsx(S.h2,{className:"featured-title",variants:Qs,children:"Top Rated Sports Strategies"}),m.jsx(S.div,{variants:fh,children:m.jsx(Kt,{to:"/content",className:"featured-view-all",children:"Learn More Contents"})})]}),m.jsx(S.div,{className:"featured-grid",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.3}}},children:s?m.jsx(S.div,{variants:Pe,className:"loading-text",children:"Loading top rated strategies..."}):e.map((h,d)=>{var f;return m.jsx(S.div,{variants:{hidden:{opacity:0,y:60,rotateX:15},visible:{opacity:1,y:0,rotateX:0,transition:{duration:.8,ease:[.25,.46,.45,.94],delay:d*.1}}},whileHover:{y:-8,rotateY:5,scale:1.02,transition:{duration:.3}},children:m.jsx($r,{id:h._id,image:`http://localhost:5000${h.thumbnailUrl||h.fileUrl}`,title:h.title,coach:h.coachName,price:h.saleType==="Auction"&&((f=h.auctionDetails)!=null&&f.basePrice)?h.auctionDetails.basePrice:h.price,hasVideo:h.contentType==="Video",saleType:h.saleType,auctionDetails:h.auctionDetails})},h._id)})})]})}),m.jsx(S.section,{className:"mission-section p-section",initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:Y,children:m.jsxs("div",{className:"mission-container max-container",children:[m.jsx(S.div,{className:"mission-image",variants:{hidden:{opacity:0,x:-80,scale:.9},visible:{opacity:1,x:0,scale:1,transition:{duration:1,ease:[.25,.46,.45,.94]}}},whileHover:{scale:1.05,transition:{duration:.3}},children:m.jsx(S.img,{src:zr,alt:"Coaching Session",whileHover:{scale:1.1,transition:{duration:.3}}})}),m.jsxs(S.div,{className:"mission-content",variants:{hidden:{opacity:0,x:80},visible:{opacity:1,x:0,transition:{duration:1,ease:[.25,.46,.45,.94],staggerChildren:.2,delayChildren:.3}}},children:[m.jsx(S.h2,{className:"mission-title mb-20",variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8}}},children:"Our Mission"}),m.jsx(S.p,{className:"mission-description mb-30",variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8}}},children:"Build a digital marketplace where sports professionals, coaches, and enthusiasts can exchange sports strategies—videos, PDFs, playbooks, and custom requests."}),m.jsx(S.div,{variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8}}},whileHover:{scale:1.05,transition:{duration:.2}},whileTap:{scale:.95},children:m.jsx(Kt,{to:"/info",className:"btn-outline",onClick:c,children:"Start Trading Strategies Today"})})]})]})}),m.jsx(S.section,{className:"offer-join-section p-section",initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},variants:Y,children:m.jsxs("div",{className:"offer-join-container max-container",children:[m.jsxs(S.div,{className:"offer-column",variants:{hidden:{opacity:0,x:-60},visible:{opacity:1,x:0,transition:{duration:.8,ease:[.25,.46,.45,.94],staggerChildren:.15,delayChildren:.2}}},children:[m.jsx(S.h2,{className:"offer-title mb-20",variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8}}},children:"What We Offer"}),m.jsx(S.ul,{className:"offer-list",variants:Y,children:[{icon:nh,text:"Fixed-Price Listings And Bidding Options For Exclusive Sports Digital Content"},{icon:sh,text:"Buyer Requests For Tailored Playbook, Opponent Scouting Reports, Meal Plans Or Wellness Guides, etc."},{icon:ih,text:"Secure, Cloud-Based Hosting"},{icon:Gr,text:"Transparent Commission-Based Payments For Creators And The Platform"},{icon:rh,text:"Focus On Seller Credibility, Data Security, And High-Quality Content"}].map((h,d)=>m.jsxs(S.li,{className:"offer-item mb-20",variants:{hidden:{opacity:0,x:-30,y:20},visible:{opacity:1,x:0,y:0,transition:{duration:.6,ease:[.25,.46,.45,.94]}}},whileHover:{x:10,transition:{duration:.3}},children:[m.jsx(S.div,{className:"icon-container",whileHover:{scale:1.2,rotate:5,transition:{duration:.3}},children:m.jsx(h.icon,{className:"offer-icon"})}),m.jsx("div",{className:"offer-text",children:m.jsx("h3",{children:h.text})})]},d))})]}),m.jsx(S.div,{className:"vertical-line",variants:{hidden:{opacity:0,scaleY:0},visible:{opacity:1,scaleY:1,transition:{duration:1.2,ease:[.25,.46,.45,.94],delay:.5}}},children:m.jsx(S.img,{src:th,alt:"verticallineimg",style:{originY:0}})}),m.jsxs(S.div,{className:"join-column",variants:{hidden:{opacity:0,x:60},visible:{opacity:1,x:0,transition:{duration:.8,ease:[.25,.46,.45,.94],staggerChildren:.15,delayChildren:.4}}},children:[m.jsx(S.h2,{className:"join-title mb-20",variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8}}},children:"Why Join Our Marketplace?"}),m.jsx(S.ul,{className:"join-list",variants:Y,children:[{icon:oh,title:"Access Expert Strategies",desc:"Explore And Purchase Strategies Curated By Top Sports Minds"},{icon:ah,title:"Flexible Transactions",desc:"Choose Between Fixed Prices Or Competitive Bidding, With Options For Custom Content Requests"},{icon:Wr,title:"Secure & Protected",desc:"Scalable Cloud Hosting And Verified Sellers Ensure Safety And Trust"},{icon:Ur,title:"Fair & Transparent",desc:"Clear Fee Structures And Secure Payment Processing Support A Trustworthy Environment"}].map((h,d)=>m.jsxs(S.li,{className:"join-item mb-20",variants:{hidden:{opacity:0,x:30,y:20},visible:{opacity:1,x:0,y:0,transition:{duration:.6,ease:[.25,.46,.45,.94]}}},whileHover:{x:-10,transition:{duration:.3}},children:[m.jsx(S.div,{className:"icon-container",whileHover:{scale:1.2,rotate:-5,transition:{duration:.3}},children:m.jsx(h.icon,{className:"join-icon"})}),m.jsxs("div",{className:"join-text",children:[m.jsx("h3",{children:h.title}),m.jsx("p",{children:h.desc})]})]},d))})]})]})}),m.jsx(S.section,{className:"cta-section p-section",initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:Y,children:m.jsxs(S.div,{className:"cta-container max-container",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.3,delayChildren:.2}}},children:[m.jsx(S.h2,{className:"cta-title mb-10",variants:{hidden:{opacity:0,y:50,scale:.9},visible:{opacity:1,y:0,scale:1,transition:{duration:.8,ease:[.25,.46,.45,.94]}}},children:"Ready to Elevate Your Game?"}),m.jsx(S.p,{className:"cta-description mb-25",variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.8,ease:[.25,.46,.45,.94]}}},children:"Join our platform now and start trading or requesting winning sports strategies!"}),m.jsx(S.div,{variants:{hidden:{opacity:0,y:30,scale:.8},visible:{opacity:1,y:0,scale:1,transition:{duration:.8,ease:[.25,.46,.45,.94]}}},whileHover:{scale:1.1,y:-5,transition:{duration:.3}},whileTap:{scale:.95},children:m.jsx(Kt,{to:"/auth",className:"btn btn-primary mt-20",onClick:c,children:"Join The Strategy Exchange"})})]})})]})};export{Ph as default};
