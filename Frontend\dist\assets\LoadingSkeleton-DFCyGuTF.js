import{j as s}from"./index-ctFdmWBt.js";/* empty css                        */const e=({variant:t="rectangular",width:n="100%",height:a="20px",className:r="",count:c=1,animation:x="pulse"})=>{const o=Array.from({length:c},(j,d)=>s.jsx("div",{className:`skeleton skeleton--${t} skeleton--${x} ${r}`,style:{width:n,height:a}},d));return c===1?o[0]:s.jsx("div",{className:"skeleton-group",children:o})},l=({columns:t=7})=>s.jsx("div",{className:"table-row-skeleton",children:Array.from({length:t},(n,a)=>s.jsx(e,{height:"40px",className:"table-cell-skeleton"},a))}),h=()=>s.jsxs("div",{className:"card-skeleton",children:[s.jsx(e,{variant:"rectangular",height:"200px",className:"card-image-skeleton"}),s.jsxs("div",{className:"card-content-skeleton",children:[s.jsx(e,{height:"20px",width:"80%",className:"card-title-skeleton"}),s.jsx(e,{height:"16px",width:"60%",className:"card-subtitle-skeleton"}),s.jsx(e,{height:"24px",width:"40%",className:"card-price-skeleton"})]})]}),i=()=>s.jsxs("div",{className:"stat-card-skeleton",children:[s.jsx(e,{variant:"circular",width:"60px",height:"60px",className:"stat-icon-skeleton"}),s.jsxs("div",{className:"stat-content-skeleton",children:[s.jsx(e,{height:"32px",width:"60px",className:"stat-number-skeleton"}),s.jsx(e,{height:"16px",width:"100px",className:"stat-label-skeleton"})]})]}),p=()=>s.jsxs("div",{className:"dashboard-skeleton",children:[s.jsxs("div",{className:"stats-skeleton",children:[s.jsx(i,{}),s.jsx(i,{}),s.jsx(i,{})]}),s.jsxs("div",{className:"section-skeleton",children:[s.jsxs("div",{className:"section-header-skeleton",children:[s.jsx(e,{height:"24px",width:"200px"}),s.jsx(e,{height:"16px",width:"100px"})]}),s.jsxs("div",{className:"table-skeleton",children:[s.jsx(l,{}),s.jsx(l,{})]})]}),s.jsxs("div",{className:"section-skeleton",children:[s.jsxs("div",{className:"section-header-skeleton",children:[s.jsx(e,{height:"24px",width:"180px"}),s.jsx(e,{height:"16px",width:"120px"})]}),s.jsxs("div",{className:"table-skeleton",children:[s.jsx(l,{}),s.jsx(l,{})]})]})]}),N=({count:t=8})=>s.jsx("div",{className:"strategies-grid-skeleton",children:Array.from({length:t},(n,a)=>s.jsx(h,{},a))});export{p as D,e as L,N as S,l as T,i as a};
