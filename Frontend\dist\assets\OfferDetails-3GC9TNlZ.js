import{a as U,c as T,b as q,d as Y,r as l,aT as x,j as e,P as z,e as G,f as E,aB as H,aU as J,N as $}from"./index-ctFdmWBt.js";import{S as c}from"./SellerLayout-EbrVdrvL.js";import{L as K}from"./LoadingSkeleton-DFCyGuTF.js";import{f as Q}from"./dateValidation-cL5kH0gD.js";/* empty css                        */import"./timezoneUtils-BuH33ask.js";const te=()=>{var p,O,N,g,D,v,b,y,R,S,C,A;const{id:d}=U(),k=T(),i=q(),{sellerOffers:r,isLoading:I,isError:M,error:o}=Y(a=>a.offer),[s,w]=l.useState(null),[n,u]=l.useState(""),[B,f]=l.useState(!1),[t,V]=l.useState(""),[m,_]=l.useState(!1);l.useEffect(()=>{(!r||r.length===0)&&i(x())},[i,r]),l.useEffect(()=>{if(r&&r.length>0&&d){const a=r.find(j=>j._id===d);w(a)}},[r,d]);const F=a=>Q(a),h=a=>`$${parseFloat(a).toFixed(2)}`,L=a=>{const j={Pending:"status-pending",Accepted:"status-accepted",Rejected:"status-rejected",Cancelled:"status-cancelled",Expired:"status-expired"};return e.jsx("span",{className:`status-badge ${j[a]||""}`,children:a})},P=async()=>{if(s){_(!0);try{await i(J({offerId:s._id,status:t,sellerResponse:n.trim()})).unwrap(),$.success(`Offer ${t} successfully`),i(x()),f(!1),u("")}catch(a){$.error(a.message||`Failed to ${t} offer`)}finally{_(!1)}}};return I&&!s?e.jsx(c,{children:e.jsx("div",{className:"OfferDetails",children:e.jsx(K,{type:"table",rows:1})})}):M?e.jsx(c,{children:e.jsx("div",{className:"OfferDetails",children:e.jsx(z,{title:"Error Loading Offer Details",message:(o==null?void 0:o.message)||"Failed to load offer details",onRetry:()=>i(x())})})}):s?e.jsx(c,{children:e.jsxs("div",{className:"OfferDetails",children:[e.jsx("div",{className:"OfferDetails__content",children:e.jsxs("div",{className:"OfferDetails__main-section",children:[e.jsx("div",{className:"OfferDetails__content-card",children:e.jsxs("div",{className:"OfferDetails__content-info",children:[e.jsx("img",{src:(p=s.content)!=null&&p.thumbnailUrl?G(s.content.thumbnailUrl):E(200,120,"No image"),alt:((O=s.content)==null?void 0:O.title)||"Content",className:"OfferDetails__content-image",onError:a=>{a.target.src=E(200,120,"Image not found")}}),e.jsxs("div",{className:"OfferDetails__content-details",children:[e.jsx("h3",{className:"OfferDetails__content-title",children:((N=s.content)==null?void 0:N.title)||"Untitled Content"}),e.jsx("p",{className:"OfferDetails__content-sport",children:((g=s.content)==null?void 0:g.sport)||"Sports Content"}),e.jsxs("p",{className:"OfferDetails__content-price",children:["Listed Price: ",h(((D=s.content)==null?void 0:D.price)||0)]})]})]})}),e.jsxs("div",{className:"OfferDetails__offer-card",children:[e.jsx("h4",{children:"Offer Information"}),e.jsxs("div",{className:"OfferDetails__info-grid",children:[e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Offer ID:"}),e.jsxs("span",{children:["#",(v=s._id)==null?void 0:v.substring(0,8)]})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Offer Amount:"}),e.jsx("span",{className:"OfferDetails__amount",children:h(s.amount)})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Date Received:"}),e.jsx("span",{children:F(s.createdAt)})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Status:"}),L(s.status)]})]})]}),e.jsxs("div",{className:"OfferDetails__buyer-card",children:[e.jsx("h4",{children:"Buyer Information"}),e.jsxs("div",{className:"OfferDetails__buyer-info",children:[e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Name:"}),e.jsx("span",{children:`${((b=s.buyer)==null?void 0:b.firstName)||""} ${((y=s.buyer)==null?void 0:y.lastName)||""}`.trim()||"Unknown Buyer"})]}),e.jsxs("div",{className:"OfferDetails__info-item",children:[e.jsx("label",{children:"Email:"}),e.jsx("span",{children:((R=s.buyer)==null?void 0:R.email)||"N/A"})]})]})]}),s.message&&e.jsxs("div",{className:"OfferDetails__message-card",children:[e.jsx("h4",{children:"Buyer's Message"}),e.jsx("div",{className:"OfferDetails__message-content",children:s.message})]}),s.sellerResponse&&e.jsxs("div",{className:"OfferDetails__response-card",children:[e.jsx("h4",{children:"Your Response"}),e.jsx("div",{className:"OfferDetails__response-content",children:s.sellerResponse})]})]})}),B&&e.jsx("div",{className:"OfferDetails__modal-overlay",children:e.jsxs("div",{className:"OfferDetails__response-modal",children:[e.jsxs("div",{className:"OfferDetails__modal-header",children:[e.jsxs("h3",{children:[t==="accepted"?"Accept":"Reject"," Offer"]}),e.jsx("button",{className:"OfferDetails__modal-close",onClick:()=>f(!1),children:e.jsx(H,{})})]}),e.jsxs("div",{className:"OfferDetails__modal-body",children:[e.jsxs("div",{className:"OfferDetails__offer-summary",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Content:"})," ",(S=s.content)==null?void 0:S.title]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Buyer:"})," ",(C=s.buyer)==null?void 0:C.firstName," ",(A=s.buyer)==null?void 0:A.lastName]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Offer Amount:"})," ",h(s.amount)]})]}),e.jsxs("div",{className:"OfferDetails__response-input",children:[e.jsxs("label",{htmlFor:"responseMessage",children:["Response Message ",t==="rejected"?"(Required)":"(Optional)"]}),e.jsx("textarea",{id:"responseMessage",value:n,onChange:a=>u(a.target.value),placeholder:`Add a ${t==="accepted"?"thank you":"reason for rejection"} message...`,rows:4,maxLength:500,required:t==="rejected"}),e.jsxs("small",{children:[n.length,"/500 characters"]})]})]}),e.jsxs("div",{className:"OfferDetails__modal-actions",children:[e.jsx("button",{className:"btn-outline",onClick:()=>f(!1),disabled:m,children:"Cancel"}),e.jsx("button",{className:`btn-primary ${t==="rejected"?"btn-danger":""}`,onClick:P,disabled:m||t==="rejected"&&!n.trim(),children:m?"Processing...":`${t==="accepted"?"Accept":"Reject"} Offer`})]})]})})]})}):e.jsx(c,{children:e.jsx("div",{className:"OfferDetails",children:e.jsxs("div",{className:"OfferDetails__error",children:[e.jsx("h3",{children:"Offer Not Found"}),e.jsx("p",{children:"The offer you're looking for could not be found."}),e.jsx("button",{className:"btn-primary",onClick:()=>k("/seller/offers"),children:"Back to Offers"})]})})})};export{te as default};
