import{c as K,A as M,b as F,d as W,r as c,t as l,j as o,L as Y,o as V,B as G,C as H,v as J}from"./index-ctFdmWBt.js";const _=()=>{var E,k,L,C,I;const v=K(),f=M(),y=F(),{isLoading:p,isError:Q,isSuccess:U,error:X}=W(s=>s.auth),[m,O]=c.useState(["","","","","",""]),i=c.useRef([]),[w,u]=c.useState(""),[d,P]=c.useState(0),[h,T]=c.useState(!0),[b,S]=c.useState(null),g=(E=f.state)==null?void 0:E.userId,N=((k=f.state)==null?void 0:k.phoneNumber)||"+91 (*************",R=((L=f.state)==null?void 0:L.cooldownSeconds)||60,D=((C=f.state)==null?void 0:C.isLogin)||!1,j=(I=f.state)==null?void 0:I.developmentOtp;c.useEffect(()=>{i.current[0]&&i.current[0].focus(),g||(l.error("Invalid access. Please try again."),v(D?"/auth":"/signup")),R>0&&(P(R),T(!1))},[]),c.useEffect(()=>{let s;return d>0?s=setTimeout(()=>{P(d-1)},1e3):d===0&&!h&&T(!0),()=>clearTimeout(s)},[d,h]),c.useEffect(()=>{j&&S(j)},[g,j]);const $=(s,e)=>{const t=s.target.value;if(!/^\d*$/.test(t))return;const r=[...m];if(r[e]=t.slice(-1),O(r),w&&u(""),t&&e<5&&i.current[e+1].focus(),t&&e===5){const n=[...r];n[e]=t.slice(-1),n.every(a=>a!=="")&&setTimeout(()=>{x(n)},100)}},A=(s,e)=>{if(s.key==="Backspace"&&!m[e]&&e>0){const t=[...m];t[e-1]="",O(t),i.current[e-1].focus()}s.key==="ArrowLeft"&&e>0&&i.current[e-1].focus(),s.key==="ArrowRight"&&e<5&&i.current[e+1].focus()},B=s=>{s.preventDefault();const e=s.clipboardData.getData("text").trim();if(/^\d{6}$/.test(e)){const t=e.split("");O(t),i.current[5].focus(),setTimeout(()=>{x(t)},100)}},q=async()=>{var s;if(!(!h||p)){O(["","","","","",""]),u(""),y(V()),i.current[0].focus();try{let e;g&&!D?e={userId:g}:e={mobile:N.replace(/\s+/g,"").replace(/[()]/g,"")};const t=await y(G(e)).unwrap();l.otp.success("OTP resent successfully!");const r=t.cooldownSeconds||60;P(r),T(!1),t.developmentOtp&&S(t.developmentOtp)}catch(e){console.error("Resend OTP error:",e);let t="Failed to resend OTP. Please try again.";if(typeof e=="string"?t=e:e!=null&&e.message&&(t=e.message),t.includes("wait")&&t.includes("seconds")){const r=((s=t.match(/\d+/))==null?void 0:s[0])||60;l.otp.cooldown(parseInt(r)),P(parseInt(r)),T(!1)}else l.error(t)}}},x=async(s=null)=>{var r;const e=s||m;if(e.some(n=>n==="")){u("Please enter the complete OTP");return}if(p)return;const t=e.join("");u(""),y(V());try{const n=await y(H({userId:g,otp:t})).unwrap();l.otp.verificationSuccess();const a=((r=n.user)==null?void 0:r.role)||"buyer";if(a==="seller"){const z=J(n.user);v(z)}else v(a==="admin"?"/admin/dashboard":"/content")}catch(n){console.error("OTP verification error:",n);let a="Verification failed. Please try again.";typeof n=="string"?a=n:n!=null&&n.message&&(a=n.message),a.includes("Invalid OTP")?(u("Invalid OTP. Please try again."),l.otp.verificationError()):a.includes("expired")?(u("OTP has expired. Please request a new one."),l.error("OTP has expired. Please request a new one.")):(u(a),l.error(a))}};return o.jsx("div",{className:"otp-page otp-container",children:o.jsxs("div",{className:"otp-form-container",children:[o.jsx("h1",{className:"otp-title",children:"OTP Verification"}),o.jsx("p",{className:"otp-instruction",children:b?"Enter The OTP Displayed Below":`Enter The OTP Sent To ${N}`}),b&&o.jsxs("div",{className:"development-otp-display",style:{background:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"8px",padding:"12px",margin:"16px 0",textAlign:"center"},children:[o.jsx("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#856404"},children:o.jsx("strong",{children:"Your OTP:"})}),o.jsx("p",{style:{margin:"0",fontSize:"24px",fontWeight:"bold",color:"#856404",letterSpacing:"4px"},children:b})]}),o.jsx("div",{className:"otp-input-group",children:m.map((s,e)=>o.jsx("input",{type:"text",maxLength:1,value:s,onChange:t=>$(t,e),onKeyDown:t=>A(t,e),onPaste:e===0?B:null,ref:t=>i.current[e]=t,className:"otp-input","aria-label":`Digit ${e+1} of OTP`},e))}),w&&o.jsx("p",{className:"otp-error",children:w}),o.jsxs("div",{className:"otp-resend",children:[o.jsx("span",{children:"Didn't Received OTP? "}),o.jsx("button",{onClick:q,className:"resend-button",disabled:!h||p,children:!h&&d>0?`Resend in ${d}s`:p?"Sending...":"Resend"})]}),o.jsx("button",{onClick:x,className:"verify-button btn-primary",disabled:p,children:p?"Verifying...":"Verify"}),o.jsx("div",{className:"back-to-signin",children:o.jsx(Y,{to:"/auth",children:"Back To Sign In"})})]})})};export{_ as default};
