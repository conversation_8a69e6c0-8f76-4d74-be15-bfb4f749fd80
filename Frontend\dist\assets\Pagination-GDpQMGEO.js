import{n as R,j as a}from"./index-ctFdmWBt.js";var u={exports:{}},l,y;function b(){if(y)return l;y=1;var r="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return l=r,l}var m,f;function v(){if(f)return m;f=1;var r=b();function n(){}function s(){}return s.resetWarningCache=n,m=function(){function e(o,i,q,N,_,d){if(d!==r){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}e.isRequired=e;function t(){return e}var p={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:n};return p.PropTypes=p,p},m}var T;function x(){return T||(T=1,u.exports=v()()),u.exports}var S=x();const c=R(S),j=({currentPage:r,totalPages:n,onPageChange:s,isLoading:e=!1,className:t=""})=>{const p=()=>{const o=[];o.push(a.jsx("button",{className:"pagination-arrow",onClick:()=>r>1&&s(r-1),disabled:r===1||e,children:"<"},"prev"));for(let i=1;i<=n;i++)i===1||i===n||i>=r-1&&i<=r+1?o.push(a.jsx("button",{className:`pagination-item ${r===i?"active":""}`,onClick:()=>s(i),disabled:e,children:i},i)):(i===r-2||i===r+2)&&o.push(a.jsx("span",{className:"pagination-ellipsis",children:"..."},`ellipsis-${i}`));return o.push(a.jsx("button",{className:"pagination-arrow",onClick:()=>r<n&&s(r+1),disabled:r===n||e,children:">"},"next")),o};return n<=1?null:a.jsx("div",{className:`pagination ${t}`.trim(),children:p()})};j.propTypes={currentPage:c.number.isRequired,totalPages:c.number.isRequired,onPageChange:c.func.isRequired,isLoading:c.bool,className:c.string};export{j as P};
