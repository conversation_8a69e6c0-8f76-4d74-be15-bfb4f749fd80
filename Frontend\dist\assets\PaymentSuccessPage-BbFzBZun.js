import{a as S,c as A,A as B,b as O,d as b,r as v,N as g,O as R,j as e,P as w}from"./index-ctFdmWBt.js";import{L as T}from"./LoadingSkeleton-DFCyGuTF.js";import{f as F}from"./dateValidation-cL5kH0gD.js";/* empty css                        */import"./timezoneUtils-BuH33ask.js";const V=()=>{var h,m,u,x,j,p,f,N,y;const{orderId:t}=S(),a=A(),c=B(),r=O(),{user:n}=b(i=>i.auth),{order:o,isLoading:D,error:l}=b(i=>i.order),[d,I]=v.useState(!1);(h=c.state)==null||h.paymentResult;const C=(m=c.state)==null?void 0:m.order;v.useEffect(()=>{if(!n){g.error("Please log in to view your order"),a("/login");return}t&&!o&&r(R(t))},[r,t,n,a,o]);const s=o||C,k=async()=>{g.error("Download functionality has been disabled for security purposes")},L=()=>{a("/buyer/downloads")},P=()=>{a("/buyer/dashboard")};return D?e.jsx(T,{type:"payment-success"}):l||!s?e.jsx(w,{title:"Order Not Found",message:l||"Unable to find order information.",onRetry:()=>a("/buyer/dashboard"),retryText:"Go to Dashboard"}):s.buyer._id!==n.id&&s.buyer!==n.id?e.jsx(w,{title:"Access Denied",message:"You don't have permission to view this order.",onRetry:()=>a("/buyer/dashboard"),retryText:"Go to Dashboard"}):e.jsx("div",{className:"payment-success-page",children:e.jsx("div",{className:"max-container",children:e.jsxs("div",{className:"success-content",children:[e.jsxs("div",{className:"success-header",children:[e.jsx("div",{className:"success-icon-large",children:e.jsxs("svg",{width:"80",height:"80",viewBox:"0 0 80 80",fill:"none",children:[e.jsx("circle",{cx:"40",cy:"40",r:"40",fill:"#10B981"}),e.jsx("path",{d:"M25 40L35 50L55 30",stroke:"white",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("h1",{className:"success-title",children:"Payment Successful!"}),e.jsx("p",{className:"success-subtitle",children:"Thank you for your purchase. Your payment has been processed successfully."})]}),e.jsxs("div",{className:"order-details-card",children:[e.jsx("h2",{className:"card-title",children:"Order Details"}),e.jsxs("div",{className:"order-info-grid",children:[e.jsxs("div",{className:"info-item",children:[e.jsx("span",{className:"info-label",children:"Order ID"}),e.jsxs("span",{className:"info-value",children:["#",(u=s._id)==null?void 0:u.slice(-8).toUpperCase()]})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("span",{className:"info-label",children:"Date"}),e.jsx("span",{className:"info-value",children:F(s.createdAt)})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("span",{className:"info-label",children:"Amount Paid"}),e.jsxs("span",{className:"info-value",children:["$",(x=s.amount)==null?void 0:x.toFixed(2)]})]}),e.jsxs("div",{className:"info-item",children:[e.jsx("span",{className:"info-label",children:"Payment Status"}),e.jsx("span",{className:"info-value status-paid",children:"Completed"})]})]}),e.jsxs("div",{className:"content-details",children:[e.jsx("h3",{className:"content-title",children:"Purchased Content"}),e.jsxs("div",{className:"content-item",children:[e.jsx("div",{className:"content-image",children:e.jsx("img",{src:((j=s.content)==null?void 0:j.thumbnailUrl)||"https://via.placeholder.com/100x100/f5f5f5/666666?text=IMG",alt:((p=s.content)==null?void 0:p.title)||"Content",className:"content-thumbnail"})}),e.jsxs("div",{className:"content-info",children:[e.jsx("h4",{className:"content-name",children:((f=s.content)==null?void 0:f.title)||"Content Title"}),e.jsxs("p",{className:"content-coach",children:["By ",((N=s.content)==null?void 0:N.coachName)||"Coach"]}),e.jsx("p",{className:"content-type",children:((y=s.content)==null?void 0:y.contentType)||"Digital Content"})]})]})]})]}),e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{className:"btn-primary download-btn",onClick:k,disabled:d,children:d?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"spinner"}),"Downloading..."]}):e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})}),"Download Content"]})}),e.jsx("button",{className:"btn-secondary",onClick:L,children:"View All Downloads"}),e.jsx("button",{className:"btn-outline",onClick:P,children:"Back to Dashboard"})]}),e.jsxs("div",{className:"additional-info",children:[e.jsxs("div",{className:"info-card",children:[e.jsx("h3",{children:"What's Next?"}),e.jsxs("ul",{children:[e.jsx("li",{children:"Your content is now available for download"}),e.jsx("li",{children:"You can access it anytime from your Downloads page"}),e.jsx("li",{children:"A receipt has been sent to your email"}),e.jsx("li",{children:"Contact support if you have any issues"})]})]}),e.jsxs("div",{className:"info-card",children:[e.jsx("h3",{children:"Need Help?"}),e.jsx("p",{children:"If you have any questions about your purchase or need assistance, please don't hesitate to contact our support team."}),e.jsx("button",{className:"btn-link",children:"Contact Support"})]})]})]})})})};export{V as default};
