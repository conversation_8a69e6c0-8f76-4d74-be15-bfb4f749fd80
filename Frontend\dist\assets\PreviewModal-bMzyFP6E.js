import{r as l,j as e,cf as N,cg as k,aB as g}from"./index-ctFdmWBt.js";import{S as L}from"./SimplePDFViewer-RiyMKPYj.js";const F=o=>{l.useEffect(()=>{if(o){const t=window.scrollY;return document.body.style.overflow="hidden",document.body.style.position="fixed",document.body.style.top=`-${t}px`,document.body.style.width="100%",document.body.style.left="0",document.body.classList.add("modal-open"),document.body.setAttribute("data-scroll-y",t.toString()),()=>{const s=document.body.getAttribute("data-scroll-y");document.body.style.overflow="",document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.left="",document.body.classList.remove("modal-open"),document.body.removeAttribute("data-scroll-y"),s&&window.scrollTo(0,parseInt(s,10))}}},[o])},S=({isOpen:o,onClose:t,fileUrl:s,fileName:u="",title:v="Preview",contentType:a="document",className:x=""})=>{const[d,h]=l.useState(!1),[y,n]=l.useState(!0),[w,c]=l.useState(!1);F(o);const m=l.useCallback(()=>{var p;if((a==null?void 0:a.toLowerCase())==="video")return!0;if(!s)return!1;const r=["mp4","avi","mov","wmv","flv","webm","mkv"],E=(p=s.split(".").pop())==null?void 0:p.toLowerCase();return r.includes(E)},[a,s]);l.useEffect(()=>{if(o&&s&&(n(!0),c(!1),!m())){const r=setTimeout(()=>{n(!1)},1e3);return()=>clearTimeout(r)}},[o,s,m]);const i=l.useCallback(r=>{r.key==="Escape"&&t()},[t]);l.useEffect(()=>(o?document.addEventListener("keydown",i):document.removeEventListener("keydown",i),()=>{document.removeEventListener("keydown",i)}),[o,i]);const b=r=>{r.target===r.currentTarget&&t()},f=()=>{h(!d)},_=()=>{n(!1),c(!1)},j=()=>{n(!1),c(!0)};return o?e.jsx("div",{className:`preview-modal-overlay ${x}`,onClick:b,children:e.jsxs("div",{className:`preview-modal ${d?"preview-modal--fullscreen":""}`,children:[e.jsxs("div",{className:"preview-modal__header",children:[e.jsxs("div",{className:"preview-modal__title-section",children:[e.jsx("h3",{className:"preview-modal__title",children:v}),u&&e.jsx("span",{className:"preview-modal__filename",children:u})]}),e.jsxs("div",{className:"preview-modal__controls",children:[e.jsx("button",{className:"preview-modal__control-btn",onClick:f,title:d?"Exit Fullscreen":"Enter Fullscreen",children:d?e.jsx(N,{}):e.jsx(k,{})}),e.jsx("button",{className:"preview-modal__close-btn",onClick:t,title:"Close Preview",children:e.jsx(g,{})})]})]}),e.jsxs("div",{className:"preview-modal__content",children:[y&&e.jsxs("div",{className:"preview-modal__loading",children:[e.jsx("div",{className:"preview-modal__spinner"}),e.jsx("p",{children:"Loading preview..."})]}),w&&e.jsxs("div",{className:"preview-modal__error",children:[e.jsx("p",{children:"Unable to load preview"}),e.jsx("button",{className:"btn-outline",onClick:t,children:"Close"})]}),!w&&e.jsx(e.Fragment,{children:m()?e.jsx("div",{className:"preview-modal__video-container",children:e.jsxs("video",{className:"preview-modal__video",controls:!0,autoPlay:!1,controlsList:"nodownload noremoteplayback",disablePictureInPicture:!0,onLoadedData:_,onError:j,children:[e.jsx("source",{src:s,type:"video/mp4"}),"Your browser does not support the video tag."]})}):e.jsx("div",{className:"preview-modal__document-container",children:e.jsx(L,{fileUrl:s,title:v,className:"preview-modal__document-viewer",height:"100%",showDownload:!1,showFallbackOptions:!0})})})]})]})}):null};export{S as P,F as u};
