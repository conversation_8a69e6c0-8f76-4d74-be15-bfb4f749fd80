import{r as x,j as s,ay as N,az as S,aA as v}from"./index-ctFdmWBt.js";const E=({rating:g,size:c=24,interactive:r=!1,onChange:d})=>{const[o,u]=x.useState(null),f=Math.min(5,Math.max(0,Number(g)||0)),p=t=>{const l=(o!==null?o:f)-t;return l>=0?s.jsx(N,{size:c,className:"star-icon filled"}):l>=-.5?s.jsx(S,{size:c,className:"star-icon filled"}):s.jsx(v,{size:c,className:"star-icon outline"})},h=(t,e)=>{if(!r||!d)return;const n=t.currentTarget.getBoundingClientRect(),i=n.width,m=(t.clientX-n.left)/i;let a=e+1;m<=.5&&(a-=.5),d(a),u(null)},M=(t,e)=>{if(!r)return;const n=t.currentTarget.getBoundingClientRect(),i=n.width,m=(t.clientX-n.left)/i;let a=e+1;m<=.5&&(a-=.5),u(a)},R=()=>{u(null)};return s.jsxs("div",{className:"rating-stars",onMouseLeave:R,children:[[0,1,2,3,4].map(t=>s.jsx("div",{onClick:e=>h(e,t),onMouseMove:e=>M(e,t),className:r?"star-wrapper interactive":"star-wrapper",style:{cursor:r?"pointer":"default"},children:p(t+1)},t)),r&&s.jsxs("div",{className:"rating-tooltip",children:[o!==null?o:f," out of 5"]})]})};export{E as R};
