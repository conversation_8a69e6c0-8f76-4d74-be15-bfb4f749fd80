import{c as l,j as e}from"./index-ctFdmWBt.js";import{T as c}from"./thankyou-hv9It-UO.js";import{i as n}from"./herosideimg-B5KTUGzn.js";const o=()=>{const a=l(),s={requestId:"#********",price:"$22.00",requestedAmount:"$15.00",date:"20 May 2025 | 4:50PM",itemTitle:"<PERSON> and Coaching Philosophies to Developing Toughness In Your Players",itemAuthor:"By Basketball Coaches Clinic"},t=()=>{a("/buyer/account/requests")},i=()=>{a("/")};return e.jsx("div",{className:"request-thank-you-page",children:e.jsxs("div",{className:"request-thank-you-container max-container",children:[e.jsxs("div",{className:"success-header",children:[e.jsx("div",{className:"success-icon",children:e.jsx("img",{src:c,alt:"Thank you"})}),e.jsx("h1",{className:"success-title",children:"Your request is submitted successfully!"}),e.jsx("p",{className:"success-message",children:"We will update you for the request status soon via Email or SMS."})]}),e.jsxs("div",{className:"request-info-card",children:[e.jsx("h2",{className:"request-info-title",children:"Request Information"}),e.jsxs("div",{className:"reqthankyoucontainer",children:[e.jsxs("div",{className:"request-details-grid",children:[e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Request Id"}),e.jsx("span",{className:"detail-value",children:s.requestId})]}),e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Price"}),e.jsx("span",{className:"detail-value",children:s.price})]})]}),e.jsx("div",{className:"verticalline"}),e.jsxs("div",{className:"request-details-grid",children:[e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Date"}),e.jsx("span",{className:"detail-value",children:s.date})]}),e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Requested Amount"}),e.jsx("span",{className:"detail-value",children:s.requestedAmount})]})]})]}),e.jsxs("div",{className:"item-info-section",children:[e.jsx("h3",{className:"section-title",children:"Item Info"}),e.jsxs("div",{className:"item-info-content",children:[e.jsx("img",{src:n,alt:"Item",className:"item-image"}),e.jsxs("div",{className:"item-details",children:[e.jsx("p",{className:"item-title",children:s.itemTitle}),e.jsx("p",{className:"item-author",children:s.itemAuthor})]})]})]})]}),e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{type:"button",className:"btn btn-outline request-btn",onClick:t,children:"Go To My Request Page"}),e.jsx("button",{type:"button",className:"btn btn-primary homepage-btn",onClick:i,children:"Go To Homepage"})]})]})})};export{o as default};
