import{b as D,c as B,d as h,T as C,U as O,V as U,W as k,X as L,Y as V,Z as E,r as m,$ as P,a0 as _,a1 as q,a2 as F,a3 as T,j as e,a4 as I,a5 as M,a6 as G,a7 as H,L as u,a8 as S}from"./index-ctFdmWBt.js";import{S as W}from"./SellerLayout-EbrVdrvL.js";import{f as X}from"./dateValidation-cL5kH0gD.js";import"./timezoneUtils-BuH33ask.js";const g=t=>X(t),x=t=>{var n;if(!t)return"N/A";let l;return typeof t=="number"?l=t:typeof t=="object"?t.saleType==="Auction"&&((n=t.auctionDetails)!=null&&n.basePrice)?l=t.auctionDetails.basePrice:l=t.price:l=t,!l&&l!==0?"N/A":`$${parseFloat(l).toFixed(2)}`},Y=({count:t,label:l,color:n,icone:a,onClick:c,navigateTo:i})=>e.jsxs("div",{className:`stats-card ${n}`,onClick:()=>i&&c(i),style:{cursor:i?"pointer":"default"},children:[e.jsxs("div",{className:"detailmain",children:[e.jsx("h2",{children:t}),e.jsx("p",{children:l})]}),e.jsx("div",{className:`icon-round ${n}`,children:a})]}),Z=({item:t,toggle:l})=>e.jsxs("tr",{children:[e.jsx("td",{children:t.id}),e.jsx("td",{children:e.jsxs("div",{className:"video-title",children:[e.jsx(S,{className:"video-icon"}),e.jsx("span",{children:t.title})]})}),e.jsx("td",{children:t.date}),e.jsx("td",{children:t.price}),e.jsx("td",{children:e.jsxs("label",{className:"switch",children:[e.jsx("input",{type:"checkbox",checked:t.status,onChange:()=>l(t.id)}),e.jsx("span",{className:"slider round"})]})})]}),z=({item:t,showUser:l=!0})=>e.jsxs("tr",{children:[e.jsx("td",{children:t.id}),e.jsx("td",{children:e.jsxs("div",{className:"video-title",children:[e.jsx(S,{className:"video-icon"}),e.jsx("span",{children:t.title})]})}),e.jsx("td",{children:t.date}),e.jsx("td",{children:t.price}),e.jsx("td",{children:t.amount}),l?e.jsx("td",{children:t.user}):null]}),J=({item:t})=>e.jsxs("tr",{children:[e.jsx("td",{children:t.id}),e.jsx("td",{children:e.jsxs("div",{className:"video-title",children:[e.jsx(S,{className:"video-icon"}),e.jsx("span",{children:t.title})]})}),e.jsx("td",{children:t.date}),e.jsx("td",{children:t.price}),e.jsx("td",{children:t.amount})]}),K=({item:t})=>e.jsxs("tr",{children:[e.jsx("td",{children:t.id}),e.jsx("td",{children:e.jsxs("div",{className:"video-title",children:[e.jsx(S,{className:"video-icon"}),e.jsx("span",{children:t.title})]})}),e.jsx("td",{children:t.date}),e.jsx("td",{children:t.price}),e.jsx("td",{children:t.amount}),e.jsx("td",{children:t.buyer}),e.jsx("td",{children:e.jsx("span",{className:`status-badge status-${t.status.toLowerCase()}`,children:t.status})})]}),de=()=>{const t=D(),l=B(),n=h(C),a=h(O),c=h(U),i=h(k),j=h(L),p=h(V),o=h(E),[f,N]=m.useState([]);m.useEffect(()=>{t(P()),t(_()),t(q()),t(F()),t(T())},[t]),m.useEffect(()=>{if(a&&a.length>0){const s=a.map((d,r)=>({id:d._id?`#${d._id.slice(-6)}`:d.id?`#${d.id.slice(-6)}`:`#${(r+1).toString().padStart(6,"0")}`,title:d.title||"Untitled Strategy",date:g(d.createdAt||d.date),price:x(d),status:d.isActive!==void 0?d.isActive:!0}));N(s)}},[a]);const A=s=>{l(s)},w=(a==null?void 0:a.filter(s=>s.isActive===1).length)||0,$=[{count:String(w).padStart(2,"0"),label:"Strategies",color:"purple",icone:e.jsx(I,{}),navigateTo:"/seller/my-sports-strategies"},{count:String(n.totalRequests||(c==null?void 0:c.length)||0).padStart(2,"0"),label:"Requests",color:"orange",icone:e.jsx(M,{}),navigateTo:"/seller/requests"},{count:String(n.totalBids||(i==null?void 0:i.length)||0).padStart(2,"0"),label:"Bids",color:"green",icone:e.jsx(G,{}),navigateTo:"/seller/bids"},{count:String(n.totalOffers||(j==null?void 0:j.length)||0).padStart(2,"0"),label:"Offers",color:"blue",icone:e.jsx(H,{}),navigateTo:"/seller/offers"}],R=s=>{N(d=>d.map(r=>r.id===s?{...r,status:!r.status}:r))},v=(c==null?void 0:c.slice(0,2).map((s,d)=>{var r;return{id:s._id?`#${s._id.slice(-6)}`:`#${(d+1).toString().padStart(6,"0")}`,title:s.title||"Untitled Request",date:g(s.createdAt||s.date),price:x(s.budget||s),amount:x(((r=s.sellerResponse)==null?void 0:r.price)||s.requestedAmount),user:s.buyer&&`${s.buyer.firstName||""} ${s.buyer.lastName||""}`.trim()||"Unknown User"}}))||[],y=(i==null?void 0:i.slice(0,2).map((s,d)=>{var r;return{id:s._id?`#${s._id.slice(-6)}`:`#${(d+1).toString().padStart(6,"0")}`,title:((r=s.content)==null?void 0:r.title)||"Untitled Content",date:g(s.createdAt||s.date),price:x(s.content||{}),amount:x(s.amount||s.bidAmount)}}))||[],b=(j==null?void 0:j.slice(0,2).map((s,d)=>{var r;return{id:s._id?`#${s._id.slice(-6)}`:`#${(d+1).toString().padStart(6,"0")}`,title:((r=s.content)==null?void 0:r.title)||"Untitled Content",date:g(s.createdAt||s.date),price:x(s.content||{}),amount:x(s.amount),buyer:s.buyer&&`${s.buyer.firstName||""} ${s.buyer.lastName||""}`.trim()||"Unknown Buyer",status:s.status||"Pending"}}))||[];return e.jsx(W,{children:e.jsxs("div",{className:"dashboard-container",children:[e.jsx("div",{className:"stats-container",children:$.map((s,d)=>e.jsx(Y,{...s,onClick:A},d))}),e.jsxs("div",{className:"section",children:[e.jsxs("div",{className:"section-header",children:[e.jsx("h3",{children:"Latest Sports Strategies"}),e.jsx(u,{to:"/seller/my-sports-strategies",className:"view-all",children:"View All Strategies"})]}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Order id"}),e.jsx("th",{children:"Videos/Documents"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Price"}),e.jsx("th",{children:"Status"})]})}),e.jsx("tbody",{children:p.content?e.jsx("tr",{children:e.jsx("td",{colSpan:"5",style:{textAlign:"center",padding:"20px"},children:"Loading strategies..."})}):o.content?e.jsx("tr",{children:e.jsxs("td",{colSpan:"5",style:{textAlign:"center",padding:"20px",color:"red"},children:["Error loading strategies: ",o.content]})}):f.length>0?f.slice(0,2).map(s=>e.jsx(Z,{item:s,toggle:R},s.id)):e.jsx("tr",{children:e.jsx("td",{colSpan:"5",style:{textAlign:"center",padding:"20px"},children:"No strategies found"})})})]})})]}),e.jsxs("div",{className:"section",children:[e.jsxs("div",{className:"section-header",children:[e.jsx("h3",{children:"New Requests"}),e.jsx(u,{to:"/seller/requests",className:"view-all",children:"View All Requests"})]}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Order Id"}),e.jsx("th",{children:"Video/Documents"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Price"}),e.jsx("th",{children:"Requested Amount"}),e.jsx("th",{children:"Requested Customer"})]})}),e.jsx("tbody",{children:p.requests?e.jsx("tr",{children:e.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:"Loading requests..."})}):o.requests?e.jsx("tr",{children:e.jsxs("td",{colSpan:"6",style:{textAlign:"center",padding:"20px",color:"red"},children:["Error loading requests: ",o.requests]})}):v.length>0?v.map(s=>e.jsx(z,{item:s,showUser:!0},s.id)):e.jsx("tr",{children:e.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:"No requests found"})})})]})})]}),e.jsxs("div",{className:"section",children:[e.jsxs("div",{className:"section-header",children:[e.jsx("h3",{children:"New Bids"}),e.jsx(u,{to:"/seller/bids",className:"view-all",children:"View All Bids"})]}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Bid Id"}),e.jsx("th",{children:"Video/Documents"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Price"}),e.jsx("th",{children:"Bid Amount"})]})}),e.jsx("tbody",{children:p.bids?e.jsx("tr",{children:e.jsx("td",{colSpan:"5",style:{textAlign:"center",padding:"20px"},children:"Loading bids..."})}):o.bids?e.jsx("tr",{children:e.jsxs("td",{colSpan:"5",style:{textAlign:"center",padding:"20px",color:"red"},children:["Error loading bids: ",o.bids]})}):y.length>0?y.map(s=>e.jsx(J,{item:s},s.id)):e.jsx("tr",{children:e.jsx("td",{colSpan:"5",style:{textAlign:"center",padding:"20px"},children:"No bids found"})})})]})})]}),e.jsxs("div",{className:"section",children:[e.jsxs("div",{className:"section-header",children:[e.jsx("h3",{children:"New Offers"}),e.jsx(u,{to:"/seller/offers",className:"view-all",children:"View All Offers"})]}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Offer Id"}),e.jsx("th",{children:"Video/Documents"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Price"}),e.jsx("th",{children:"Offer Amount"}),e.jsx("th",{children:"Buyer"}),e.jsx("th",{children:"Status"})]})}),e.jsx("tbody",{children:p.offers?e.jsx("tr",{children:e.jsx("td",{colSpan:"7",style:{textAlign:"center",padding:"20px"},children:"Loading offers..."})}):o.offers?e.jsx("tr",{children:e.jsxs("td",{colSpan:"7",style:{textAlign:"center",padding:"20px",color:"red"},children:["Error loading offers: ",o.offers]})}):b.length>0?b.map(s=>e.jsx(K,{item:s},s.id)):e.jsx("tr",{children:e.jsx("td",{colSpan:"7",style:{textAlign:"center",padding:"20px"},children:"No offers found"})})})]})})]})]})})};export{de as default};
