import{b as j,c as g,d as y,cE as N,j as e,ae as p,a4 as f,a6 as u,cc as v,aM as k,af as A,aW as C,cd as M,cF as D,ce as P,A as L,r as w,S as l,a5 as B}from"./index-ctFdmWBt.js";const T=()=>{const i=j(),t=g(),s=y(N),a=c=>{switch(i(D(c)),c){case"dashboard":t("/seller/dashboard");break;case"my-sports-strategies":t("/seller/my-sports-strategies");break;case"requests":t("/seller/requests");break;case"bids":t("/seller/bids");break;case"offers":t("/seller/offers");break;case"cards":t("/seller/cards");break;case"payment-settings":t("/seller/payment-settings");break;case"profile":t("/seller/profile");break;default:t("/seller/dashboard")}},r=()=>{i(P()),t("/")};return e.jsx("div",{className:"SellerSidebar",children:e.jsx("div",{className:"SellerSidebar__container",children:e.jsxs("ul",{className:"SellerSidebar__menu",children:[e.jsxs("li",{className:`SellerSidebar__item ${s==="dashboard"?"active":""}`,onClick:()=>a("dashboard"),children:[e.jsx(p,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Dashboard"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="my-sports-strategies"?"active":""}`,onClick:()=>a("my-sports-strategies"),children:[e.jsx(f,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Sports Strategies"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="bids"?"active":""}`,onClick:()=>a("bids"),children:[e.jsx(u,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Bids"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="offers"?"active":""}`,onClick:()=>a("offers"),children:[e.jsx(v,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Offers"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="cards"?"active":""}`,onClick:()=>a("cards"),children:[e.jsx(k,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Cards"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="payment-settings"?"active":""}`,onClick:()=>a("payment-settings"),children:[e.jsx(A,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Payment Settings"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="profile"?"active":""}`,onClick:()=>a("profile"),children:[e.jsx(C,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Profile"})]}),e.jsxs("li",{className:"SellerSidebar__item SellerSidebar__logout",onClick:r,children:[e.jsx(M,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Logout"})]})]})})})},$=({children:i})=>{const t=j(),s=L(),a=g(),r=y(N),c={"/seller/dashboard":"dashboard","/seller/my-sports-strategies":"my-sports-strategies","/seller/my-sports-strategies/add":"my-sports-strategies","/seller/requests":"requests","/seller/bids":"bids","/seller/offers":"offers","/seller/cards":"cards","/seller/payment-settings":"payment-settings","/seller/profile":"profile"},q=()=>s.pathname.startsWith("/seller/strategy-details/")?"my-sports-strategies":s.pathname.startsWith("/seller/request-details/")?"requests":s.pathname.startsWith("/seller/bid-details/")?"bids":s.pathname.startsWith("/seller/offer-details/")?"offers":null,o=s.pathname==="/seller/my-sports-strategies/add",d=/^\/seller\/strategy-details\/[^/]+\/edit$/.test(s.pathname),m=s.pathname.startsWith("/seller/strategy-details/")&&!d,h=s.pathname.startsWith("/seller/request-details/"),b=s.pathname.startsWith("/seller/bid-details/"),x=s.pathname.startsWith("/seller/offer-details/"),S={dashboard:{title:"Dashboard",icon:e.jsx(p,{})},"my-sports-strategies":{title:"My Sports Strategies",icon:e.jsx(f,{})},requests:{title:"Requests",icon:e.jsx(B,{})},bids:{title:"Bids",icon:e.jsx(u,{})},offers:{title:"Offers",icon:e.jsx(v,{})},cards:{title:"My Cards",icon:e.jsx(k,{})},"payment-settings":{title:"Payment Settings",icon:e.jsx(A,{})},profile:{title:"My Profile",icon:e.jsx(C,{})}},_=S[r]||S.dashboard;return w.useEffect(()=>{const n=c[s.pathname]||q();n&&n!==r&&t(D(n))},[s.pathname,r,t]),e.jsx("div",{className:"SellerLayout",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(T,{})}),e.jsxs("div",{className:"outerdiv",children:[!o&&!m&&!d&&!h&&!b&&!x&&e.jsxs("div",{className:"bordrdiv mb-30",children:[e.jsxs("h2",{className:"SellerLayout__title",children:[_.icon,_.title]}),r==="my-sports-strategies"&&e.jsx("button",{className:"add-strategy-btn btn-outline",onClick:()=>a("/seller/my-sports-strategies/add"),children:"Add New Strategy"})]}),o&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>a("/seller/my-sports-strategies"),children:[e.jsx(l,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{className:"newcssforh3",children:"Add New Strategy"})]})}),d&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>a("/seller/my-sports-strategies"),children:[e.jsx(l,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{className:"newcssforh3",children:"Edit Strategy"})]})}),m&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>a("/seller/my-sports-strategies"),children:[e.jsx(l,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{className:"newcssforh3",children:"Details Page"})]})}),h&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>a("/seller/requests"),children:[e.jsx(l,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{className:"newcssforh3",children:"Request Details"})]})}),b&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>a("/seller/bids"),children:[e.jsx(l,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{className:"newcssforh3",children:"Bid Details"})]})}),x&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>a("/seller/offers"),children:[e.jsx(l,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{className:"newcssforh3",children:"Offer Details"})]})}),e.jsx("div",{className:"contentArea",children:e.jsx("div",{className:"SellerLayout__content",children:i})})]})]})})};export{$ as S};
