import{b as I,r as m,j as e,a7 as B,aB as E,aw as R,b7 as _,bc as $,N as d,aU as L,d as D,aT as F,P as U,cc as T,L as M,b5 as Y,am as q,cI as G,cJ as H}from"./index-ctFdmWBt.js";import{S as J}from"./SellerLayout-EbrVdrvL.js";import{T as K}from"./Table-j5pMA9qi.js";import{P as z}from"./Pagination-GDpQMGEO.js";import{L as Q}from"./LoadingSkeleton-DFCyGuTF.js";import{g as V}from"./settingsService-Enovp4Qd.js";import{a as W,f as X}from"./dateValidation-cL5kH0gD.js";/* empty css                        */import"./timezoneUtils-BuH33ask.js";const Z=({isOpen:i,onClose:l,offer:a,onOfferProcessed:h})=>{var N,b,v,y,s,n,o,u,k,A;const j=I(),[t,x]=m.useState(!1),[c,C]=m.useState(""),[f,p]=m.useState(null),[P,g]=m.useState(!0);m.useEffect(()=>{i&&(async()=>{g(!0);try{const O=await V();O.success&&p(O.data.platformCommission)}catch(O){console.error("Error fetching platform commission:",O),p(10),d.error("Failed to fetch platform commission. Using default rate.")}finally{g(!1)}})()},[i]);const S=async()=>{if(!a||!a._id){d.error("Invalid offer data");return}x(!0);try{await j(L({offerId:a._id,status:"accepted",sellerResponse:c.trim()||"Offer accepted! Thank you for your interest."})).unwrap(),d.success("Offer accepted successfully! Buyer will be notified."),h&&h(a._id,"accepted"),l()}catch(r){console.error("Error accepting offer:",r),d.error(r.message||"Failed to accept offer")}finally{x(!1)}},w=async()=>{if(!a||!a._id){d.error("Invalid offer data");return}if(!c.trim()){d.error("Please provide a reason for rejecting this offer");return}x(!0);try{await j(L({offerId:a._id,status:"rejected",sellerResponse:c.trim()})).unwrap(),d.success("Offer rejected successfully."),h&&h(a._id,"rejected"),l()}catch(r){console.error("Error rejecting offer:",r),d.error(r.message||"Failed to reject offer")}finally{x(!1)}};return!i||!a?null:e.jsx("div",{className:"modal-overlay",onClick:l,children:e.jsxs("div",{className:"offer-acceptance-modal",onClick:r=>r.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h2",{className:"modal-title",children:[e.jsx(B,{className:"modal-icon"}),"Manage Offer"]}),e.jsx("button",{className:"modal-close",onClick:l,children:e.jsx(E,{})})]}),e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"offer-details-section",children:[e.jsx("h3",{children:"Offer Details"}),e.jsxs("div",{className:"offer-info-grid",children:[e.jsxs("div",{className:"offer-info-item",children:[e.jsx("span",{className:"label",children:"Buyer:"}),e.jsxs("span",{className:"value",children:[(N=a.buyer)==null?void 0:N.firstName," ",(b=a.buyer)==null?void 0:b.lastName]})]}),e.jsxs("div",{className:"offer-info-item",children:[e.jsx("span",{className:"label",children:"Email:"}),e.jsx("span",{className:"value",children:(v=a.buyer)==null?void 0:v.email})]}),e.jsxs("div",{className:"offer-info-item",children:[e.jsx("span",{className:"label",children:"Offer Amount:"}),e.jsxs("span",{className:"value offer-amount",children:["$",(y=a.amount)==null?void 0:y.toFixed(2)]})]}),e.jsxs("p",{className:"offer-date",children:["Submitted on"," ",W(a.createdAt)]})]}),a.message&&e.jsxs("div",{className:"buyer-message-section",children:[e.jsx("h4",{children:"Buyer's Message"}),e.jsxs("div",{className:"buyer-message",children:['"',a.message,'"']})]})]}),e.jsxs("div",{className:"content-details-section",children:[e.jsx("h3",{children:"Content Details"}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:(s=a.content)==null?void 0:s.title}),e.jsxs("div",{className:"content-meta",children:[(n=a.content)==null?void 0:n.sport," • ",(o=a.content)==null?void 0:o.contentType," • ",(u=a.content)==null?void 0:u.difficulty]}),((k=a.content)==null?void 0:k.price)&&e.jsxs("div",{className:"listed-price",children:["Listed Price: $",a.content.price.toFixed(2)]})]})]}),e.jsxs("div",{className:"notice-section",children:[e.jsxs("div",{className:"notice-box acceptance",children:[e.jsx(R,{className:"notice-icon"}),e.jsxs("div",{className:"notice-content",children:[e.jsx("h4",{children:"Important - Offer Acceptance"}),e.jsxs("ul",{children:[e.jsxs("li",{children:["Accepting this offer will ",e.jsx("strong",{children:"create an immediate sale"})]}),e.jsxs("li",{children:["The content will be ",e.jsx("strong",{children:"removed from public listing"})]}),e.jsx("li",{children:"The buyer will receive an email with payment instructions"}),e.jsx("li",{children:"You'll receive payment after successful transaction"}),e.jsxs("li",{children:["This action ",e.jsx("strong",{children:"cannot be undone"})]})]})]})]}),e.jsxs("div",{className:"notice-box rejection",children:[e.jsx(R,{className:"notice-icon"}),e.jsxs("div",{className:"notice-content",children:[e.jsx("h4",{children:"Important - Offer Rejection"}),e.jsxs("ul",{children:[e.jsxs("li",{children:["Rejecting requires a ",e.jsx("strong",{children:"reason message"})]}),e.jsx("li",{children:"The buyer will be notified with your response"}),e.jsx("li",{children:"Your content will remain available for other offers"}),e.jsx("li",{children:"Be professional and constructive in your response"})]})]})]})]}),e.jsxs("div",{className:"response-section",children:[e.jsxs("label",{htmlFor:"sellerResponse",className:"response-label",children:["Response Message ",e.jsx("span",{className:"required",children:"*Required for rejection"})]}),e.jsx("textarea",{id:"sellerResponse",className:"response-textarea",placeholder:"Add a message to the buyer (required for rejection, optional for acceptance)...",value:c,onChange:r=>C(r.target.value),maxLength:500,rows:3}),e.jsxs("div",{className:"character-count",children:[c.length,"/500 characters"]})]}),e.jsxs("div",{className:"earnings-section",children:[e.jsx("h3",{children:"Earnings Breakdown"}),P?e.jsxs("div",{className:"loading-spinner",children:[e.jsx(_,{className:"spinner"})," Loading commission rate..."]}):e.jsxs("div",{className:"earnings-grid",children:[e.jsxs("div",{className:"earnings-item",children:[e.jsx("span",{className:"label",children:"Offer Amount:"}),e.jsxs("span",{className:"value",children:["$",(A=a.amount)==null?void 0:A.toFixed(2)]})]}),e.jsxs("div",{className:"earnings-item",children:[e.jsxs("span",{className:"label",children:["Platform Fee (",f,"%):"]}),e.jsxs("span",{className:"value",children:["-$",(a.amount*(f/100)).toFixed(2)]})]}),e.jsxs("div",{className:"earnings-item total",children:[e.jsx("span",{className:"label",children:"Your Earnings:"}),e.jsxs("span",{className:"value",children:["$",(a.amount*(1-f/100)).toFixed(2)]})]})]})]})]}),e.jsxs("div",{className:"modal-actions",children:[e.jsx("button",{className:"btn-secondary",onClick:l,disabled:t,children:"Cancel"}),e.jsxs("button",{className:"btn-danger",onClick:w,disabled:t||!c.trim(),children:[e.jsx(E,{}),t?"Processing...":"Reject Offer"]}),e.jsxs("button",{className:"btn-success",onClick:S,disabled:t,children:[e.jsx($,{}),t?"Processing...":"Accept Offer"]})]})]})})},oe=()=>{const i=I(),{sellerOffers:l,isLoading:a,isError:h,error:j,pagination:t}=D(s=>s.offer),[x,c]=m.useState(!1),[C,f]=m.useState(null);m.useEffect(()=>{i(F({page:t.page,limit:t.limit}))},[i,t.page]);const p=s=>{f(s),c(!0)},P=()=>{c(!1),f(null)},g=()=>{i(F({page:t.page,limit:t.limit}))},S=()=>{i(F({page:t.page,limit:t.limit}))},w=s=>{i(H(s))},N=s=>{const n={Pending:"status-pending",Accepted:"status-accepted",Rejected:"status-rejected",Cancelled:"status-cancelled",Expired:"status-expired"};return e.jsx("span",{className:`status-badge ${n[s]||""}`,children:s})},b=s=>X(s),v=s=>`$${parseFloat(s).toFixed(2)}`,y=[{label:"Content",key:"content",render:s=>{var n,o,u;return e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-thumbnail",children:(n=s.content)!=null&&n.thumbnailUrl?e.jsx("img",{src:Y+s.content.thumbnailUrl,alt:s.content.title}):e.jsx("div",{className:"no-thumbnail",children:e.jsx(T,{})})}),e.jsxs("div",{className:"content-details",children:[e.jsx("h4",{className:"content-title",children:((o=s.content)==null?void 0:o.title)||"Untitled"}),e.jsx("p",{className:"content-sport",children:((u=s.content)==null?void 0:u.sport)||"N/A"})]})]})}},{label:"Buyer",key:"buyer",render:s=>{var n,o;return e.jsx("div",{className:"buyer-info",children:e.jsxs("span",{className:"buyer-name",children:[(n=s.buyer)==null?void 0:n.firstName," ",(o=s.buyer)==null?void 0:o.lastName]})})}},{label:"Offer Amount",key:"amount",render:s=>e.jsx("span",{className:"offer-amount",children:v(s.amount)})},{label:"Status",key:"status",render:s=>N(s.status)},{label:"Date",key:"createdAt",render:s=>e.jsx("span",{className:"offer-date",children:b(s.createdAt)})},{label:"Actions",key:"actions",render:s=>e.jsxs("div",{className:"action-buttons",children:[e.jsx(M,{to:`/seller/offer-details/${s._id}`,className:"action-btn btn-icon view-btn",children:e.jsx(q,{})}),s.status==="Pending"&&e.jsx("button",{className:"btn-icon btn-manage",onClick:()=>p(s),title:"Manage Offer",children:e.jsx(G,{})})]})}];return e.jsx(J,{children:e.jsxs("div",{className:"SellerOffers",children:[e.jsx("div",{className:"page-header",children:e.jsx("p",{children:"Manage offers from buyers for your content"})}),a?e.jsx(Q,{type:"table",rows:5}):h?e.jsx(U,{error:j,onRetry:S,title:"Failed to load offers"}):l&&l.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"offers-summary",children:e.jsxs("p",{children:["You have received ",t.total," offer",t.total!==1?"s":""]})}),e.jsx(K,{columns:y,data:l,className:"offers-table"}),t.totalPages>1&&e.jsx("div",{className:"pagination-container",children:e.jsx(z,{currentPage:t.page,totalPages:t.totalPages,onPageChange:w})})]}):e.jsxs("div",{className:"no-offers",children:[e.jsx(T,{className:"no-offers-icon"}),e.jsx("h3",{children:"No Offers Yet"}),e.jsx("p",{children:"You haven't received any offers yet. Keep creating great content to attract buyers!"}),e.jsx(M,{to:"/seller/my-sports-strategies/add",className:"btn-primary text-decoration-none",children:"Add New Content"})]}),e.jsx(Z,{isOpen:x,onClose:P,offer:C,onOfferProcessed:g})]})})};export{oe as default};
