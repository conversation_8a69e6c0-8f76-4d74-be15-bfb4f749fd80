import{b as z,d as H,r as p,ai as U,t as C,o as $,j as e,aV as K,aW as y,aX as Q,aY as ee,e as le,aZ as D,a_ as R,ao as ie,aJ as re,a$ as O,E as A,b0 as M,b1 as se,b2 as ae}from"./index-ctFdmWBt.js";import{S as ne}from"./SellerLayout-EbrVdrvL.js";const de=()=>{const _=z(),{user:m,isLoading:P,isSuccess:E,isError:L,error:b}=H(r=>r.auth),[l,h]=p.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:"",description:"",experiences:[],minTrainingCost:"",socialLinks:{facebook:"",instagram:"",twitter:""},isOnboardingComplete:!1}),[I,B]=p.useState(null),[F,V]=p.useState(null),[d,w]=p.useState(!1),[u,Y]=p.useState({experienceYears:{}});p.useEffect(()=>{_(U())},[_]),p.useEffect(()=>{var r,i,s,t,c,n;if(m&&m.data){const a=m.data,o=a.sellerInfo||{};h({firstName:a.firstName||"",lastName:a.lastName||"",email:a.email||"",mobile:a.mobile||"",profileImage:a.profileImage||"",description:o.description||"",experiences:o.experiences&&o.experiences.length>0?o.experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:o.minTrainingCost||"",socialLinks:{facebook:((r=o.socialLinks)==null?void 0:r.facebook)||"",instagram:((i=o.socialLinks)==null?void 0:i.instagram)||"",twitter:((s=o.socialLinks)==null?void 0:s.twitter)||""},isOnboardingComplete:o.isOnboardingComplete||!1})}else if(m){const a=m.sellerInfo||{};h({firstName:m.firstName||"",lastName:m.lastName||"",email:m.email||"",mobile:m.mobile||"",profileImage:m.profileImage||"",description:a.description||"",experiences:a.experiences&&a.experiences.length>0?a.experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:a.minTrainingCost||"",socialLinks:{facebook:((t=a.socialLinks)==null?void 0:t.facebook)||"",instagram:((c=a.socialLinks)==null?void 0:c.instagram)||"",twitter:((n=a.socialLinks)==null?void 0:n.twitter)||""},isOnboardingComplete:a.isOnboardingComplete||!1})}},[m]);const[j,g]=p.useState(!1),[q,T]=p.useState(!1);p.useEffect(()=>{j&&E&&!P&&(C.success("Profile updated successfully!"),_($()),g(!1),w(!1),_(U())),j&&L&&b&&(C.error(b.message||"Failed to update profile"),_($()),g(!1))},[E,L,b,P,_,j]);const k=()=>{const r=new Date().getFullYear(),i=1950,s=r,t={};let c=!1;return l.experiences.forEach((n,a)=>{const o=parseInt(n.fromYear),S=parseInt(n.toYear),f={};String(n.fromYear||"").trim()?(isNaN(o)||o<i||o>s)&&(f.fromYear=`From year must be between ${i} and ${s}`,c=!0):(f.fromYear="From year is required",c=!0),String(n.toYear||"").trim()?isNaN(S)||S<i||S>s?(f.toYear=`To year must be between ${i} and ${s}`,c=!0):!isNaN(o)&&S<=o&&(f.toYear="To year must be greater than from year",c=!0):(f.toYear="To year is required",c=!0),Object.keys(f).length>0&&(t[a]=f)}),c?(Y(n=>({...n,experienceYears:t})),!1):(Y(n=>({...n,experienceYears:{}})),!0)},N=r=>{const{name:i,value:s}=r.target;if(i.startsWith("socialLinks.")){const t=i.split(".")[1];h(c=>({...c,socialLinks:{...c.socialLinks,[t]:s}}))}else h(t=>({...t,[i]:s}))},v=(r,i,s)=>{var t,c;h(n=>({...n,experiences:n.experiences.map((a,o)=>o===r?{...a,[i]:s}:a)})),(i==="fromYear"||i==="toYear")&&(c=(t=u.experienceYears)==null?void 0:t[r])!=null&&c[i]&&Y(n=>({...n,experienceYears:{...n.experienceYears,[r]:{...n.experienceYears[r],[i]:""}}}))},W=()=>{h(r=>({...r,experiences:[...r.experiences,{schoolName:"",position:"",fromYear:"",toYear:""}]}))},X=r=>{l.experiences.length>1&&h(i=>({...i,experiences:i.experiences.filter((s,t)=>t!==r)}))},G=async r=>{if(r.preventDefault(),!!k()){g(!0);try{let s=l.profileImage;I&&(s=(await _(se(I)).unwrap()).data.fileUrl);const t={firstName:l.firstName,lastName:l.lastName,profileImage:s,sellerInfo:{description:l.description,experiences:l.experiences,minTrainingCost:l.minTrainingCost,socialLinks:l.socialLinks,isOnboardingComplete:l.isOnboardingComplete}};_(ae(t))}catch(s){console.error("Profile update error:",s),C.error("Failed to upload image or update profile"),g(!1)}}},J=r=>{const i=r.target.files[0];if(i){B(i),T(!1);const s=new FileReader;s.onloadend=()=>{V(s.result)},s.readAsDataURL(i)}},Z=()=>{T(!0)};return e.jsx(ne,{children:e.jsxs("div",{className:"SellerProfile",children:[e.jsx("div",{className:"SellerProfile__header",children:e.jsxs("button",{className:"SellerProfile__edit-btn btn-primary",onClick:()=>w(!d),children:[e.jsx(K,{})," ",d?"Cancel Edit":"Edit Profile"]})}),e.jsxs("div",{className:"SellerProfile__container",children:[e.jsx("div",{className:"SellerProfile__left-section",children:e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Basic Information"}),e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(y,{})}),e.jsx("input",{type:"text",name:"firstName",value:l.firstName,onChange:N,placeholder:"First Name",disabled:!d,className:`SellerProfile__input ${d?"":"SellerProfile__input--disabled"}`})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(y,{})}),e.jsx("input",{type:"text",name:"lastName",value:l.lastName,onChange:N,placeholder:"Last Name",disabled:!d,className:`SellerProfile__input ${d?"":"SellerProfile__input--disabled"}`})]})})]}),e.jsxs("div",{className:"SellerProfile__form-row ",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(Q,{})}),e.jsx("input",{type:"email",name:"email",value:l.email,placeholder:"Email Address",disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(ee,{})}),e.jsx("input",{type:"tel",name:"mobile",value:l.mobile,placeholder:"Mobile Number",disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})})]})]})}),e.jsx("div",{className:"SellerProfile__right-section",children:e.jsxs("div",{className:"SellerProfile__image-container",children:[e.jsx("h3",{className:"SellerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"SellerProfile__image",children:F||l.profileImage&&!q?e.jsx("img",{src:F||le(l.profileImage),alt:"Profile",onError:Z}):e.jsx("div",{className:"SellerProfile__placeholder",children:l.firstName&&l.lastName?`${l.firstName.charAt(0)}${l.lastName.charAt(0)}`:e.jsx(y,{className:"SellerProfile__user-icon"})})}),d&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"SellerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:J,style:{display:"none"}})]})]})})]}),e.jsxs("div",{className:"SellerProfile__description-experience-container",children:[e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Description"}),e.jsx("div",{className:"SellerProfile__description-container",children:d?e.jsx("textarea",{name:"description",value:l.description,onChange:N,placeholder:"Enter your professional description...",className:"SellerProfile__textarea",rows:4}):e.jsx("div",{className:"SellerProfile__description-display",children:l.description||"No description provided"})})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Minimum Training Cost"}),e.jsx("div",{className:"SellerProfile__cost-container",children:d?e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(D,{})}),e.jsx("input",{type:"number",name:"minTrainingCost",value:l.minTrainingCost,onChange:N,placeholder:"Enter amount",className:"SellerProfile__input"})]})}):e.jsxs("div",{className:"SellerProfile__cost-display",children:[e.jsx(D,{className:"SellerProfile__cost-icon"}),e.jsx("span",{className:"SellerProfile__cost-amount",children:l.minTrainingCost?`${l.minTrainingCost}`:"Not specified"})]})})]})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Experience"}),e.jsx("div",{className:"SellerProfile__experiences",children:d?e.jsxs(e.Fragment,{children:[l.experiences&&l.experiences.length>0?e.jsx("div",{className:"SellerProfile__experiences-grid",children:l.experiences.map((r,i)=>{var s,t,c,n,a,o,S,f;return e.jsxs("div",{className:"SellerProfile__experience-edit-item",children:[e.jsxs("div",{className:"SellerProfile__experience-edit-header",children:[e.jsx(R,{className:"SellerProfile__experience-icon"}),e.jsxs("span",{className:"SellerProfile__experience-number",children:["Experience ",i+1]}),l.experiences.length>1&&e.jsx("button",{type:"button",className:"SellerProfile__remove-btn",onClick:()=>X(i),children:e.jsx(ie,{className:""})})]}),e.jsxs("div",{className:"SellerProfile__experience-form",children:[e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsx("input",{type:"text",placeholder:"Enter Experience",value:r.schoolName,onChange:x=>v(i,"schoolName",x.target.value),className:"SellerProfile__input"})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsx("input",{type:"text",placeholder:"Enter Position",value:r.position,onChange:x=>v(i,"position",x.target.value),className:"SellerProfile__input"})})]}),e.jsxs("div",{className:"SellerProfile__form-row SellerProfile__form-row-email-phone",children:[e.jsxs("div",{className:"SellerProfile__input-field",children:[e.jsx("input",{type:"text",placeholder:"From Year",value:r.fromYear,onChange:x=>v(i,"fromYear",x.target.value),onBlur:()=>k(),className:`SellerProfile__input ${(t=(s=u.experienceYears)==null?void 0:s[i])!=null&&t.fromYear?"SellerProfile__input--error":""}`}),((n=(c=u.experienceYears)==null?void 0:c[i])==null?void 0:n.fromYear)&&e.jsx("div",{className:"SellerProfile__field-error",children:u.experienceYears[i].fromYear})]}),e.jsxs("div",{className:"SellerProfile__input-field",children:[e.jsx("input",{type:"text",placeholder:"To Year",value:r.toYear,onChange:x=>v(i,"toYear",x.target.value),onBlur:()=>k(),className:`SellerProfile__input ${(o=(a=u.experienceYears)==null?void 0:a[i])!=null&&o.toYear?"SellerProfile__input--error":""}`}),((f=(S=u.experienceYears)==null?void 0:S[i])==null?void 0:f.toYear)&&e.jsx("div",{className:"SellerProfile__field-error",children:u.experienceYears[i].toYear})]})]})]})]},i)})}):e.jsx("div",{className:"SellerProfile__no-data",children:"No experience information provided"}),e.jsx("div",{className:"SellerProfile__add-experience-container",children:e.jsxs("button",{type:"button",className:"SellerProfile__add-btn btn-primary",onClick:W,children:[e.jsx(re,{})," Add More Experience"]})})]}):e.jsx(e.Fragment,{children:l.experiences&&l.experiences.length>0?e.jsx("div",{className:"SellerProfile__experiences-grid",children:l.experiences.map((r,i)=>e.jsxs("div",{className:"SellerProfile__experience-item",children:[e.jsxs("div",{className:"SellerProfile__experience-header",children:[e.jsx(R,{className:"SellerProfile__experience-icon"}),e.jsxs("span",{className:"SellerProfile__experience-number",children:["Experience ",i+1]})]}),e.jsxs("div",{className:"SellerProfile__experience-content",children:[e.jsx("div",{className:"SellerProfile__experience-field",children:r.schoolName||"School Name"}),e.jsx("div",{className:"SellerProfile__experience-field",children:r.position||"Position"}),e.jsxs("div",{className:"SellerProfile__experience-years",children:[e.jsx("span",{children:r.fromYear||"Start Year"}),e.jsx("span",{children:r.toYear||"End Year"})]})]})]},i))}):e.jsx("div",{className:"SellerProfile__no-data",children:"No experience information provided"})})})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Social Media Links"}),e.jsx("div",{className:"SellerProfile__social-links",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(O,{className:"facebook"})}),e.jsx("input",{type:"url",name:"socialLinks.facebook",value:l.socialLinks.facebook,onChange:N,placeholder:"Facebook URL",className:"SellerProfile__input"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(A,{className:"instagram"})}),e.jsx("input",{type:"url",name:"socialLinks.instagram",value:l.socialLinks.instagram,onChange:N,placeholder:"Instagram URL",className:"SellerProfile__input"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(M,{className:"twitter"})}),e.jsx("input",{type:"url",name:"socialLinks.twitter",value:l.socialLinks.twitter,onChange:N,placeholder:"Twitter URL",className:"SellerProfile__input"})]})})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(O,{className:"SellerProfile__social-icon facebook"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Facebook:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.facebook?e.jsx("a",{href:l.socialLinks.facebook,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.facebook}):"Not provided"})]}),e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(A,{className:"SellerProfile__social-icon instagram"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Instagram:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.instagram?e.jsx("a",{href:l.socialLinks.instagram,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.instagram}):"Not provided"})]}),e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(M,{className:"SellerProfile__social-icon twitter"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Twitter:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.twitter?e.jsx("a",{href:l.socialLinks.twitter,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.twitter}):"Not provided"})]})]})})]}),d&&e.jsx("div",{className:"SellerProfile__buttons",children:e.jsx("button",{type:"button",className:"SellerProfile__save-btn btn-primary",onClick:G,disabled:j||P,children:j||P?"Updating...":"Update & Save"})})]})})};export{de as default};
