import{d as i,V as d,c as m,j as s,am as u}from"./index-ctFdmWBt.js";import{S as x}from"./SellerLayout-EbrVdrvL.js";import{L as j}from"./index-D-wstcb_.js";import{T as q}from"./Table-j5pMA9qi.js";const N=()=>{const t=i(d),l=m(),o=e=>{l(`/seller/request-details/${e.replace("#","")}`)},r=[{key:"no",label:"No.",className:"no"},{key:"id",label:"Request Id"},{key:"content",label:"Videos/Documents",render:e=>s.jsxs("div",{className:"video-doc",children:[s.jsx("img",{src:e.image,alt:e.title}),s.jsx("span",{children:e.title})]})},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"requestedAmount",label:"Requested Amount"},{key:"requestedCustomer",label:"Requested Customer"},{key:"action",label:"Action",render:e=>s.jsxs("div",{className:"action-icons",children:[s.jsx(u,{className:"eye-icon",onClick:()=>o(e.id)}),s.jsx(j,{className:"comment-icon"})]})}],n=e=>e.map((a,c)=>({...a,no:c+1,date:`${a.date} | 4:50PM`}));return s.jsx(x,{children:s.jsx("div",{className:"seller-requests-container",children:s.jsx(q,{columns:r,data:n(t),className:"requests-table"})})})};export{N as default};
