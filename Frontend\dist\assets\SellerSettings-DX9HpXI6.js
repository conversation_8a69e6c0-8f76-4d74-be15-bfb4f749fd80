import{d as C,b as I,r as h,a9 as k,j as s,aa as A,ab as D,ac as j,ad as c,ae as g,af as P,ag as N,ah as F,ai as R,k as M,s as S,aj as L,ak as v}from"./index-ctFdmWBt.js";import{g as z}from"./settingsService-Enovp4Qd.js";import{S as U}from"./SellerLayout-EbrVdrvL.js";const O=()=>{var f;const{user:n}=C(e=>e.auth),y=I(),[t,m]=h.useState({connected:!1,accountId:"",detailsSubmitted:!1,chargesEnabled:!1,payoutsEnabled:!1,loading:!0,error:null}),[o,p]=h.useState(!1),[l,u]=h.useState({platformCommission:10,sellerEarnings:90,loading:!0});h.useEffect(()=>{d(),E()},[]),h.useEffect(()=>{var e;(e=n==null?void 0:n.paymentInfo)!=null&&e.stripeConnectId&&d()},[(f=n==null?void 0:n.paymentInfo)==null?void 0:f.stripeConnectId]);const E=async()=>{try{u(a=>({...a,loading:!0}));const e=await z();if(e.success)u({platformCommission:e.data.platformCommission,sellerEarnings:e.data.sellerEarnings,loading:!1});else throw new Error(e.message||"Failed to fetch platform commission")}catch(e){console.error("Error fetching platform commission:",e),u(a=>({...a,loading:!1}))}},d=async()=>{var e;try{m(r=>({...r,loading:!0,error:null}));const a=(e=n==null?void 0:n.paymentInfo)==null?void 0:e.stripeConnectId;if(!a){m(r=>({...r,connected:!1,loading:!1}));return}const i=await k(a);if(i.success)m(r=>({...r,connected:!0,accountId:i.data.accountId,detailsSubmitted:i.data.detailsSubmitted,chargesEnabled:i.data.chargesEnabled,payoutsEnabled:i.data.payoutsEnabled,loading:!1}));else throw new Error(i.message||"Failed to get Stripe status")}catch(a){console.error("Error checking Stripe status:",a),m(i=>({...i,loading:!1,error:a.message}))}},b=async()=>{try{p(!0);const e=await F({email:n.email,firstName:n.firstName,lastName:n.lastName,context:"settings"});if(e.success){const a=window.open(e.data.onboardingUrl,"stripe-onboarding","width=800,height=600,scrollbars=yes,resizable=yes"),i=setInterval(async()=>{a.closed&&(clearInterval(i),p(!1),await y(R()),await d())},2e3),r=e.data.isExisting?"Continue your Stripe account setup in the opened window.":"Stripe onboarding window opened. Please complete the setup.";M(r)}else throw new Error(e.message||"Failed to setup Stripe Connect")}catch(e){console.error("Stripe setup error:",e),p(!1),S(e.message||"Failed to setup Stripe Connect")}},w=async()=>{var e;try{const a=await L();if(a.success)window.open(a.data.dashboardUrl,"stripe-dashboard","width=1200,height=800,scrollbars=yes,resizable=yes"),a.data.message&&v(a.data.message);else throw new Error(a.message||"Failed to access dashboard")}catch(a){console.error("Dashboard access error:",a),((e=a.response)==null?void 0:e.status)===500||a.message.includes("Failed to create dashboard link")?(window.open("https://dashboard.stripe.com/dashboard","stripe-dashboard","width=1200,height=800,scrollbars=yes,resizable=yes"),v("Opening general Stripe dashboard. Please log in to your Stripe account.")):S(a.message||"Failed to access Stripe dashboard")}},x=e=>e?s.jsx(c,{className:"status-icon success"}):s.jsx(j,{className:"status-icon error"});return s.jsx(U,{children:s.jsxs("div",{className:"seller-settings-content",children:[s.jsxs("div",{className:"settings-section",children:[s.jsxs("div",{className:"section-header",children:[s.jsxs("div",{className:"section-title",children:[s.jsx(A,{className:"section-icon"}),s.jsx("h2",{children:"Stripe Standard Account"})]}),!t.loading&&t.connected&&s.jsxs("button",{className:"btn-outline refresh-btn",onClick:d,disabled:o,children:[s.jsx(D,{className:o?"spinning":""}),"Refresh Status"]})]}),s.jsx("div",{className:"stripe-status-card",children:t.loading?s.jsxs("div",{className:"loading-state",children:[s.jsx("div",{className:"spinner"}),s.jsx("p",{children:"Checking Stripe Standard account status..."})]}):t.error?s.jsxs("div",{className:"error-state",children:[s.jsx(j,{className:"error-icon"}),s.jsxs("div",{className:"error-content",children:[s.jsx("h3",{children:"Error Loading Status"}),s.jsx("p",{children:t.error}),s.jsx("button",{className:"btn btn-primary",onClick:d,children:"Try Again"})]})]}):t.connected?s.jsx("div",{className:"connected-state",children:s.jsxs("div",{className:"status-overview",children:[s.jsxs("div",{className:"status-header",children:[s.jsx(c,{className:"main-status-icon success"}),s.jsxs("div",{children:[s.jsx("h3",{children:"Stripe Express Account Connected"}),s.jsxs("p",{children:["Account ID: ",t.accountId]})]})]}),s.jsxs("div",{className:"status-grid",children:[s.jsxs("div",{className:"status-item",children:[x(t.detailsSubmitted),s.jsxs("div",{className:"status-content",children:[s.jsx("h4",{children:"Account Details"}),s.jsx("p",{children:t.detailsSubmitted?"Complete":"Incomplete"})]})]}),s.jsxs("div",{className:"status-item",children:[x(t.chargesEnabled),s.jsxs("div",{className:"status-content",children:[s.jsx("h4",{children:"Accept Payments"}),s.jsx("p",{children:t.chargesEnabled?"Enabled":"Disabled"})]})]}),s.jsxs("div",{className:"status-item",children:[x(t.payoutsEnabled),s.jsxs("div",{className:"status-content",children:[s.jsx("h4",{children:"Receive Payouts"}),s.jsx("p",{children:t.payoutsEnabled?"Enabled":"Disabled"})]})]})]}),(!t.detailsSubmitted||!t.chargesEnabled||!t.payoutsEnabled)&&s.jsxs("div",{className:"incomplete-warning",children:[s.jsx(j,{className:"warning-icon"}),s.jsxs("div",{className:"warning-content",children:[s.jsx("h4",{children:"Setup Incomplete"}),s.jsx("p",{children:"Please complete your Stripe onboarding to start receiving payments."}),s.jsx("button",{className:"btn btn-primary",onClick:b,disabled:o,children:"Complete Setup"})]})]}),t.detailsSubmitted&&t.chargesEnabled&&s.jsxs("div",{className:"dashboard-access",children:[s.jsxs("div",{className:"dashboard-info",children:[s.jsx(g,{className:"dashboard-icon"}),s.jsxs("div",{children:[s.jsx("h4",{children:"Access Your Stripe Dashboard"}),s.jsx("p",{children:"View detailed analytics, manage payouts, configure payout schedules, and access all Stripe features."})]})]}),s.jsxs("button",{className:"btn btn-primary dashboard-btn",onClick:w,children:[s.jsx(g,{}),"Open Stripe Dashboard"]})]})]})}):s.jsx("div",{className:"not-connected-state",children:s.jsxs("div",{className:"setup-prompt",children:[s.jsx("h3",{children:"🏦 Fast & Secure Payment Setup Required"}),s.jsx("p",{children:"Connect your Stripe Express account to receive payments from your content sales."}),s.jsxs("div",{className:"benefits-list",children:[s.jsxs("div",{className:"benefit-item",children:[s.jsx(c,{className:"benefit-icon"}),s.jsx("span",{children:"Stripe Express account with streamlined setup"})]}),s.jsxs("div",{className:"benefit-item",children:[s.jsx(c,{className:"benefit-icon"}),s.jsx("span",{children:"Quick onboarding process"})]}),s.jsxs("div",{className:"benefit-item",children:[s.jsx(c,{className:"benefit-icon"}),s.jsxs("span",{children:["You keep ",l.loading?"...":`${l.sellerEarnings}%`," of each sale"]})]}),s.jsxs("div",{className:"benefit-item",children:[s.jsx(c,{className:"benefit-icon"}),s.jsx("span",{children:"Automatic tax handling and reporting"})]}),s.jsxs("div",{className:"benefit-item",children:[s.jsx(c,{className:"benefit-icon"}),s.jsx("span",{children:"Built-in fraud protection"})]})]}),s.jsx("button",{className:"btn btn-stripe-connect",onClick:b,disabled:o,children:o?"Setting up...":"🔒 Setup Stripe Express Account"})]})})})]}),s.jsxs("div",{className:"settings-section",children:[s.jsx("div",{className:"section-header",children:s.jsxs("div",{className:"section-title",children:[s.jsx(P,{className:"section-icon"}),s.jsx("h2",{children:"Payment Information"})]})}),s.jsxs("div",{className:"payment-info-grid",children:[s.jsxs("div",{className:"info-card",children:[s.jsx("h3",{children:"Platform Fee"}),s.jsx("div",{className:"fee-display",children:l.loading?s.jsx("div",{className:"loading-spinner",children:"..."}):`${l.platformCommission}%`}),s.jsx("p",{children:"Deducted from each sale"})]}),s.jsxs("div",{className:"info-card",children:[s.jsx("h3",{children:"Your Earnings"}),s.jsx("div",{className:"fee-display",children:l.loading?s.jsx("div",{className:"loading-spinner",children:"..."}):`${l.sellerEarnings}%`}),s.jsx("p",{children:"Automatically transferred to your account"})]}),s.jsxs("div",{className:"info-card",children:[s.jsx("h3",{children:"Payout Schedule"}),s.jsx("div",{className:"fee-display",children:"Daily"}),s.jsx("p",{children:"Express payouts available"})]})]})]}),s.jsx("div",{className:"settings-section",children:s.jsxs("div",{className:"help-card",children:[s.jsx("h3",{children:"Need Help?"}),s.jsx("p",{children:"Having trouble with your payment setup? Contact our support team for assistance."}),s.jsxs("div",{className:"help-links",children:[s.jsxs("a",{href:"/contact",className:"help-link",children:["Contact Support ",s.jsx(N,{})]}),s.jsxs("a",{href:"https://stripe.com/docs/connect",target:"_blank",rel:"noopener noreferrer",className:"help-link",children:["Stripe Documentation ",s.jsx(N,{})]})]})]})})]})})};export{O as default};
