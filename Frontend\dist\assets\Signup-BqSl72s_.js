import{c as T,b as C,d as S,r as h,j as e,w as _,x as w,L as N,o as f,y as k,t as o,q as P,v as E,z as q}from"./index-ctFdmWBt.js";import{P as F,G,f as y,v as I}from"./firebaseService-Bi10J-DQ.js";const A=()=>{const l=T(),c=C(),{isLoading:d,isError:$,isSuccess:D,error:L}=S(s=>s.auth),[a,g]=h.useState({firstName:"",lastName:"",email:"",phone:"",countryCode:"+91",accountType:"learn",agreeToTerms:!1}),[t,p]=h.useState({}),u=s=>{const{name:r,value:i,type:n,checked:m}=s.target;g({...a,[r]:n==="checkbox"?m:i}),t[r]&&p({...t,[r]:null})},x=s=>{g({...a,countryCode:s.target.value})},j=()=>{const s={};a.firstName.trim()||(s.firstName="First name is required"),a.lastName.trim()||(s.lastName="Last name is required"),a.email.trim()?/\S+@\S+\.\S+/.test(a.email)||(s.email="Email is invalid"):s.email="Email is required";const r=I(a.countryCode,a.phone);return r.isValid||(s.phone=r.error),a.agreeToTerms||(s.agreeToTerms="You must agree to the terms and conditions"),s},v=async s=>{s.preventDefault();const r=j();if(Object.keys(r).length>0){p(r);return}c(f());try{const i={firstName:a.firstName,lastName:a.lastName,email:a.email,mobile:`${a.countryCode}${a.phone}`,role:a.accountType==="learn"?"buyer":"seller"},n=await c(k(i)).unwrap();o.auth.registrationSuccess(),l("/otp-verification",{state:{userId:n.userId,phoneNumber:`${a.countryCode} ${a.phone}`,cooldownSeconds:n.cooldownSeconds||60,isLogin:!1,developmentOtp:n.developmentOtp}})}catch(i){console.error("Registration error:",i);let n="Registration failed. Please try again.";typeof i=="string"?n=i:i!=null&&i.message&&(n=i.message),n.includes("Email already registered")?o.error("This email is already registered. Please try logging in instead."):n.includes("Mobile number already registered")?o.error("This mobile number is already registered. Please try logging in instead."):n.includes("already registered")?o.error("This email or mobile number is already registered. Please try logging in instead."):o.error(n)}},b=async()=>{try{if(c(f()),!y.isInitialized()){o.error("Firebase is not initialized. Please check your configuration.");return}const s=await y.signInWithGoogle();try{const r=await c(P(s.idToken)).unwrap();if(o.info("Account already exists. Redirecting to dashboard..."),r.user.role==="buyer")l("/content");else if(r.user.role==="seller"){const i=E(r.user);l(i)}else r.user.role==="admin"?l("/admin/dashboard"):l("/")}catch(r){const i=typeof r=="string"?r:(r==null?void 0:r.message)||"";if(i.includes("not found")||i.includes("does not exist")){const n="buyer";try{await c(q({idToken:s.idToken,role:n})).unwrap(),o.auth.registrationSuccess(),n==="buyer"&&l("/content")}catch(m){throw m}}else throw r}}catch(s){console.error("Google sign-up error:",s);let r="Failed to sign up with Google. Please try again.";typeof s=="string"?r=s:s!=null&&s.message&&(r=s.message),o.error(r)}};return e.jsx("div",{className:"signup__page",children:e.jsxs("div",{className:"signup__container",children:[e.jsx("h1",{className:"signup__title",children:"Sign up to your account"}),e.jsxs("form",{onSubmit:v,className:"signup__form",children:[e.jsxs("div",{className:"signup__form-row",children:[e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(_,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:a.firstName,onChange:u,placeholder:"First Name",className:`signup__input ${t.firstName?"signup__input--error":""}`,required:!0}),t.firstName&&e.jsx("p",{className:"signup__error",children:t.firstName})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(_,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:a.lastName,onChange:u,placeholder:"Last Name",className:`signup__input ${t.lastName?"signup__input--error":""}`,required:!0}),t.lastName&&e.jsx("p",{className:"signup__error",children:t.lastName})]})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(w,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:a.email,onChange:u,placeholder:"Enter Email Address",className:`signup__input ${t.email?"signup__input--error":""}`,required:!0}),t.email&&e.jsx("p",{className:"signup__error",children:t.email})]}),e.jsx(F,{countryCode:a.countryCode,phone:a.phone,onCountryCodeChange:x,onPhoneChange:u,error:t.phone,placeholder:"00000 00000",className:"signup__phone-container",required:!0,name:"phone"}),e.jsxs("div",{className:"signup__terms",children:[e.jsx("input",{type:"checkbox",id:"agreeToTerms",name:"agreeToTerms",checked:a.agreeToTerms,onChange:u,className:"signup__checkbox"}),e.jsxs("label",{htmlFor:"agreeToTerms",className:"signup__terms-label",children:["By sign up you agree to our"," ",e.jsx(N,{to:"/terms",className:"signup__terms-link",children:"Terms & Conditions"})]}),t.agreeToTerms&&e.jsx("p",{className:"signup__error",children:t.agreeToTerms})]}),e.jsx("button",{type:"submit",className:"signup__button btn-primary",disabled:d,children:d?"Creating Account...":"Create Your Account"}),e.jsx("div",{className:"signup__divider",children:e.jsx("span",{children:"or"})}),e.jsx(G,{onClick:b,isLoading:d,text:"Sign up with Google",variant:"secondary"}),e.jsxs("p",{className:"signup__login-link mt-10",children:["Do you have an account?"," ",e.jsx(N,{to:"/auth",className:"signup__link",children:"Sign In"})]})]})]})})};export{A as default};
