import{r as s,j as e,av as M,aw as O,ag as H,ax as V}from"./index-ctFdmWBt.js";const Y=({fileUrl:n,title:m="PDF Document",className:w="",height:v="100%",showDownload:D=!1,onDownload:h=null,showFallbackOptions:F=!0})=>{const[a,E]=s.useState(!1),[b,P]=s.useState(!1),[f,k]=s.useState(!1),[g,l]=s.useState(!1),[y,c]=s.useState(!0),[d,T]=s.useState(0),[_,p]=s.useState(!1),[x,j]=s.useState(!1),N=s.useRef(null),i=s.useRef(null),o=s.useRef(!0);s.useEffect(()=>(o.current=!0,()=>{o.current=!1,i.current&&clearTimeout(i.current)}),[]),s.useEffect(()=>{(async()=>{try{const t=navigator.userAgent,$=/Android/i.test(t),A=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)||window.innerWidth<=768;let u=!1;try{navigator.brave&&typeof navigator.brave.isBrave=="function"?u=await navigator.brave.isBrave():/Brave/i.test(t)?u=!0:window.chrome&&window.chrome.runtime&&window.chrome.runtime.onConnect&&(u=!window.chrome.webstore)}catch(z){console.warn("Brave detection failed:",z),u=!1}o.current&&(E($),P(A),k(u),j(!0))}catch(t){console.error("Environment detection failed:",t),o.current&&j(!0)}})()},[]),s.useEffect(()=>{if(!(!n||!x))return i.current&&clearTimeout(i.current),o.current&&(p(!1),l(!1),c(!0)),i.current=setTimeout(()=>{o.current&&y&&!g&&(console.log("PDF load timeout - likely blocked by browser security"),p(!0),l(!0),c(!1))},8e3),()=>{i.current&&clearTimeout(i.current)}},[n,d,x]);const S=s.useCallback(()=>{if(!n)return"";try{const r=new URL(n);d>0&&r.searchParams.set("t",Date.now().toString());let t="toolbar=0&navpanes=0&view=FitH";return a?t+="&scrollbar=1&zoom=page-fit&embedded=true":b&&(t+="&scrollbar=1&zoom=page-fit"),`${r.toString()}#${t}`}catch(r){console.warn("URL parsing failed, using fallback:",r);const t=d>0?`&t=${Date.now()}`:"";return`${n}#toolbar=0&navpanes=0&view=FitH${t}`}},[n,d,a,b]),C=s.useCallback(()=>{if(o.current)try{i.current&&clearTimeout(i.current),c(!1),l(!1),p(!1),console.log("PDF loaded successfully")}catch(r){console.error("Error in handleLoad:",r)}},[]),B=s.useCallback(()=>{if(o.current)try{i.current&&clearTimeout(i.current),c(!1),l(!0),console.log("PDF preview failed to load")}catch(r){console.error("Error in handleError:",r)}},[]),R=s.useCallback(()=>{if(o.current)try{l(!1),p(!1),c(!0),T(r=>r+1)}catch(r){console.error("Error in handleRetry:",r)}},[]),I=s.useCallback(()=>{try{n&&window.open(n,"_blank","noopener,noreferrer")}catch(r){console.error("Error opening PDF in new tab:",r)}},[n]),L=s.useCallback(()=>{try{if(h)h();else if(n){const r=document.createElement("a");r.href=n,r.download=m||"document.pdf",r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}catch(r){console.error("Error downloading PDF:",r)}},[n,m,h]);if(!x)return e.jsx("div",{className:`simple-pdf-viewer ${w}`,style:{height:v},children:e.jsxs("div",{className:"simple-pdf-viewer__loading",children:[e.jsx("div",{className:"simple-pdf-viewer__spinner"}),e.jsx("p",{children:"Initializing PDF viewer..."})]})});if(g&&F){const r=_?"PDF preview was blocked by your browser's security settings":"Unable to load the PDF preview",t=f?"Brave browser blocks PDF previews by default for security. Use the options below to access the document:":_?"This usually happens when browsers block embedded content. Try the alternatives below:":"Try the options below to access the document:";return e.jsx("div",{className:`simple-pdf-viewer ${w} simple-pdf-viewer--error`,style:{height:v},children:e.jsxs("div",{className:"simple-pdf-viewer__error",children:[e.jsx("div",{className:"simple-pdf-viewer__error-icon",children:f?e.jsx(M,{}):e.jsx(O,{})}),e.jsx("h3",{children:r}),e.jsx("p",{children:t}),f&&e.jsxs("div",{className:"simple-pdf-viewer__brave-info",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"For Brave users:"})," You can enable PDF previews by:"]}),e.jsxs("ol",{children:[e.jsx("li",{children:"Clicking the Brave shield icon in the address bar"}),e.jsx("li",{children:'Turning off "Block scripts" for this site'}),e.jsx("li",{children:"Refreshing the page"})]})]}),e.jsxs("div",{className:"simple-pdf-viewer__error-actions",children:[e.jsxs("button",{className:"simple-pdf-viewer__btn simple-pdf-viewer__btn--primary",onClick:I,title:"Opens PDF in a new browser tab",children:[e.jsx(H,{})," Open in New Tab"]}),e.jsxs("button",{className:"simple-pdf-viewer__btn simple-pdf-viewer__btn--secondary",onClick:R,title:"Try loading the preview again",children:[e.jsx(V,{})," Retry Preview"]}),D&&e.jsx("button",{className:"simple-pdf-viewer__btn simple-pdf-viewer__btn--tertiary",onClick:L,title:"Download the PDF file",children:"Download PDF"})]})]})})}return e.jsxs("div",{className:`simple-pdf-viewer ${w} ${a?"simple-pdf-viewer--android":""}`,style:{height:v},children:[y&&e.jsxs("div",{className:"simple-pdf-viewer__loading",children:[e.jsx("div",{className:"simple-pdf-viewer__spinner"}),e.jsx("p",{children:"Loading PDF..."})]}),e.jsx("div",{className:"simple-pdf-viewer__content",children:e.jsx("iframe",{ref:N,src:S(),className:`simple-pdf-viewer__iframe ${a?"simple-pdf-viewer__iframe--android":""}`,title:m,loading:"lazy",onError:B,onLoad:C,"data-mobile":b?"true":"false","data-android":a?"true":"false","data-brave":f?"true":"false",allowFullScreen:!1,sandbox:"allow-same-origin allow-scripts allow-popups",style:{pointerEvents:"auto",touchAction:"pan-x pan-y zoom",border:"none",width:"100%",height:"100%",opacity:y?0:1,transition:"opacity 0.3s ease",overflow:"auto"}},d)})]})};export{Y as S};
