import{r as i,j as e,K as N,L as p,m as S}from"./index-ctFdmWBt.js";const B=({image:y,title:f,coach:m,price:c,id:d,type:D="buy",saleType:o="Fixed",auctionDetails:r={},isSold:w=!1,contentType:l="Document"})=>{const[u,g]=i.useState([]),[b,v]=i.useState(!0),[h,j]=i.useState(null);i.useEffect(()=>{(async()=>{var t,a;try{if(d){const x=await S.get(`/reviews/content/${d}`);g(x.data.data)}}catch(x){j(((a=(t=x.response)==null?void 0:t.data)==null?void 0:a.message)||"Failed to fetch reviews")}finally{v(!1)}})()},[d]);const n=(()=>{if(w)return{text:"SOLD",type:"sold",disabled:!0};if(o==="Fixed")return{text:"Buy Now",type:"buy",disabled:!1};if(o==="Auction"){const s=new Date,t=r.auctionStartDate?new Date(r.auctionStartDate):null,a=r.auctionEndDate?new Date(r.auctionEndDate):null;return t&&s<t?{text:"Bid Now",type:"bid",disabled:!1}:t&&a&&s>=t&&s<=a?{text:"Bid Now",type:"bid",disabled:!1}:a&&s>a?{text:"Auction Ended",type:"ended",disabled:!0}:{text:"Bid Now",type:"bid",disabled:!1}}return{text:"View Details",type:"view",disabled:!1}})();return e.jsxs("div",{className:"strategy-card-component strategy-card",children:[e.jsxs("div",{className:"strategy-card-image",children:[e.jsx("img",{src:y,alt:f}),e.jsx("div",{className:"content-type-label",children:(l==null?void 0:l.toLowerCase())==="video"?"Video":"Document"})]}),e.jsxs("div",{className:"strategy-card-content",children:[e.jsx("h3",{className:"strategy-card-title",children:f}),e.jsxs("div",{className:"strategy-card-coach-and-review",children:[e.jsxs("p",{className:"strategy-card-coach",children:["By ",m]}),!b&&!h&&u.length>0&&e.jsxs("div",{className:"strategy-card-review-overlay",children:[e.jsx("span",{className:"review-rating",children:(u.reduce((s,t)=>s+(t.rating||0),0)/u.length).toFixed(1)}),e.jsx(N,{style:{color:"#ffd700",fontSize:"1.1rem",marginLeft:2}})]})]}),e.jsxs("div",{className:"strategy-card-footer",children:[e.jsx("span",{className:"strategy-card-price",children:o==="Auction"&&r.basePrice?`$${r.basePrice.toFixed(2)}`:`$${(c==null?void 0:c.toFixed(2))||"0.00"}`}),n.disabled?e.jsx("button",{className:`action-button action-button--${n.type}`,disabled:!0,children:n.text}):e.jsx(p,{to:`/buyer/details/${d}`,className:`action-button--${n.type}`,children:n.text})]})]})]})};export{B as S};
