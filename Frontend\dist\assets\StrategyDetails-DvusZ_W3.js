import{a as Y,c as G,b as H,d as J,r as n,g as w,N as o,j as e,an as K,ao as Q,M as W,h as _,as as X,at as U,au as Z,aq as ee}from"./index-ctFdmWBt.js";import{S as D}from"./SellerLayout-EbrVdrvL.js";import{D as te}from"./DocumentViewer-CrWHT6xN.js";import{P as se}from"./PreviewModal-bMzyFP6E.js";import{o as P}from"./ourmissionimage-imuutOi1.js";import{C as ae}from"./ConfirmationModal-3QP_xvbZ.js";import{P as j}from"./PreviewContent-HZYJfZIk.js";import{f as le}from"./dateValidation-cL5kH0gD.js";import"./SimplePDFViewer-RiyMKPYj.js";import"./timezoneUtils-BuH33ask.js";const he=()=>{var f,b;const{id:r}=Y(),g=G(),d=H(),{singleContent:a,isLoading:C,error:p}=J(s=>s.content),[E,y]=n.useState(!1),[F,v]=n.useState(!1),[L,N]=n.useState(!1),[T,h]=n.useState(!1),[x,V]=n.useState(!1),[S,ie]=n.useState(!1),[M,k]=n.useState(null);n.useEffect(()=>{r&&(!a||a._id!==r)&&d(w(r))},[d,r,a]),n.useEffect(()=>{const s=()=>{const l=sessionStorage.getItem("previousPath");r&&document.visibilityState==="visible"&&l&&l.includes("/edit")&&(d(w(r)),sessionStorage.removeItem("previousPath"))};return document.addEventListener("visibilitychange",s),()=>{document.removeEventListener("visibilitychange",s)}},[d,r]),n.useEffect(()=>{p&&o.error(p.message||"Failed to load strategy details")},[p]);const I=()=>{sessionStorage.setItem("previousPath",`/seller/strategy-details/${r}/edit`),g(`/seller/strategy-details/${r}/edit`)},$=()=>{y(!0)},q=async()=>{v(!0);try{await d(ee(r)).unwrap(),o.success("Strategy deleted successfully"),g("/seller/my-sports-strategies")}catch(s){console.error("Delete failed:",s),o.error(s.message||"Failed to delete strategy")}finally{v(!1),y(!1)}},O=()=>{if(a!=null&&a.fileUrl){const s=a.fileUrl.split(".").pop().toLowerCase();["mp4","avi","mov","wmv","flv","webm","mkv"].includes(s)?(V(!x),x||h(!0)):A()}else o.error("No file available")},A=()=>{o.error("Download functionality has been disabled for security purposes")},u=()=>{if(!(a!=null&&a.fileUrl))return!1;const s=a.fileUrl.split(".").pop().toLowerCase();return["mp4","avi","mov","wmv","flv","webm","mkv"].includes(s)},B=s=>le(s),z=s=>{var i;let l;return(s==null?void 0:s.saleType)==="Auction"&&((i=s==null?void 0:s.auctionDetails)!=null&&i.basePrice)?l=s.auctionDetails.basePrice:l=s==null?void 0:s.price,typeof l=="number"?`$${l.toFixed(2)}`:"$0.00"},R=s=>{if(!s)return"N/A";const l=Math.floor(s/60),i=s%60;return l>0?`${l}:${i.toString().padStart(2,"0")}:00`:`${i}:00`},c=()=>{var i;if(!a||a.saleType!=="Auction")return!1;const s=new Date,l=(i=a.auctionDetails)!=null&&i.auctionStartDate?new Date(a.auctionDetails.auctionStartDate):null;return l&&s>=l};if(C)return e.jsx(D,{children:e.jsx("div",{className:"StrategyDetails",children:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading strategy details..."})]})})});if(!a)return e.jsx(D,{children:e.jsx("div",{className:"StrategyDetails",children:e.jsxs("div",{className:"error-container",children:[e.jsx("h3",{children:"Strategy not found"}),e.jsx("p",{children:"The strategy you're looking for doesn't exist or has been removed."}),e.jsx("button",{className:"btn btn-primary",onClick:()=>g("/seller/my-sports-strategies"),children:"Back to Strategies"})]})})});const t=a;return e.jsx(D,{children:e.jsxs("div",{className:"StrategyDetails",children:[e.jsxs("div",{className:"StrategyDetails__info-header",children:[e.jsx("div",{className:"StrategyDetails__info-content",children:e.jsx("h2",{className:"StrategyDetails__info-title",children:"Video/Document Info"})}),e.jsxs("div",{className:"StrategyDetails__info-actions",children:[e.jsxs("button",{className:`StrategyDetails__edit-btn ${c()?"disabled":""}`,onClick:c()?void 0:I,disabled:c(),title:c()?"You can't edit the strategy once the auction has started.":"Edit Strategy",style:{cursor:c()?"not-allowed":"pointer",opacity:c()?.6:1},children:[e.jsx(K,{className:"StrategyDetails__action-icon"}),"Edit"]}),e.jsxs("button",{className:"StrategyDetails__delete-btn",onClick:$,children:[e.jsx(Q,{className:"StrategyDetails__action-icon"}),"Delete"]})]})]}),e.jsxs("div",{className:"StrategyDetails__content",children:[e.jsxs("div",{className:"StrategyDetails__content-header",children:[e.jsx("h3",{className:"StrategyDetails__strategy-title",children:t.title}),t.fileUrl&&e.jsxs("button",{className:"btn-outline StrategyDetails__previewBtn",onClick:()=>N(!0),title:"Preview Document/Video in Full Screen",children:[e.jsx(W,{}),"Preview"]})]}),e.jsx("div",{className:"StrategyDetails__media-container",children:x&&u()?e.jsx("div",{className:"StrategyDetails__video-wrapper",children:e.jsxs("video",{className:"StrategyDetails__video",controls:!0,autoPlay:T,controlsList:"nodownload nofullscreen noremoteplayback",disablePictureInPicture:!0,onPlay:()=>h(!0),onPause:()=>h(!1),onError:s=>{console.error("Video error:",s);const l=s.target.src,i=_(S?t.fileUrl:t.previewUrl||t.fileUrl),m=X(S?t.fileUrl:t.previewUrl||t.fileUrl);l===i&&m&&m!==i?(k(m),s.target.src=m,s.target.load()):o.error("Failed to load video. Please try again.")},onLoadStart:()=>console.log("Video loading started"),onCanPlay:()=>console.log("Video can play"),poster:_(t.thumbnailUrl||t.previewUrl)||P,children:[e.jsx("source",{src:M||_(S?t.fileUrl:t.previewUrl||t.fileUrl),type:"video/mp4"}),"Your browser does not support the video tag."]})}):t.contentType==="PDF"||t.contentType==="Document"?e.jsx("div",{className:"StrategyDetails__document-container",children:e.jsx("div",{className:"StrategyDetails__document-viewer",children:e.jsx(te,{fileUrl:t.fileUrl,fileName:((f=t.fileUrl)==null?void 0:f.split("/").pop())||"document",title:t.title,className:"StrategyDetails__document-element",height:"500px",showDownload:!1})})}):e.jsxs("div",{className:"StrategyDetails__image-container",children:[e.jsx("img",{src:_(t.thumbnailUrl||t.previewUrl)||P,alt:t.title,className:"StrategyDetails__image"}),e.jsx("div",{className:"StrategyDetails__play-overlay",onClick:O,title:u()?t.previewUrl&&t.previewUrl!==t.fileUrl?"Play Preview (10 seconds)":"Play Video":t.fileUrl?"Download File":"No file available",children:t.fileUrl?e.jsx(e.Fragment,{children:u()?e.jsxs(e.Fragment,{children:[e.jsx(U,{className:"StrategyDetails__play-icon"}),e.jsx("span",{className:"StrategyDetails__overlay-text",children:t.previewUrl&&t.previewUrl!==t.fileUrl?"Play Preview":"Play Video"})]}):e.jsxs(e.Fragment,{children:[e.jsx(Z,{className:"StrategyDetails__play-icon"}),e.jsx("span",{className:"StrategyDetails__overlay-text",children:"Download"})]})}):e.jsx(U,{className:"StrategyDetails__play-icon"})})]})}),e.jsxs("div",{className:"StrategyDetails__description-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"Description"}),e.jsx(j,{html:t.description,className:"StrategyDetails__description",ariaLabel:"Strategy description preview"})]}),e.jsxs("div",{className:"StrategyDetails__coach-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"The Coach"}),e.jsx("div",{className:"StrategyDetails__coach-info",children:e.jsxs("div",{className:"StrategyDetails__coach-details",children:[e.jsx("h4",{className:"StrategyDetails__coach-name",children:t.coachName||"Coach Name"}),e.jsxs("p",{className:"StrategyDetails__coach-title",children:[t.sport," Specialist"]}),e.jsx(j,{html:t.aboutCoach,className:"StrategyDetails__coach-description",ariaLabel:"Coach description preview"})]})})]}),e.jsxs("div",{className:"StrategyDetails__stats-and-steps",children:[e.jsxs("div",{className:"StrategyDetails__stats-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"Strategic Content Info"}),e.jsxs("div",{className:"StrategyDetails__stats-grid",children:[e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Category"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.category})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Sport"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.sport})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Duration"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:R(t.duration)})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Price"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:z(t)})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Content Type"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.contentType})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Difficulty"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.difficulty})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Status"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.status})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Created"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:B(t.createdAt)})]})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsxs("div",{className:"StrategyDetails__steps-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"This strategic content Includes"}),e.jsxs("div",{className:"StrategyDetails__steps-list",children:[e.jsx("div",{className:"StrategyDetails__step",children:e.jsx(j,{html:t.strategicContent,className:"StrategyDetails__step-text",ariaLabel:"Strategic content preview"})}),t.tags&&t.tags.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Tags:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.tags.join(", ")})]}),t.prerequisites&&t.prerequisites.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Prerequisites:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.prerequisites.join(", ")})]}),t.learningObjectives&&t.learningObjectives.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Learning Objectives:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.learningObjectives.join(", ")})]}),t.equipment&&t.equipment.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Equipment Needed:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.equipment.join(", ")})]}),e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Language:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.language})]}),e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Sale Type:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.saleType})]}),t.fileSize&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"File Size:"}),e.jsxs("span",{className:"StrategyDetails__step-text",children:[(t.fileSize/1024/1024).toFixed(2)," MB"]})]})]})]})]})]}),e.jsx(ae,{isOpen:E,onClose:()=>y(!1),onConfirm:q,title:"Delete Strategy",message:`Are you sure you want to delete "${t==null?void 0:t.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",type:"danger",isLoading:F}),e.jsx(se,{isOpen:L,onClose:()=>N(!1),fileUrl:_(t.fileUrl),fileName:((b=t.fileUrl)==null?void 0:b.split("/").pop())||t.title,title:t.title,contentType:t.contentType})]})})};export{he as default};
