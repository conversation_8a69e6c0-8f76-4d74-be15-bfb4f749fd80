const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jquery-BPSN-Ou5.js","assets/index-ctFdmWBt.js","assets/index-CieivHBP.css","assets/jquery-BQXThELV.js","assets/summernote-lite-CeJbJNZW.js"])))=>i.map(i=>d[i]);
import{r as o,j as E,_ as I}from"./index-ctFdmWBt.js";const T=({value:e="",onChange:l,placeholder:w="Enter text...",height:S=350,className:y="",disabled:b=!1,contentKey:V=""})=>{const c=o.useRef(null),t=o.useRef(null),[i,_]=o.useState(!1),[d,p]=o.useState(e),[a,m]=o.useState(!1),[u,f]=o.useState(!1);return o.useEffect(()=>((async()=>{try{const r=(await I(async()=>{const{default:n}=await import("./jquery-BPSN-Ou5.js").then(x=>x.j);return{default:n}},__vite__mapDeps([0,1,2,3]))).default;window.$=r,window.jQuery=r,await I(()=>import("./summernote-lite-CeJbJNZW.js").then(n=>n.s),__vite__mapDeps([4,1,2,3])),c.current&&!t.current&&(r(c.current).summernote({height:S,placeholder:w,toolbar:[["style",["style"]],["font",["bold","underline","clear"]],["fontname",["fontname"]],["para",["ul","ol","paragraph"]],["table",["table"]],["insert",["link","picture","video"]],["view",["fullscreen","codeview"]]],callbacks:{onChange:function(n){m(!0),l==null||l(n),setTimeout(()=>m(!1),1e3)},onFocus:function(){m(!0)},onBlur:function(){setTimeout(()=>m(!1),100)}}}),t.current=r(c.current),_(!0),setTimeout(()=>{const n=d||e||"";n&&!u&&(r(c.current).summernote("code",n),f(!0)),p("")},100),b&&r(c.current).summernote("disable"))}catch(r){console.error("Failed to load Summernote:",r)}})(),()=>{if(t.current)try{t.current.summernote("destroy"),t.current=null}catch(r){console.error("Error destroying Summernote:",r)}_(!1),m(!1),f(!1)}),[V]),o.useEffect(()=>{if(e!=null&&!a)if(i&&t.current&&u){const s=t.current.summernote("code");s!==e&&!s.includes(e)&&!e.includes(s)&&t.current.summernote("code",e)}else i||p(e)},[e,i,a,u]),o.useEffect(()=>{if(i&&t.current&&e&&e.trim()!==""&&!a&&!u){const s=t.current.summernote("code");s!==e&&(s.trim()===""||s==="<p><br></p>")&&(t.current.summernote("code",e),f(!0))}},[i,e,a,u]),o.useEffect(()=>{if(i&&t.current&&!a&&!u){const s=setTimeout(()=>{const r=t.current.summernote("code"),n=e||d;n&&r!==n&&(r.trim()===""||r==="<p><br></p>")&&(t.current.summernote("code",n),f(!0))},1e3);return()=>clearTimeout(s)}},[i,e,d,a,u]),E.jsx("div",{className:`summernote-wrapper ${y}`,children:E.jsx("div",{ref:c})})};export{T as S};
