import{j as s}from"./index-ctFdmWBt.js";const $=({columns:r,data:t,onRowClick:d,renderRow:i,className:b="",emptyMessage:j="No data available",variant:n="default",gridTemplate:c="",isAdmin:N=!1,loading:h={isLoading:!1,message:"Loading..."},onSort:x,sortConfig:g={key:null,direction:null}})=>{const p=e=>{d&&d(e)},y=e=>{x&&x(e)},k=`table-container ${n} ${N?"admin-table":""} ${b}`;return s.jsx("div",{className:k,children:n==="grid"?s.jsxs("div",{className:`table ${b}`,children:[s.jsx("div",{className:"table-header",style:c?{gridTemplateColumns:c}:{},children:r.map(e=>s.jsx("div",{className:`table-cell ${e.className||""} ${e.sortable?"sortable":""}`,onClick:()=>e.sortable&&y(e.key),style:{cursor:e.sortable?"pointer":"default"},children:e.label},e.key))}),h.isLoading?s.jsx("div",{className:"table-row empty-row",children:s.jsx("div",{className:"table-cell full-span empty-message",children:h.message})}):t.length>0?t.map((e,l)=>s.jsx("div",{className:`table-row ${d?"clickable":""}`,style:c?{gridTemplateColumns:c}:{},onClick:()=>p(e),children:i?i(e,l):r.map(a=>s.jsx("div",{className:`table-cell ${a.className||""}`,children:a.render?a.render(e,l):e[a.key]},a.key))},e.id||l)):s.jsx("div",{className:"table-row empty-row",children:s.jsx("div",{className:"table-cell full-span empty-message",children:j})})]}):s.jsxs("table",{className:`table ${b}`,children:[s.jsx("thead",{children:s.jsx("tr",{children:r.map(e=>s.jsx("th",{className:`${e.className||""} ${e.sortable?"sortable":""}`,onClick:()=>e.sortable&&y(e.key),style:{cursor:e.sortable?"pointer":"default"},children:e.label},e.key))})}),s.jsx("tbody",{children:h.isLoading?s.jsx("tr",{children:s.jsx("td",{colSpan:r.length,className:"empty-message",children:h.message})}):t.length>0?t.map((e,l)=>s.jsx("tr",{onClick:()=>p(e),className:d?"clickable":"",children:i?i(e,l):r.map(a=>s.jsx("td",{className:a.className||"",children:a.render?a.render(e,l):e[a.key]},a.key))},e.id||l)):s.jsx("tr",{children:s.jsx("td",{colSpan:r.length,className:"empty-message",children:j})})})]})})};export{$ as T};
