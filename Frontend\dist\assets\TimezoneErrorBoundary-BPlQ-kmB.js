var m=Object.defineProperty;var u=(s,r,i)=>r in s?m(s,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[r]=i;var c=(s,r,i)=>u(s,typeof r!="symbol"?r+"":r,i);import{j as e,dc as x}from"./index-ctFdmWBt.js";import{g as l,b as d,v as f}from"./timezoneUtils-BuH33ask.js";const N=({progress:s=0,isVisible:r=!1,fileName:i="",uploadType:o="file"})=>r?e.jsx("div",{className:"upload-progress-overlay",children:e.jsxs("div",{className:"upload-progress-container",children:[e.jsxs("div",{className:"upload-progress-header",children:[e.jsxs("h4",{className:"upload-progress-title",children:["Uploading ",o,"..."]}),i&&e.jsx("p",{className:"upload-progress-filename",children:i})]}),e.jsxs("div",{className:"upload-progress-bar-container",children:[e.jsx("div",{className:"upload-progress-bar",children:e.jsx("div",{className:"upload-progress-fill",style:{width:`${s}%`}})}),e.jsxs("span",{className:"upload-progress-percentage",children:[Math.round(s),"%"]})]}),e.jsx("div",{className:"upload-progress-status",children:s<100?e.jsxs("p",{className:"upload-progress-message",children:["Please wait while your ",o," is being uploaded..."]}):e.jsx("p",{className:"upload-progress-message upload-progress-complete",children:"Upload completed! Processing..."})})]})}):null,p={Video:[".mp4",".mov",".avi",".webm"],Document:[".pdf"]},h={Video:["video/mp4","video/quicktime","video/x-msvideo","video/webm"],Document:["application/pdf"]},g={Video:1024*1024*1024,Document:50*1024*1024},j=s=>{if(!s)return"";const r=s.lastIndexOf(".");return r===-1?"":s.substring(r).toLowerCase()},w=(s,r)=>{if(!s)return{isValid:!1,message:"No file selected"};if(!r)return{isValid:!1,message:"Please select a content type first"};const i=j(s.name),o=p[r],t=h[r],n=g[r];if(s.size>n)return{isValid:!1,message:`File size exceeds the maximum limit of ${n/1048576}MB for ${r.toLowerCase()} files`};if(!o.includes(i)){const a=o.join(", ").toUpperCase();return{isValid:!1,message:`Invalid file format for ${r}. Supported formats: ${a}`}}return t.includes(s.type)?{isValid:!0,message:"File validation successful"}:{isValid:!1,message:`Invalid file type for ${r}. Please select a valid ${r.toLowerCase()} file.`}},y=s=>s?p[s].join(","):"",E=s=>!s,S=()=>{const s=l(),r=d();return e.jsxs("div",{className:"timezone-info",style:{fontSize:"0.8rem",color:"#666",marginTop:"4px"},children:["All times are in your local timezone: ",s," (",r,")"]})};class I extends x.Component{constructor(i){super(i);c(this,"handleRetry",()=>{this.setState({hasError:!1,errorInfo:null})});this.state={hasError:!1,errorInfo:null,timezoneSupported:!0}}static getDerivedStateFromError(i){var t,n,a;return{hasError:!0,isTimezoneError:((t=i.message)==null?void 0:t.includes("timezone"))||((n=i.message)==null?void 0:n.includes("Intl"))||((a=i.message)==null?void 0:a.includes("locale"))}}componentDidCatch(i,o){console.error("Timezone Error Boundary caught an error:",i,o);try{const t=l(),n=d(),a=f(t)}catch(t){console.error("Failed to log timezone environment:",t)}this.setState({errorInfo:o,timezoneSupported:this.checkTimezoneSupport()})}checkTimezoneSupport(){try{return typeof Intl>"u"||typeof Intl.DateTimeFormat>"u"?!1:(new Date().toLocaleString("en-US",{timeZone:"America/New_York"}),!0)}catch{return!1}}render(){return this.state.hasError?e.jsxs("div",{className:"timezone-error-boundary",children:[e.jsxs("div",{className:"error-card",children:[e.jsx("h2",{children:"🌍 Timezone Error"}),this.state.timezoneSupported?e.jsxs("div",{className:"timezone-error-details",children:[e.jsx("p",{children:"There was an issue with date/timezone handling."}),e.jsxs("details",{className:"error-details",children:[e.jsx("summary",{children:"Technical Details"}),e.jsxs("div",{className:"timezone-info",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Your Timezone:"})," ",l()]}),e.jsxs("p",{children:[e.jsx("strong",{children:"UTC Offset:"})," ",d()]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Browser:"})," ",navigator.userAgent]})]}),this.state.errorInfo&&e.jsx("pre",{className:"error-stack",children:this.state.errorInfo.componentStack})]})]}):e.jsxs("div",{className:"browser-support-error",children:[e.jsx("p",{children:"Your browser doesn't fully support timezone functionality."}),e.jsxs("div",{className:"suggestions",children:[e.jsx("h4",{children:"Suggestions:"}),e.jsxs("ul",{children:[e.jsx("li",{children:"Update your browser to the latest version"}),e.jsx("li",{children:"Try using Chrome, Firefox, or Safari"}),e.jsx("li",{children:"Enable JavaScript if it's disabled"})]})]})]}),e.jsxs("div",{className:"error-actions",children:[e.jsx("button",{className:"btn btn-primary",onClick:this.handleRetry,children:"Try Again"}),e.jsx("button",{className:"btn btn-outline",onClick:()=>window.location.reload(),children:"Refresh Page"})]})]}),e.jsx("style",{jsx:!0,children:`
            .timezone-error-boundary {
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 300px;
              padding: 20px;
            }
            
            .error-card {
              background: #fff;
              border: 1px solid #ddd;
              border-radius: 8px;
              padding: 24px;
              max-width: 600px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              text-align: center;
            }
            
            .error-card h2 {
              color: #d32f2f;
              margin-bottom: 16px;
            }
            
            .suggestions ul {
              text-align: left;
              margin: 12px 0;
            }
            
            .error-details {
              text-align: left;
              margin: 16px 0;
            }
            
            .timezone-info {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              margin: 8px 0;
            }
            
            .error-stack {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              overflow-x: auto;
              font-size: 12px;
              margin: 8px 0;
            }
            
            .error-actions {
              margin-top: 20px;
              display: flex;
              gap: 12px;
              justify-content: center;
            }
            
            .btn {
              padding: 8px 16px;
              border-radius: 4px;
              border: none;
              cursor: pointer;
              font-weight: 500;
            }
            
            .btn-primary {
              background: #1976d2;
              color: white;
            }
            
            .btn-outline {
              background: transparent;
              color: #1976d2;
              border: 1px solid #1976d2;
            }
            
            .btn:hover {
              opacity: 0.9;
            }
          `})]}):this.props.children}}export{g as F,I as T,N as U,S as a,y as g,E as i,w as v};
