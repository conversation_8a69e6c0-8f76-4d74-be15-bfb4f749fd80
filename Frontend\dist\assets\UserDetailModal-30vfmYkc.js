import{b as W,d as $,b3 as X,b4 as Z,r as u,j as e,b5 as z,aW as K,b6 as Q,aB as U,aV as ee,aw as E,b7 as p,b8 as se,aX as ae,aY as te,b9 as k,ba as ie,aZ as le,bb as ne,bc as A,bd as g,be as re,bf as I,bg as x,bh as P,bi as y,bj as M,k as S,bk as ce}from"./index-ctFdmWBt.js";const oe=()=>{var C;const l=W(),s=$(X),r=$(Z),[D,j]=u.useState(!1),[t,m]=u.useState({}),[i,c]=u.useState({}),[L,f]=u.useState(!1),[n,h]=u.useState(!1),w=a=>{switch(a){case 1:return"active";case 0:return"inactive";case-1:return"deleted";default:return"active"}},T=a=>{switch(a){case"active":return 1;case"inactive":return 0;case"deleted":return-1;default:return 1}};if(u.useEffect(()=>{s&&m({firstName:(s==null?void 0:s.firstName)||"",lastName:(s==null?void 0:s.lastName)||"",email:(s==null?void 0:s.email)||"",phone:(s==null?void 0:s.mobile)||(s==null?void 0:s.phone)||"",role:(s==null?void 0:s.role)||"buyer",status:w(s==null?void 0:s.status)})},[s]),!s)return null;const B=()=>{var d,o,N,_;const a={};return(d=t.firstName)!=null&&d.trim()||(a.firstName="First name is required"),(o=t.lastName)!=null&&o.trim()||(a.lastName="Last name is required"),(N=t.email)!=null&&N.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.email)||(a.email="Please enter a valid email address"):a.email="Email is required",(_=t.phone)!=null&&_.trim()||(a.phone="Phone number is required"),c(a),Object.keys(a).length===0},v=()=>{l(re()),j(!1),c({}),f(!1),h(!1)},R=()=>{j(!0),c({})},q=async()=>{if(B())try{h(!0),l(x(!0));const a={...t,status:T(t.status),mobile:t.phone};delete a.phone,await l(P({id:s.id||s._id,userData:a})).unwrap(),l(y({id:Date.now(),type:"user_update",description:`User updated: ${t.firstName} ${t.lastName}`,timestamp:new Date().toISOString(),user:"Admin"})),j(!1),c({}),(s.id||s._id)&&l(M(s.id||s._id)),S(`User "${t.firstName} ${t.lastName}" has been updated successfully!`)}catch(a){console.error("Failed to update user:",a),c({general:a.message||"Failed to update user. Please try again."})}finally{h(!1),l(x(!1))}},O=async()=>{const d=s.status===1?0:1,o=d===1?"activate":"deactivate";if(window.confirm(`Are you sure you want to ${o} this user?`))try{h(!0),l(x(!0)),await l(P({id:s.id||s._id,userData:{status:d}})).unwrap(),l(y({id:Date.now(),type:"user_status_change",description:`User ${w(d)}: ${s.firstName} ${s.lastName}`,timestamp:new Date().toISOString(),user:"Admin"})),(s.id||s._id)&&l(M(s.id||s._id)),S(`User has been ${o}d successfully!`)}catch(N){console.error("Failed to update user status:",N),c({general:N.message||"Failed to update user status. Please try again."})}finally{h(!1),l(x(!1))}},V=async()=>{try{h(!0),l(x(!0)),await l(ce(s.id||s._id)).unwrap(),l(y({id:Date.now(),type:"user_deletion",description:`User deleted: ${s.firstName} ${s.lastName}`,timestamp:new Date().toISOString(),user:"Admin"})),v(),S(`User "${s.firstName} ${s.lastName}" has been deleted successfully!`)}catch(a){console.error("Failed to delete user:",a),c({general:a.message||"Failed to delete user. Please try again."})}finally{h(!1),l(x(!1))}},J=()=>{f(!0)},Y=()=>{f(!1)},b=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),F=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),G=a=>{switch(a){case 1:return e.jsx(A,{className:"status-icon active",title:"Active"});case 0:return e.jsx(I,{className:"status-icon inactive",title:"Inactive"});case-1:return e.jsx(g,{className:"status-icon deleted",title:"Deleted"});default:return e.jsx(I,{className:"status-icon inactive",title:"Unknown"})}},H=a=>{const d={buyer:{color:"#3b82f6",label:"Buyer"},seller:{color:"#10b981",label:"Seller"},admin:{color:"#f59e0b",label:"Admin"}},o=d[a]||d.buyer;return e.jsx("span",{className:"role-badge",style:{backgroundColor:`${o.color}20`,color:o.color},children:o.label})};return e.jsxs("div",{className:"UserDetailModal",children:[e.jsx("div",{className:"UserDetailModal__overlay",onClick:v}),e.jsxs("div",{className:"UserDetailModal__container",children:[e.jsxs("div",{className:"UserDetailModal__header",children:[e.jsxs("div",{className:"header-info",children:[e.jsx("div",{className:"user-avatar",children:s!=null&&s.profileImage?e.jsx("img",{src:z+s.profileImage,alt:(s==null?void 0:s.firstName)||"User"}):e.jsx(K,{})}),e.jsxs("div",{className:"user-basic-info",children:[e.jsxs("h2",{children:[(s==null?void 0:s.firstName)||"Unknown"," ",(s==null?void 0:s.lastName)||"User"]}),e.jsxs("div",{className:"user-badges",children:[H((s==null?void 0:s.role)==="admin"?s==null?void 0:s.role:s==null?void 0:s.activeRole),G(s==null?void 0:s.status),(s==null?void 0:s.verificationStatus)==="verified"&&e.jsx(Q,{className:"verification-icon verified",title:"Verified User"})]})]})]}),e.jsx("button",{className:"close-btn",onClick:v,children:e.jsx(U,{})})]}),e.jsxs("div",{className:"UserDetailModal__content",children:[e.jsxs("div",{className:"info-section",children:[e.jsxs("div",{className:"section-header",children:[e.jsx("h3",{children:"User Information"}),!D&&e.jsxs("button",{className:"btn btn-outline",onClick:R,children:[e.jsx(ee,{}),"Edit User"]})]}),D?e.jsxs("div",{className:"edit-form",children:[i.general&&e.jsxs("div",{className:"error-message general-error",children:[e.jsx(E,{}),i.general]}),e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"First Name *"}),e.jsx("input",{type:"text",value:t.firstName,onChange:a=>{m({...t,firstName:a.target.value}),i.firstName&&c({...i,firstName:""})},className:`form-input ${i.firstName?"error":""}`,disabled:n}),i.firstName&&e.jsx("span",{className:"error-text",children:i.firstName})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Last Name *"}),e.jsx("input",{type:"text",value:t.lastName,onChange:a=>{m({...t,lastName:a.target.value}),i.lastName&&c({...i,lastName:""})},className:`form-input ${i.lastName?"error":""}`,disabled:n}),i.lastName&&e.jsx("span",{className:"error-text",children:i.lastName})]})]}),e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Email *"}),e.jsx("input",{type:"email",value:t.email,onChange:a=>{m({...t,email:a.target.value}),i.email&&c({...i,email:""})},className:`form-input ${i.email?"error":""}`,disabled:n}),i.email&&e.jsx("span",{className:"error-text",children:i.email})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Phone *"}),e.jsx("input",{type:"tel",value:t.phone,onChange:a=>{m({...t,phone:a.target.value}),i.phone&&c({...i,phone:""})},className:`form-input ${i.phone?"error":""}`,disabled:n}),i.phone&&e.jsx("span",{className:"error-text",children:i.phone})]})]}),e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Role"}),e.jsxs("select",{value:t.role,onChange:a=>m({...t,role:a.target.value}),className:"form-select",disabled:n,children:[e.jsx("option",{value:"buyer",children:"Buyer"}),e.jsx("option",{value:"seller",children:"Seller"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Status"}),e.jsxs("select",{value:t.status,onChange:a=>m({...t,status:a.target.value}),className:"form-select",disabled:n,children:[e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"}),e.jsx("option",{value:"deleted",children:"Deleted"})]})]})]}),e.jsxs("div",{className:"form-actions",children:[e.jsx("button",{className:"btn btn-primary",onClick:q,disabled:n||(r==null?void 0:r.userDetail),children:n?e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"spinner"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx(se,{}),"Save Changes"]})}),e.jsx("button",{className:"btn btn-outline",onClick:()=>{j(!1),c({})},disabled:n||(r==null?void 0:r.userDetail),children:"Cancel"})]})]}):e.jsxs("div",{className:"info-grid",children:[e.jsxs("div",{className:"info-item",children:[e.jsx(ae,{className:"info-icon"}),e.jsxs("div",{children:[e.jsx("span",{className:"info-label",children:"Email"}),e.jsx("span",{className:"info-value",children:s.email})]})]}),e.jsxs("div",{className:"info-item",children:[e.jsx(te,{className:"info-icon"}),e.jsxs("div",{children:[e.jsx("span",{className:"info-label",children:"Phone"}),e.jsx("span",{className:"info-value",children:s.phone})]})]}),e.jsxs("div",{className:"info-item",children:[e.jsx(k,{className:"info-icon"}),e.jsxs("div",{children:[e.jsx("span",{className:"info-label",children:"Date Joined"}),e.jsx("span",{className:"info-value",children:b(s.dateJoined)})]})]}),e.jsxs("div",{className:"info-item",children:[e.jsx(k,{className:"info-icon"}),e.jsxs("div",{children:[e.jsx("span",{className:"info-label",children:"Last Login"}),e.jsx("span",{className:"info-value",children:b(s.lastLogin)})]})]})]})]}),(s.role==="admin"?s.role:s.activeRole)==="buyer"&&e.jsxs("div",{className:"stats-section",children:[e.jsx("h3",{className:"pb-5",children:"Purchase Statistics"}),e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx(ie,{className:"stat-icon"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("span",{className:"stat-number",children:s.totalPurchases}),e.jsx("span",{className:"stat-label",children:"Total Purchases"})]})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(le,{className:"stat-icon"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("span",{className:"stat-number",children:F(s.totalSpent)}),e.jsx("span",{className:"stat-label",children:"Total Spent"})]})]})]})]}),e.jsxs("div",{className:"activity-section",children:[e.jsx("h3",{children:"Recent Activity"}),e.jsx("div",{className:"activity-list",children:(C=s.activityHistory)==null?void 0:C.map((a,d)=>e.jsxs("div",{className:"activity-item",children:[e.jsxs("div",{className:"activity-content",children:[e.jsx("span",{className:"activity-description",children:a.action}),e.jsx("span",{className:"activity-date",children:b(a.date)})]}),a.amount&&e.jsx("span",{className:"activity-amount",children:F(a.amount)})]},d))})]}),e.jsx("div",{className:"actions-section",children:e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{className:`btn ${(s==null?void 0:s.status)===1?"btn-warning":"btn-success"}`,onClick:O,disabled:n||(r==null?void 0:r.userDetail)||(s==null?void 0:s.status)===-1,children:n?e.jsx(p,{className:"spinner"}):(s==null?void 0:s.status)===1?e.jsxs(e.Fragment,{children:[e.jsx(ne,{}),"Deactivate User"]}):e.jsxs(e.Fragment,{children:[e.jsx(A,{}),"Activate User"]})}),e.jsxs("button",{className:"btn btn-danger",onClick:J,disabled:n||(r==null?void 0:r.userDetail),children:[e.jsx(g,{}),"Delete User"]})]})}),L&&e.jsx("div",{className:"delete-confirm-overlay",children:e.jsxs("div",{className:"delete-confirm-modal",children:[e.jsxs("div",{className:"delete-confirm-header",children:[e.jsx(E,{className:"warning-icon"}),e.jsx("h3",{children:"Confirm Deletion"})]}),e.jsxs("div",{className:"delete-confirm-content",children:[e.jsxs("p",{children:["Are you sure you want to delete user"," ",e.jsxs("strong",{children:['"',s.firstName," ",s.lastName,'"']}),"?"]}),e.jsx("p",{className:"warning-text",children:"This action cannot be undone. All user data will be permanently removed."})]}),e.jsxs("div",{className:"delete-confirm-actions",children:[e.jsx("button",{className:"btn btn-danger",onClick:()=>{f(!1),V()},disabled:n,children:n?e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"spinner"}),"Deleting..."]}):e.jsxs(e.Fragment,{children:[e.jsx(g,{}),"Yes, Delete User"]})}),e.jsx("button",{className:"btn btn-outline",onClick:Y,disabled:n,children:"Cancel"})]})]})})]})]})]})};export{oe as U};
