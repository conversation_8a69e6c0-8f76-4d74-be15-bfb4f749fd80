import React, { useState, useEffect } from 'react';
import { FaExternalLinkAlt, FaExclamationTriangle, FaShieldAlt } from 'react-icons/fa';
import '../../styles/SimplePDFViewer.css';

const SimplePDFViewer = ({
  fileUrl,
  title = 'PDF Document',
  className = '',
  height = '100%'
}) => {
  const [isAndroid, setIsAndroid] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isBrave, setIsBrave] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [showFallback, setShowFallback] = useState(false);
  const [loadTimeout, setLoadTimeout] = useState(null);

  useEffect(() => {
    // Detect Android and mobile devices
    const userAgent = navigator.userAgent;
    const androidDevice = /Android/i.test(userAgent);
    const mobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) ||
      window.innerWidth <= 768;

    // Detect Brave browser
    const braveDetected = (navigator.brave && navigator.brave.isBrave) ||
      userAgent.includes('Brave') ||
      (window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect);

    setIsAndroid(androidDevice);
    setIsMobile(mobileDevice);
    setIsBrave(braveDetected);

    // Set a timeout to detect if iframe fails to load (especially in Brave)
    const timeout = setTimeout(() => {
      if (!isBlocked && isBrave) {
        console.log('PDF load timeout - likely blocked by Brave');
        setIsBlocked(true);
      }
    }, 5000); // 5 second timeout

    setLoadTimeout(timeout);

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [fileUrl, isBrave, isBlocked]);

  // Enhanced PDF URL parameters for better compatibility
  const getPDFUrl = () => {
    if (!fileUrl) return '';

    // For Brave browser, use minimal parameters to avoid blocking
    if (isBrave) {
      return `${fileUrl}#view=FitH`;
    }

    if (isAndroid) {
      // Android Chrome optimized parameters for inline viewing
      return `${fileUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit&embedded=true`;
    } else if (isMobile) {
      // iOS and other mobile devices
      return `${fileUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-fit`;
    } else {
      // Desktop parameters
      return `${fileUrl}#toolbar=0&navpanes=0&view=FitH`;
    }
  };

  // Handle iframe load error (likely Brave blocking)
  const handleIframeError = () => {
    console.log('PDF iframe blocked or failed to load');
    setIsBlocked(true);
    setShowFallback(true);
    // Clear timeout since we detected an error
    if (loadTimeout) {
      clearTimeout(loadTimeout);
      setLoadTimeout(null);
    }
  };

  // Handle opening PDF in new tab
  const handleOpenInNewTab = () => {
    window.open(fileUrl, '_blank', 'noopener,noreferrer');
  };

  // If showing fallback (Brave blocked), show alternative options
  if (showFallback || isBlocked) {
    return (
      <div className={`simple-pdf-viewer simple-pdf-viewer--fallback ${className}`} style={{ height }}>
        <div className="simple-pdf-viewer__blocked">
          <div className="simple-pdf-viewer__blocked-icon">
            <FaShieldAlt />
          </div>
          <h3>PDF Preview Blocked</h3>
          <p>Your browser has blocked this PDF preview for security reasons.</p>
          <p>You can still view the PDF using the options below:</p>

          <div className="simple-pdf-viewer__actions">
            <button
              className="simple-pdf-viewer__btn simple-pdf-viewer__btn--primary"
              onClick={handleOpenInNewTab}
            >
              <FaExternalLinkAlt /> Open in New Tab
            </button>

            <button
              className="simple-pdf-viewer__btn simple-pdf-viewer__btn--secondary"
              onClick={() => setShowFallback(false)}
            >
              Try Again
            </button>
          </div>

          {isBrave && (
            <div className="simple-pdf-viewer__help">
              <p><strong>To enable PDF previews in Brave:</strong></p>
              <ol>
                <li>Click the Brave Shield icon (🛡️) in the address bar</li>
                <li>Turn off "Block Scripts" for this site</li>
                <li>Refresh the page</li>
              </ol>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Render PDF preview iframe
  return (
    <div className={`simple-pdf-viewer ${className} ${isAndroid ? 'simple-pdf-viewer--android' : ''} ${isBrave ? 'simple-pdf-viewer--brave' : ''}`} style={{ height }}>
      <div className="simple-pdf-viewer__content">
        <iframe
          src={getPDFUrl()}
          className={`simple-pdf-viewer__iframe ${isAndroid ? 'simple-pdf-viewer__iframe--android' : ''}`}
          title={title}
          loading="lazy"
          onError={handleIframeError}
          onLoad={() => {
            console.log('PDF loaded successfully');
            setIsBlocked(false);
            // Clear timeout since PDF loaded successfully
            if (loadTimeout) {
              clearTimeout(loadTimeout);
              setLoadTimeout(null);
            }
          }}
          // Enhanced attributes for compatibility
          data-mobile={isMobile ? 'true' : 'false'}
          data-android={isAndroid ? 'true' : 'false'}
          data-brave={isBrave ? 'true' : 'false'}
          allowFullScreen={false}
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
          style={{
            pointerEvents: 'auto',
            touchAction: 'pan-x pan-y zoom',
            border: 'none',
            width: '100%',
            height: '100%'
          }}
        />

        {/* Quick fallback button for blocked content */}
        <div className="simple-pdf-viewer__quick-actions">
          <button
            className="simple-pdf-viewer__quick-btn"
            onClick={() => setShowFallback(true)}
            title="Having trouble viewing? Click for alternatives"
          >
            <FaExclamationTriangle /> Issues viewing?
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimplePDFViewer;
