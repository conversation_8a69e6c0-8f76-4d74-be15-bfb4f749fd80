/* Simple PDF Viewer Styles */
.simple-pdf-viewer {
  display: flex;
  flex-direction: column;
  background: var(--white);
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  position: relative;
}

/* Brave browser specific styles */
.simple-pdf-viewer--brave {
  /* Enhanced isolation for Brave browser */
  isolation: isolate;
  contain: layout style;
}

/* Fallback interface for blocked PDFs */
.simple-pdf-viewer--fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.simple-pdf-viewer__blocked {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading4);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.simple-pdf-viewer__blocked-icon {
  font-size: 3rem;
  color: #ffc107;
  margin-bottom: var(--heading6);
}

.simple-pdf-viewer__blocked h3 {
  color: var(--text-color);
  margin-bottom: var(--basefont);
  font-size: var(--heading5);
  font-weight: 600;
}

.simple-pdf-viewer__blocked p {
  color: var(--medium-gray);
  margin-bottom: var(--basefont);
  line-height: 1.5;
}

.simple-pdf-viewer__actions {
  display: flex;
  gap: var(--basefont);
  margin: var(--heading6) 0;
  flex-wrap: wrap;
  justify-content: center;
}

.simple-pdf-viewer__btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  min-height: 44px;
  touch-action: manipulation;
}

.simple-pdf-viewer__btn--primary {
  background: var(--primary-color);
  color: var(--white);
}

.simple-pdf-viewer__btn--primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.simple-pdf-viewer__btn--secondary {
  background: var(--white);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.simple-pdf-viewer__btn--secondary:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-1px);
}

.simple-pdf-viewer__help {
  margin-top: var(--heading6);
  padding: var(--basefont);
  background: rgba(255, 193, 7, 0.1);
  border-radius: var(--border-radius);
  border-left: 4px solid #ffc107;
  text-align: left;
  max-width: 400px;
}

.simple-pdf-viewer__help p {
  margin-bottom: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
}

.simple-pdf-viewer__help ol {
  margin: 0;
  padding-left: 20px;
  color: var(--medium-gray);
}

.simple-pdf-viewer__help li {
  margin-bottom: 4px;
  line-height: 1.4;
}

/* Quick actions button */
.simple-pdf-viewer__quick-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.simple-pdf-viewer__quick-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.9);
  color: var(--text-color);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.simple-pdf-viewer__quick-btn:hover {
  background: #ffc107;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Header */

.simple-pdf-viewer .simple-pdf-viewer__title {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.simple-pdf-viewer .simple-pdf-viewer__note {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-style: italic;
}

/* Content */
.simple-pdf-viewer .simple-pdf-viewer__content {
  flex: 1;
  position: relative;
  background: var(--light-gray);
  /* Ensure proper rendering on all devices */
  width: 100%;
  height: 100%;
}

.simple-pdf-viewer .simple-pdf-viewer__iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: var(--white);
  /* Enhanced mobile compatibility */
  -webkit-overflow-scrolling: touch;
  pointer-events: auto;
  touch-action: pan-x pan-y zoom;
  /* Better rendering */
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

/* Android-specific iframe improvements */
.simple-pdf-viewer--android .simple-pdf-viewer__iframe,
.simple-pdf-viewer__iframe--android {
  /* Force proper rendering on Android */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Better Android iframe handling */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* Ensure full viewport usage */
  width: 100% !important;
  height: 100% !important;
  /* Improve touch responsiveness */
  -webkit-touch-callout: none;
  /* Force hardware acceleration */
  will-change: transform;
  /* Better isolation */
  isolation: isolate;
  contain: layout style paint;
  /* Override any pointer restrictions */
  pointer-events: auto !important;
  touch-action: manipulation;
}

/* Enhanced Android container */
.simple-pdf-viewer--android {
  /* Better Android performance */
  contain: layout style;
  will-change: auto;
  /* Ensure proper stacking */
  position: relative;
  z-index: 1;
}

.simple-pdf-viewer--android .simple-pdf-viewer__content {
  /* Remove any Android-specific background */
  background: var(--white);
  /* Ensure proper dimensions */
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.simple-pdf-viewer__filename {
  font-family: 'Courier New', monospace;
  font-size: var(--smallfont);
  color: var(--medium-gray);
  word-break: break-all;
  margin-bottom: var(--heading5) !important;
}

.simple-pdf-viewer__open-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--basefont) var(--heading5);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
  min-width: 160px;
  touch-action: manipulation;
}

.simple-pdf-viewer__open-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-pdf-viewer__title {
    font-size: var(--smallfont);
  }

  .simple-pdf-viewer .simple-pdf-viewer__note {
    font-size: var(--extrasmallfont);
  }

  /* Mobile fallback interface */
  .simple-pdf-viewer__blocked {
    padding: var(--heading5);
  }

  .simple-pdf-viewer__blocked h3 {
    font-size: var(--heading6);
  }

  .simple-pdf-viewer__actions {
    flex-direction: column;
    width: 100%;
  }

  .simple-pdf-viewer__btn {
    width: 100%;
    justify-content: center;
  }

  .simple-pdf-viewer__help {
    margin-top: var(--basefont);
    padding: var(--basefont);
  }

  .simple-pdf-viewer__quick-btn {
    font-size: var(--extrasmallfont);
    padding: 6px 10px;
  }

  /* Mobile-specific iframe handling */
  .simple-pdf-viewer__iframe {
    /* Improve mobile PDF viewing */
    min-height: 400px;
    max-height: 70vh;
    /* Better mobile scroll handling */
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    /* Ensure touch events work */
    pointer-events: auto !important;
    touch-action: pan-x pan-y zoom;
    /* Mobile-specific positioning */
    position: relative;
  }

  .simple-pdf-viewer__content {
    /* Prevent parent container from interfering */
    overflow: visible;
    position: relative;
    height: 100%;
  }
  
  /* Android mobile specific */
  .simple-pdf-viewer--android .simple-pdf-viewer__iframe {
    /* Ensure Android iframe works properly */
    position: relative !important;
    display: block !important;
    opacity: 1 !important;
    /* Force proper dimensions */
    width: 100vw;
    max-width: 100%;
    /* Better Android mobile rendering */
    transform: none;
    -webkit-transform: none;
  }
}

@media (max-width: 480px) {
  .simple-pdf-viewer__iframe {
    min-height: 350px;
    max-height: 60vh;
    /* Ensure iframe fills mobile viewport properly */
    width: 100% !important;
    border: none;
    /* Better mobile touch handling */
    touch-action: manipulation;
  }

  /* Small Android screens */
  .simple-pdf-viewer--android .simple-pdf-viewer__iframe {
    /* Optimize for small Android screens */
    min-height: 300px;
    max-height: 55vh;
    /* Ensure visibility */
    display: block !important;
    visibility: visible !important;
  }

  /* Small screen fallback interface */
  .simple-pdf-viewer__blocked {
    padding: var(--basefont);
  }

  .simple-pdf-viewer__blocked-icon {
    font-size: 2.5rem;
  }

  .simple-pdf-viewer__blocked h3 {
    font-size: var(--basefont);
    margin-bottom: var(--smallfont);
  }

  .simple-pdf-viewer__blocked p {
    font-size: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .simple-pdf-viewer__help {
    font-size: var(--smallfont);
    padding: var(--smallfont);
  }

  .simple-pdf-viewer__help ol {
    padding-left: 16px;
  }

  .simple-pdf-viewer__quick-actions {
    top: 5px;
    right: 5px;
  }
}

/* Force iframe visibility on Android */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .simple-pdf-viewer--android iframe {
    /* Android Chrome specific */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    /* Disable any interference */
    filter: none !important;
    /* Ensure proper sizing */
    min-width: 100% !important;
    min-height: 100% !important;
  }
}
